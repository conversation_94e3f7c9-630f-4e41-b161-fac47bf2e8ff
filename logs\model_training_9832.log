2025-09-16 10:23:59:916173|model_training|9832|12904|INFO|train_model.py:101|==================================================
2025-09-16 10:23:59:917172|model_training|9832|12904|INFO|train_model.py:102|开始模型训练流程 (单本地网强制模式)
2025-09-16 10:23:59:918174|model_training|9832|12904|INFO|train_model.py:103|配置文件: config/config.yaml
2025-09-16 10:23:59:918174|model_training|9832|12904|INFO|train_model.py:104|数据目录: data/raw
2025-09-16 10:23:59:918174|model_training|9832|12904|INFO|train_model.py:105|输出目录: models/trained
2025-09-16 10:23:59:919175|model_training|9832|12904|INFO|train_model.py:106|==================================================
2025-09-16 10:23:59:919175|model_training|9832|12904|INFO|train_model.py:109|步骤 1: 数据加载
2025-09-16 10:23:59:920173|model_training|9832|12904|INFO|train_model.py:132|预测目标月: 202509; 训练月(当前样本月): 202508; 加载历史窗口: ['202506', '202507', '202508']
2025-09-16 10:23:59:920173|model_training|9832|12904|INFO|train_model.py:137|数据加载子步骤: 按需选择历史月份文件并加载
