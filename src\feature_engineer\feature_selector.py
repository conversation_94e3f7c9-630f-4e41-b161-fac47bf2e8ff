"""
特征选择器

负责特征重要性分析和特征选择
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
import yaml
import os
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.feature_selection import RFE, SelectFromModel
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LassoCV
import matplotlib.pyplot as plt
import seaborn as sns

class FeatureSelector:
    """特征选择器类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化特征选择器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 特征选择配置
        self.max_features = self.config.get('feature_engineering', {}).get('max_features', 20)
        self.selection_method = self.config.get('feature_engineering', {}).get('feature_selection_method', 'importance')
        
        # 存储选择结果
        self.selected_features = []
        self.feature_scores = {}
        self.feature_importance = {}
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/config.yaml')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"警告: 无法加载配置文件: {e}")
            return {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置统一日志: 移除旧处理器, 继承根日志格式"""
        logger = logging.getLogger(__name__)
        try:
            from src.utils.logger import TelecomLogFormatter
            for h in list(logger.handlers):
                if isinstance(h, logging.StreamHandler) and not isinstance(getattr(h, 'formatter', None), TelecomLogFormatter):
                    logger.removeHandler(h)
        except Exception:
            pass
        logger.propagate = True
        return logger
    
    def calculate_correlation_matrix(self, df: pd.DataFrame, target_column: str) -> pd.DataFrame:
        """
        计算特征相关性矩阵
        
        Args:
            df: 特征DataFrame
            target_column: 目标列名
            
        Returns:
            相关性矩阵
        """
        # 只计算数值特征的相关性
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        if target_column not in numeric_cols:
            self.logger.warning(f"目标列 {target_column} 不是数值类型")
            return pd.DataFrame()
        
        correlation_matrix = df[numeric_cols].corr()
        
        self.logger.info(f"计算了 {len(numeric_cols)} 个数值特征的相关性矩阵")
        
        return correlation_matrix
    
    def remove_highly_correlated_features(self, df: pd.DataFrame, 
                                        threshold: float = 0.95,
                                        target_column: str = 'lag_amount') -> List[str]:
        """
        移除高度相关的特征
        
        Args:
            df: 特征DataFrame
            threshold: 相关性阈值
            target_column: 目标列名
            
        Returns:
            保留的特征列表
        """
        correlation_matrix = self.calculate_correlation_matrix(df, target_column)
        
        if correlation_matrix.empty:
            return df.columns.tolist()
        
        # 查找高度相关的特征对
        upper_triangle = correlation_matrix.where(
            np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)
        )
        
        # 找到相关性超过阈值的特征
        highly_correlated = [column for column in upper_triangle.columns 
                           if any(upper_triangle[column].abs() > threshold)]
        
        # 保留与目标变量相关性更高的特征
        features_to_remove = []
        for feature in highly_correlated:
            if feature != target_column:
                target_corr = abs(correlation_matrix[target_column][feature])
                
                # 找到与该特征高度相关的其他特征
                highly_corr_with_feature = upper_triangle.index[
                    upper_triangle[feature].abs() > threshold
                ].tolist()
                
                for other_feature in highly_corr_with_feature:
                    if other_feature != target_column:
                        other_target_corr = abs(correlation_matrix[target_column][other_feature])
                        
                        # 保留与目标变量相关性更高的特征
                        if target_corr < other_target_corr and feature not in features_to_remove:
                            features_to_remove.append(feature)
                        elif target_corr > other_target_corr and other_feature not in features_to_remove:
                            features_to_remove.append(other_feature)
        
        # 移除重复
        features_to_remove = list(set(features_to_remove))
        remaining_features = [col for col in df.columns if col not in features_to_remove]
        
        self.logger.info(f"移除了 {len(features_to_remove)} 个高度相关的特征")
        
        return remaining_features
    
    def univariate_feature_selection(self, X: pd.DataFrame, y: pd.Series, 
                                   k: int = None, method: str = 'f_regression') -> List[str]:
        """
        单变量特征选择
        
        Args:
            X: 特征矩阵
            y: 目标变量
            k: 选择的特征数量
            method: 选择方法
            
        Returns:
            选择的特征列表
        """
        if k is None:
            k = min(self.max_features, X.shape[1])
        
        # 选择评分函数
        if method == 'f_regression':
            score_func = f_regression
        elif method == 'mutual_info':
            score_func = mutual_info_regression
        else:
            score_func = f_regression
        
        # 执行特征选择
        selector = SelectKBest(score_func=score_func, k=k)
        selector.fit(X, y)
        
        # 获取选择的特征
        selected_features = X.columns[selector.get_support()].tolist()
        
        # 存储特征分数
        self.feature_scores[method] = dict(zip(X.columns, selector.scores_))
        
        self.logger.info(f"单变量特征选择({method})：选择了 {len(selected_features)} 个特征")
        
        return selected_features
    
    def recursive_feature_elimination(self, X: pd.DataFrame, y: pd.Series, 
                                    n_features: int = None) -> List[str]:
        """
        递归特征消除
        
        Args:
            X: 特征矩阵
            y: 目标变量
            n_features: 目标特征数量
            
        Returns:
            选择的特征列表
        """
        if n_features is None:
            n_features = min(self.max_features, X.shape[1])
        
        # 使用随机森林作为估计器
        estimator = RandomForestRegressor(n_estimators=50, random_state=42)
        
        # 执行递归特征消除
        selector = RFE(estimator=estimator, n_features_to_select=n_features)
        selector.fit(X, y)
        
        # 获取选择的特征
        selected_features = X.columns[selector.get_support()].tolist()
        
        # 存储特征排名
        self.feature_scores['rfe_ranking'] = dict(zip(X.columns, selector.ranking_))
        
        self.logger.info(f"递归特征消除：选择了 {len(selected_features)} 个特征")
        
        return selected_features
    
    def model_based_feature_selection(self, X: pd.DataFrame, y: pd.Series, 
                                    method: str = 'random_forest') -> List[str]:
        """
        基于模型的特征选择
        
        Args:
            X: 特征矩阵
            y: 目标变量
            method: 模型方法
            
        Returns:
            选择的特征列表
        """
        if method == 'random_forest':
            # 使用随机森林的特征重要性
            estimator = RandomForestRegressor(n_estimators=100, random_state=42)
            estimator.fit(X, y)
            
            # 存储特征重要性
            self.feature_importance['random_forest'] = dict(zip(X.columns, estimator.feature_importances_))
            
            # 选择重要性最高的特征
            importances = estimator.feature_importances_
            indices = np.argsort(importances)[::-1]
            
            n_features = min(self.max_features, len(indices))
            selected_indices = indices[:n_features]
            selected_features = X.columns[selected_indices].tolist()
            
        elif method == 'lasso':
            # 使用Lasso回归的特征选择
            estimator = LassoCV(cv=5, random_state=42)
            selector = SelectFromModel(estimator)
            selector.fit(X, y)
            
            selected_features = X.columns[selector.get_support()].tolist()
            
            # 存储Lasso系数
            estimator.fit(X, y)
            self.feature_importance['lasso'] = dict(zip(X.columns, estimator.coef_))
        
        else:
            # 默认使用随机森林
            return self.model_based_feature_selection(X, y, 'random_forest')
        
        self.logger.info(f"基于模型的特征选择({method})：选择了 {len(selected_features)} 个特征")
        
        return selected_features
    
    def comprehensive_feature_selection(self, X: pd.DataFrame, y: pd.Series) -> List[str]:
        """
        综合特征选择
        
        Args:
            X: 特征矩阵
            y: 目标变量
            
        Returns:
            最终选择的特征列表
        """
        self.logger.info("开始综合特征选择")
        
        # 1. 移除高度相关的特征
        remaining_features = self.remove_highly_correlated_features(
            pd.concat([X, y], axis=1), target_column=y.name
        )
        X_filtered = X[remaining_features]
        
        if X_filtered.empty:
            self.logger.error("所有特征都被过滤掉了")
            return []
        
        # 2. 应用多种特征选择方法
        selection_results = {}
        
        # 单变量特征选择
        try:
            selection_results['f_regression'] = self.univariate_feature_selection(
                X_filtered, y, method='f_regression'
            )
        except Exception as e:
            self.logger.warning(f"F回归特征选择失败: {e}")
        
        try:
            selection_results['mutual_info'] = self.univariate_feature_selection(
                X_filtered, y, method='mutual_info'
            )
        except Exception as e:
            self.logger.warning(f"互信息特征选择失败: {e}")
        
        # 递归特征消除
        try:
            selection_results['rfe'] = self.recursive_feature_elimination(X_filtered, y)
        except Exception as e:
            self.logger.warning(f"递归特征消除失败: {e}")
        
        # 基于模型的特征选择
        try:
            selection_results['random_forest'] = self.model_based_feature_selection(
                X_filtered, y, 'random_forest'
            )
        except Exception as e:
            self.logger.warning(f"随机森林特征选择失败: {e}")
        
        # 3. 综合选择结果
        final_features = self._combine_selection_results(selection_results)
        
        self.selected_features = final_features
        
        self.logger.info(f"综合特征选择完成，最终选择 {len(final_features)} 个特征")
        
        return final_features
    
    def _combine_selection_results(self, selection_results: Dict[str, List[str]]) -> List[str]:
        """
        综合多种选择方法的结果
        
        Args:
            selection_results: 各种方法的选择结果
            
        Returns:
            最终特征列表
        """
        if not selection_results:
            return []
        
        # 统计每个特征被选择的次数
        feature_votes = {}
        for method, features in selection_results.items():
            for feature in features:
                feature_votes[feature] = feature_votes.get(feature, 0) + 1
        
        # 按投票数排序
        sorted_features = sorted(feature_votes.items(), key=lambda x: x[1], reverse=True)
        
        # 选择投票数最高的特征
        final_features = []
        for feature, votes in sorted_features:
            if len(final_features) < self.max_features:
                final_features.append(feature)
            else:
                break
        
        # 记录选择统计
        self.logger.info("特征选择投票结果:")
        for feature, votes in sorted_features[:10]:  # 显示前10个
            self.logger.info(f"  {feature}: {votes} 票")
        
        return final_features
    
    def analyze_feature_importance(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Dict[str, float]]:
        """
        分析特征重要性
        
        Args:
            X: 特征矩阵
            y: 目标变量
            
        Returns:
            特征重要性分析结果
        """
        importance_analysis = {}
        
        # 1. 随机森林特征重要性
        try:
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y)
            importance_analysis['random_forest'] = dict(zip(X.columns, rf.feature_importances_))
        except Exception as e:
            self.logger.error(f"随机森林重要性分析失败: {e}")
        
        # 2. 与目标变量的相关性
        try:
            correlations = X.corrwith(y).abs()
            importance_analysis['correlation'] = correlations.to_dict()
        except Exception as e:
            self.logger.error(f"相关性分析失败: {e}")
        
        # 3. 互信息
        try:
            mi_scores = mutual_info_regression(X, y, random_state=42)
            importance_analysis['mutual_info'] = dict(zip(X.columns, mi_scores))
        except Exception as e:
            self.logger.error(f"互信息分析失败: {e}")
        
        return importance_analysis
    
    def plot_feature_importance(self, importance_dict: Dict[str, float], 
                              title: str = "Feature Importance", 
                              top_n: int = 20) -> None:
        """
        绘制特征重要性图
        
        Args:
            importance_dict: 特征重要性字典
            title: 图表标题
            top_n: 显示前N个特征
        """
        if not importance_dict:
            self.logger.warning("没有特征重要性数据可以绘制")
            return
        
        # 排序并取前N个
        sorted_features = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)[:top_n]
        
        features, importances = zip(*sorted_features)
        
        # 创建图表
        plt.figure(figsize=(10, 8))
        y_pos = np.arange(len(features))
        
        plt.barh(y_pos, importances)
        plt.yticks(y_pos, features)
        plt.xlabel('Importance Score')
        plt.title(title)
        plt.gca().invert_yaxis()
        plt.tight_layout()
        
        # 保存图表
        plot_path = os.path.join('logs', f'{title.lower().replace(" ", "_")}.png')
        os.makedirs('logs', exist_ok=True)
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"特征重要性图已保存到: {plot_path}")
    
    def get_feature_selection_report(self) -> Dict:
        """
        获取特征选择报告
        
        Returns:
            特征选择报告
        """
        report = {
            'selected_features': self.selected_features,
            'num_selected_features': len(self.selected_features),
            'feature_scores': self.feature_scores,
            'feature_importance': self.feature_importance,
            'max_features_limit': self.max_features
        }
        
        return report
    
    def save_selected_features(self, filepath: str):
        """
        保存选择的特征
        
        Args:
            filepath: 保存路径
        """
        try:
            feature_data = {
                'selected_features': self.selected_features,
                'feature_scores': self.feature_scores,
                'feature_importance': self.feature_importance
            }
            
            import json
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(feature_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"特征选择结果已保存到: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存特征选择结果失败: {e}")
    
    def load_selected_features(self, filepath: str):
        """
        加载特征选择结果
        
        Args:
            filepath: 文件路径
        """
        try:
            import json
            with open(filepath, 'r', encoding='utf-8') as f:
                feature_data = json.load(f)
            
            self.selected_features = feature_data.get('selected_features', [])
            self.feature_scores = feature_data.get('feature_scores', {})
            self.feature_importance = feature_data.get('feature_importance', {})
            
            self.logger.info(f"特征选择结果已从 {filepath} 加载")
            
        except Exception as e:
            self.logger.error(f"加载特征选择结果失败: {e}")