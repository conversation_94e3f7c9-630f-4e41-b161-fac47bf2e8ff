"""
数据清洗器

负责数据质量检查、异常处理和数据预处理
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import re
from datetime import datetime
from ..utils.base_component import BaseComponent

class DataCleaner(BaseComponent):
    """数据清洗器类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化数据清洗器

        Args:
            config_path: 配置文件路径
        """
        super().__init__(config_path, "DataCleaner")

        # 档位标签映射
        self.gear_label_mapping = {
            0.2: '0.2', 0.4: '0.4', 0.6: '0.6', 0.8: '0.8', 1.0: '1.0'
        }

        # 业务类型映射
        self.business_type_mapping = {
            '0': 'voice',   # 语音
            '1': 'data'     # 数据
        }

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        base_config = super()._get_default_config()
        base_config.update({
            'business_rules': {
                'filter_gear_labels': [0, 1],
                'min_lag_percentage': 0.5,
                'max_lag_percentage': 3.0
            },
            'feature_engineering': {
                'gear_labels': [0.2, 0.4, 0.6, 0.8, 1.0]
            }
        })
        return base_config
    
    def clean_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗数据类型
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        df_cleaned = df.copy()
        
        try:
            # 转换数值字段
            numeric_columns = ['gear_label', 'total_volume', 'trigger_usage', 'lag_amount']
            for col in numeric_columns:
                if col in df_cleaned.columns:
                    df_cleaned[col] = pd.to_numeric(df_cleaned[col], errors='coerce')
            
            # 转换业务类型
            if 'business_type' in df_cleaned.columns:
                df_cleaned['business_type'] = df_cleaned['business_type'].astype(str)
            
            # 转换量本类型
            if 'volume_type' in df_cleaned.columns:
                df_cleaned['volume_type'] = pd.to_numeric(df_cleaned['volume_type'], errors='coerce')
            
            # 处理时间字段
            if 'remind_time' in df_cleaned.columns:
                df_cleaned['remind_time'] = pd.to_datetime(df_cleaned['remind_time'], errors='coerce')
            
            self.logger.info("数据类型转换完成")
            
        except Exception as e:
            self.logger.error(f"数据类型转换失败: {e}")
        
        return df_cleaned
    
    def handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理缺失值
        
        Args:
            df: 数据DataFrame
            
        Returns:
            处理后的DataFrame
        """
        df_cleaned = df.copy()
        
        # 统计缺失值
        missing_stats = df_cleaned.isnull().sum()
        if missing_stats.sum() > 0:
            self.logger.info(f"发现缺失值: {missing_stats[missing_stats > 0].to_dict()}")
        
        # 删除关键字段缺失的行
        key_columns = ['order_id', 'product_instance_id', 'local_network', 'business_type']
        before_count = len(df_cleaned)
        df_cleaned = df_cleaned.dropna(subset=[col for col in key_columns if col in df_cleaned.columns])
        after_count = len(df_cleaned)
        
        if before_count != after_count:
            self.logger.info(f"删除关键字段缺失的行: {before_count - after_count} 行")
        
        # 填充数值字段的缺失值
        numeric_columns = ['total_volume', 'trigger_usage', 'lag_amount']
        for col in numeric_columns:
            if col in df_cleaned.columns:
                median_value = df_cleaned[col].median()
                df_cleaned[col] = df_cleaned[col].fillna(median_value)
        
        return df_cleaned
    
    def standardize_gear_labels(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化档位标签
        
        Args:
            df: 数据DataFrame
            
        Returns:
            标准化后的DataFrame
        """
        if 'gear_label' not in df.columns:
            return df
        
        df_cleaned = df.copy()
        
        def map_gear_label(value):
            """映射档位标签"""
            if pd.isna(value):
                return np.nan
            
            try:
                value = float(value)
                
                # 按照业务规则映射档位标签
                if value > 1:
                    return 1.0
                elif value >= 0.8:
                    return 0.8
                elif value >= 0.6:
                    return 0.6
                elif value >= 0.4:
                    return 0.4
                elif value >= 0.2:
                    return 0.2
                else:
                    return 0.0
                    
            except (ValueError, TypeError):
                return np.nan
        
        df_cleaned['gear_label'] = df_cleaned['gear_label'].apply(map_gear_label)
        
        # 统计档位标签分布
        gear_distribution = df_cleaned['gear_label'].value_counts().sort_index()
        self.logger.info(f"档位标签分布: {gear_distribution.to_dict()}")
        
        return df_cleaned
    
    def filter_business_rules(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        应用业务规则过滤
        
        Args:
            df: 数据DataFrame
            
        Returns:
            过滤后的DataFrame
        """
        df_filtered = df.copy()
        initial_count = len(df_filtered)
        
        # 过滤档位标签为0和1的数据
        filter_labels = self.config['business_rules']['filter_gear_labels']
        if 'gear_label' in df_filtered.columns:
            df_filtered = df_filtered[~df_filtered['gear_label'].isin(filter_labels)]
            self.logger.info(f"过滤档位标签 {filter_labels} 的数据")
        
        # 过滤通用量本类型(=1)
        if 'volume_type' in df_filtered.columns:
            df_filtered = df_filtered[df_filtered['volume_type'] == 1]
            self.logger.info("只保留通用量本类型(=1)的数据")
        
        # 过滤/标记滞后量数据：根据配置 business_rules.filtering_mode 选择 hard/soft
        if 'lag_amount' in df_filtered.columns and 'total_volume' in df_filtered.columns:
            with np.errstate(divide='ignore', invalid='ignore'):
                df_filtered['lag_percentage'] = (df_filtered['lag_amount'] / df_filtered['total_volume']) * 100
            br = self.config.get('business_rules', {}) or {}
            mode = str(br.get('filtering_mode', 'hard')).lower()
            min_lag = float(br.get('min_lag_percentage', 0.5))
            max_lag = float(br.get('max_lag_percentage', 3.0))
            if mode == 'soft':
                ext_min = float(br.get('extended_min_lag_percentage', 0.3))
                ext_max = float(br.get('extended_max_lag_percentage', 4.0))
                core_w = float(br.get('core_weight', 2.0))
                ext_w = float(br.get('extended_weight', 1.0))
                in_core = (df_filtered['lag_percentage'] >= min_lag) & (df_filtered['lag_percentage'] <= max_lag)
                in_ext = (df_filtered['lag_percentage'] >= ext_min) & (df_filtered['lag_percentage'] <= ext_max)
                before = len(df_filtered)
                df_filtered = df_filtered[in_ext]
                after = len(df_filtered)
                dropped = before - after
                if dropped:
                    self.logger.info(f"软过滤: 丢弃扩展区间外样本 {dropped} 行 (ext {ext_min}-{ext_max}%)")
                df_filtered['sample_weight'] = np.where(in_core.loc[df_filtered.index], core_w, ext_w)
                core_cnt = int((df_filtered['sample_weight'] == core_w).sum())
                ext_cnt = after - core_cnt
                self.logger.info(f"软过滤保留: 核心区间 {core_cnt} 行 (w={core_w}), 扩展区间 {ext_cnt} 行 (w={ext_w})")
            else:
                # hard 模式: 仅保留核心区间样本，不设置权重
                in_core = (df_filtered['lag_percentage'] >= min_lag) & (df_filtered['lag_percentage'] <= max_lag)
                before = len(df_filtered)
                df_filtered = df_filtered[in_core]
                after = len(df_filtered)
                dropped = before - after
                if dropped:
                    self.logger.info(f"硬过滤: 丢弃核心区间外样本 {dropped} 行 (core {min_lag}-{max_lag}%)")
        
        final_count = len(df_filtered)
        self.logger.info(f"业务规则过滤: {initial_count} -> {final_count} 行 (过滤率: {(1-final_count/initial_count)*100:.2f}%)")
        
        return df_filtered
    

    
    def deduplicate(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        去除重复数据
        
        Args:
            df: 数据DataFrame
            
        Returns:
            去重后的DataFrame
        """
        initial_count = len(df)
        
        # 基于关键字段去重
        key_columns = ['order_id', 'product_instance_id', 'remind_time']
        existing_columns = [col for col in key_columns if col in df.columns]
        
        if existing_columns:
            df_dedup = df.drop_duplicates(subset=existing_columns, keep='first')
            final_count = len(df_dedup)
            
            if initial_count != final_count:
                self.logger.info(f"去除重复数据: {initial_count - final_count} 行")
            
            return df_dedup
        else:
            self.logger.warning("未找到去重所需的关键字段")
            return df
    
    def clean_data(self, df: pd.DataFrame,
                   apply_business_rules: bool = True,
                   collect_filtered: bool = False) -> Tuple[pd.DataFrame, Dict, Optional[pd.DataFrame]]:
        """
        执行完整的数据清洗流程

        Args:
            df: 原始数据DataFrame
            apply_business_rules: 是否应用业务规则过滤
            collect_filtered: 是否收集被过滤的原始记录并在返回第三项提供

        Returns:
            (清洗后的DataFrame, 清洗报告, 被过滤原始记录DataFrame或None)
        """
        self.logger.info("开始数据清洗流程")

        # 记录初始状态
        initial_count = len(df)
        cleaning_report = {
            'initial_count': initial_count,
            'steps': {}
        }

        # 初始化被过滤记录收集
        filtered_records: List[pd.DataFrame] = []  # 每项为 DataFrame，包含原始列 + filter_reason

        # 1. 数据类型转换（不产生过滤）
        df_cleaned = self.clean_data_types(df)
        cleaning_report['steps']['type_conversion'] = {
            'count': len(df_cleaned),
            'removed': initial_count - len(df_cleaned)
        }

        # 2. 处理缺失值 -> 删除关键字段缺失的行，需要记录
        if collect_filtered:
            key_columns = ['order_id', 'product_instance_id', 'local_network', 'business_type']
            existing_key_cols = [c for c in key_columns if c in df_cleaned.columns]
            if existing_key_cols:
                missing_mask = df_cleaned[existing_key_cols].isnull().any(axis=1)
                if missing_mask.any():
                    missing_df = df_cleaned[missing_mask].copy()
                    missing_df['filter_reason'] = 'missing_required_field'
                    filtered_records.append(missing_df)
        df_cleaned = self.handle_missing_values(df_cleaned)
        cleaning_report['steps']['missing_values'] = {
            'count': len(df_cleaned),
            'removed': cleaning_report['steps']['type_conversion']['count'] - len(df_cleaned)
        }

        # 2.1 按最新确认业务公式重算滞后量: h = trigger_usage - total_volume * gear_label
        if all(c in df_cleaned.columns for c in ['trigger_usage','total_volume','gear_label']):
            try:
                recomputed = df_cleaned['trigger_usage'] - (df_cleaned['total_volume'] * df_cleaned['gear_label'])
                # 统计与原始差异
                if 'lag_amount' in df_cleaned.columns:
                    diff = (df_cleaned['lag_amount'] - recomputed).abs()
                    mismatch_mask = diff > 0.01
                    mismatch_cnt = int(mismatch_mask.sum())
                    if mismatch_cnt > 0:
                        self.logger.warning(f"滞后量与新公式不一致 {mismatch_cnt} 条，已用新公式覆盖")
                    df_cleaned.loc[mismatch_mask, 'lag_amount'] = recomputed[mismatch_mask]
                else:
                    df_cleaned['lag_amount'] = recomputed
                # 丢弃负值样本（视为异常: 触发使用量 < 档位阈值）
                neg_mask = df_cleaned['lag_amount'] < 0
                neg_cnt = int(neg_mask.sum())
                if neg_cnt > 0:
                    self.logger.warning(f"重算后出现 {neg_cnt} 条 lag_amount 为负，已删除")
                    df_cleaned = df_cleaned[~neg_mask]
                cleaning_report['steps']['lag_amount_recomputed'] = {
                    'recomputed': True,
                    'mismatch_overwritten': int(mismatch_cnt) if 'lag_amount' in locals() else 0,
                    'negative_dropped': neg_cnt,
                    'count': len(df_cleaned)
                }
            except Exception as e:
                self.logger.error(f"滞后量重算失败: {e}")

        # 3. 标准化档位标签
        df_cleaned = self.standardize_gear_labels(df_cleaned)

        # 4. 去重 -> 记录重复行
        if collect_filtered:
            key_columns = ['order_id', 'product_instance_id', 'remind_time']
            existing_columns = [c for c in key_columns if c in df_cleaned.columns]
            if existing_columns:
                dup_mask = df_cleaned.duplicated(subset=existing_columns, keep='first')
                if dup_mask.any():
                    dup_df = df_cleaned[dup_mask].copy()
                    dup_df['filter_reason'] = 'duplicate_record'
                    filtered_records.append(dup_df)
        df_cleaned = self.deduplicate(df_cleaned)
        cleaning_report['steps']['deduplication'] = {
            'count': len(df_cleaned),
            'removed': cleaning_report['steps']['missing_values']['count'] - len(df_cleaned)
        }

        # 5. 应用业务规则过滤
        if apply_business_rules:
            if collect_filtered:
                before_business = df_cleaned.copy()
                df_after = self.filter_business_rules(df_cleaned)
                removed_idx = set(before_business.index) - set(df_after.index)
                if removed_idx:
                    removed_df = before_business.loc[list(removed_idx)].copy()
                    # 细分原因
                    if 'gear_label' in before_business.columns:
                        filter_labels = self.config['business_rules']['filter_gear_labels']
                        gear_mask = before_business['gear_label'].isin(filter_labels)
                    else:
                        gear_mask = pd.Series(False, index=before_business.index)
                    if 'volume_type' in before_business.columns:
                        vol_mask = before_business['volume_type'] != 1
                    else:
                        vol_mask = pd.Series(False, index=before_business.index)
                    if all(c in before_business.columns for c in ['lag_amount', 'total_volume']):
                        tmp = before_business.copy()
                        tmp['lag_percentage'] = (tmp['lag_amount'] / tmp['total_volume']) * 100
                        min_lag = self.config['business_rules']['min_lag_percentage']
                        max_lag = self.config['business_rules']['max_lag_percentage']
                        lag_mask = (tmp['lag_percentage'] < min_lag) | (tmp['lag_percentage'] > max_lag)
                    else:
                        lag_mask = pd.Series(False, index=before_business.index)

                    reason_list = []
                    for idx in removed_df.index:
                        r = []
                        if gear_mask.get(idx, False):
                            r.append('filtered_gear_label')
                        if vol_mask.get(idx, False):
                            r.append('non_general_volume_type')
                        if lag_mask.get(idx, False):
                            r.append('lag_percentage_out_of_range')
                        if not r:
                            r.append('business_rule_filtered')
                        reason_list.append(';'.join(r))
                    removed_df['filter_reason'] = reason_list
                    filtered_records.append(removed_df)
                df_cleaned = df_after
            else:
                df_cleaned = self.filter_business_rules(df_cleaned)
            cleaning_report['steps']['business_rules'] = {
                'count': len(df_cleaned),
                'removed': cleaning_report['steps']['deduplication']['count'] - len(df_cleaned)
            }



        # 记录最终结果
        final_count = len(df_cleaned)
        cleaning_report['final_count'] = final_count
        cleaning_report['total_removed'] = initial_count - final_count
        cleaning_report['removal_rate'] = (initial_count - final_count) / initial_count * 100 if initial_count else 0

        self.logger.info(
            f"数据清洗完成: {initial_count} -> {final_count} 行 (清洗率: {cleaning_report['removal_rate']:.2f}%)"
        )

        filtered_df_final: Optional[pd.DataFrame] = None
        if collect_filtered and filtered_records:
            try:
                filtered_df_final = pd.concat(filtered_records, ignore_index=True)
            except Exception:
                filtered_df_final = pd.DataFrame()

        return df_cleaned, cleaning_report, filtered_df_final