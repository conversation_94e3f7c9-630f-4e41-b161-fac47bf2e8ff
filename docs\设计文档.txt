一、设计思路
设计提前量模型和滞后量模型,通过规则+AI智能调整提高过滤率，同时保证提醒不遗漏
g-l<c+u<g+h  g 为档位值 ，l为提前量，c是使用量，u为当前量，h为滞后量 
h通过从策略提醒提取数据训练生成滞后量模型进行预测出来
u通过从批价清单、量本记录提取数据训练生成模型进行预测出来
l通过以下公式计算出来
当u>=h时，l的最大值为u-h
当u<h时，l=0即可

目标保证h和l预测出来的值比3%还小达到收窄目标，为保证预测出来的值不准影响生产，需要做下以下部分规则校验：
1）h大于3%时，以3%的值为准
2）h 小于3%时，需要提供一个风险系数，如h 小于0.5%时（可配置）存在漏发风险，h直接置为0.5%的值
3）L只要在0和3%之间都可以

滞后量h模型：
目前数据分析情况如下：通过一个本地网一个月的工单记录数据分析，每个用户每月平均只有一条数据，可考虑按历史三个月的统计数据进行建模

提前量u模型：
目前数据分析情况如下：
清单语音数据：以一个月一个本地网的语音清单分析
在一个小时内平均话单笔数1.57笔，1笔的70%，2笔的占20%，3笔占7%，4笔占2%，其它约占1% ，可考虑按用户按小时以使用量平均值作为数据建模


二、滞后量模型实现方案：
1、数据采集
1) 从提醒日志表cc_dat_object_sms_log_，工单表cc_dat_object_ceil_关联 提取过去3个月的历史数据（通过工单ID关联）
2) 按以下内容字段提取
工单ID、提醒时间（精确到分 / 秒 ）、产品实例ID、本地网、业务类型（0 语音还是1数据)、量本类型（通用量本为1 ）、档位标签(触发时使用量/总量 的档位值)、总量 （从提醒日志表取）|触发时使用量（从提醒日志表取）、滞后量（总量*档位标签-触发时使用量），流量的要转成M，语音的转换成分钟
----档位标签X=触发时使用量/总量 如1>0.85 >=0.8则为0.8，如>1则为1，
如0.6=<X<0.8,则X为0.6，如0.4=<X<0.6,则X为0.4，如0.2=<X<0.4,则X为0.2，否则为0
为1、为0的数据都可以过滤
3) 根据以上的数据按以下格式输出：
工单ID|提醒时间（精确到分 / 秒 ）|产品实例ID|本地网|业务类型（0 语音还是1数据)、量本类型（通用量本为1 ）|档位标签(触发时使用量/总量 的档位值)|总量 （从提醒日志表取）|触发时使用量（从提醒日志表取）|滞后量（总量*档位标签-触发时使用量）
生成文件名：CC_DAT_AI_TRAIN_本地网_时间（YYYYMM)_序号.txt
4) 现场形成固定脚本
5) 由于一个用户一个月平均才一条数据，实际按每三个月采集一次数据即可

2、模型训练与预测
1) 扫描预训练的原始数据目录文件CC_DAT_AI_TRAIN_本地网_时间（YYYYMM)_序号.txt ，近三个月的数据
2) 可多线程处理以上数据，不同线程处理不同本地网数据，并加载近3个月的数据文件入内存
3) 按用户、本地网、业务类型(0流量，1语音)、量本类型 1、档位标签 计算时间戳最大的那一条数据总量，使用量，滞后量
4) 其它特征再根据实际数据分析情况增加，生成如下数据格式:
用户|年|月|本地网|业务类型(0流量，1语音)|量本类型 1|档位标签|总量|触发时使用量|滞后量(标签列）
|前一个月的总量|前一个月的触发时使用量|前一个月的滞后量|前二个月的总量|前二个月的触发时使用量|前二个月的滞后量
5) 特征加工后的数据文件名如下：
CC_DAT_AI_TRAIN_OUT_本地网_时间（YYYYMM)_序号.txt，数据文件可以50W行一个文件

6) 组装模型数据集，如20%作为测试集，80%数据作为训练集
7) 使用机器算法如随机森林回归算法进行模型训练
8) 训练完成后得到滞后量模型文件
生成文件名：CC_DAT_AI_TRAIN_OUT_本地网_时间（YYYYMM).PKL
9) 每三个月训练一次

3、预测
1) 扫描预训练的原始数据目录文件CC_DAT_AI_TRAIN_本地网_时间（YYYYMM)_序号.txt ，近三个月的数据
2) 按用户、本地网、业务类型(0流量，1语音)、量本类型 1、档位标签 计算每个月内时间戳最大的那一条数据总量，使用量，滞后量
3) 其它特征再根据实际数据分析情况增加，生成如下数据格式
用户|年|月|本地网|业务类型(0流量，1语音)|量本类型 1|档位标签|前一个月的总量|前一个月的触发时使用量|前一个月的滞后量|前二个月的总量|前二个月的触发时使用量|前二个月的滞后量
4) 调用模型训练文件预测得到 该用户下一个月的滞后量
最终生成如下数据格式：
用户|年|月|本地网|业务类型(0流量，1语音)|量本类型 1|档位标签|前一个月的总量|前一个月的触发时使用量|前一个月的滞后量|前二个月的总量|前二个月的触发时使用量|前二个月的滞后量|预测的滞后量
5) 生成文件名：
CC_DAT_AI_PRE_OUT_本地网_时间（YYYYMM)_序号.txt

