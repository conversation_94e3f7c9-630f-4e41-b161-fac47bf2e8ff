"""
日期时间工具模块

提供日期时间处理相关的工具函数
"""

from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
from typing import List, Tuple, Optional, Union
import pandas as pd
import calendar

class DateUtils:
    """日期时间工具类"""

    # 常用日期格式
    DATE_FORMAT = '%Y-%m-%d'
    DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'
    COMPACT_DATE_FORMAT = '%Y%m%d'
    COMPACT_DATETIME_FORMAT = '%Y%m%d_%H%M%S'

    @staticmethod
    def get_current_timestamp(fmt: str = None) -> str:
        """获取当前时间戳字符串
        Args:
            fmt: 可选的自定义时间格式（例如 '%Y%m'），为空则使用默认 COMPACT_DATETIME_FORMAT
        Returns:
            格式化后的时间戳字符串
        说明:
            之前的代码中出现传入 '%Y%m' 的调用，这里向后兼容该用法。
        """
        fmt_pattern = fmt if fmt else DateUtils.COMPACT_DATETIME_FORMAT
        try:
            return datetime.now().strftime(fmt_pattern)
        except Exception:
            # 回退到默认格式
            return datetime.now().strftime(DateUtils.COMPACT_DATETIME_FORMAT)


    @staticmethod
    def shift_year_month_str(ym: str, delta: int) -> str:
        """将YYYYMM字符串按月份偏移返回新的YYYYMM。
        参数:
            ym: 形如 'YYYYMM' 的月份字符串
            delta: 偏移的月数，可为负
        返回:
            偏移后的 'YYYYMM' 字符串
        """
        if not ym or len(ym) != 6 or not str(ym).isdigit():
            raise ValueError("YYYYMM格式非法")
        y = int(ym[:4]); m = int(ym[4:6])
        total = y * 12 + m - 1 + delta
        ny = total // 12
        nm = total % 12 + 1
        return f"{ny}{nm:02d}"

    @staticmethod
    def get_current_date() -> str:
        """
        获取当前日期字符串

        Returns:
            日期字符串
        """
        return datetime.now().strftime(DateUtils.DATE_FORMAT)

    @staticmethod
    def parse_date(date_str: str, format_str: str = None) -> Optional[datetime]:
        """
        解析日期字符串

        Args:
            date_str: 日期字符串
            format_str: 日期格式，如果为None则尝试多种格式

        Returns:
            datetime对象或None
        """
        if format_str:
            try:
                return datetime.strptime(date_str, format_str)
            except ValueError:
                return None

        # 尝试多种常见格式
        formats = [
            '%Y-%m-%d',
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d',
            '%Y/%m/%d %H:%M:%S',
            '%Y%m%d',
            '%Y%m%d%H%M%S',
            '%d/%m/%Y',
            '%d-%m-%Y',
            '%m/%d/%Y',
            '%m-%d-%Y'
        ]

        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        return None

    @staticmethod
    def format_date(dt: Union[datetime, date], format_str: str = None) -> str:
        """
        格式化日期

        Args:
            dt: datetime或date对象
            format_str: 格式字符串

        Returns:
            格式化后的日期字符串
        """
        if format_str is None:
            format_str = DateUtils.DATE_FORMAT

        if isinstance(dt, date) and not isinstance(dt, datetime):
            dt = datetime.combine(dt, datetime.min.time())

        return dt.strftime(format_str)

    @staticmethod
    def add_months(dt: datetime, months: int) -> datetime:
        """
        增加月份

        Args:
            dt: 日期时间
            months: 要增加的月份数（可以为负数）

        Returns:
            新的日期时间
        """
        return dt + relativedelta(months=months)

    @staticmethod
    def add_days(dt: datetime, days: int) -> datetime:
        """
        增加天数

        Args:
            dt: 日期时间
            days: 要增加的天数（可以为负数）

        Returns:
            新的日期时间
        """
        return dt + timedelta(days=days)

    @staticmethod
    def get_month_range(year: int, month: int) -> Tuple[datetime, datetime]:
        """
        获取指定月份的开始和结束日期

        Args:
            year: 年份
            month: 月份

        Returns:
            (月初日期, 月末日期)
        """
        start_date = datetime(year, month, 1)

        # 获取月末日期
        _, last_day = calendar.monthrange(year, month)
        end_date = datetime(year, month, last_day, 23, 59, 59)

        return start_date, end_date

    @staticmethod
    def get_quarter_range(year: int, quarter: int) -> Tuple[datetime, datetime]:
        """
        获取指定季度的开始和结束日期

        Args:
            year: 年份
            quarter: 季度（1-4）

        Returns:
            (季度开始日期, 季度结束日期)
        """
        if quarter not in [1, 2, 3, 4]:
            raise ValueError("季度必须是1-4")

        start_month = (quarter - 1) * 3 + 1
        end_month = quarter * 3

        start_date = datetime(year, start_month, 1)
        _, last_day = calendar.monthrange(year, end_month)
        end_date = datetime(year, end_month, last_day, 23, 59, 59)

        return start_date, end_date

    @staticmethod
    def get_year_range(year: int) -> Tuple[datetime, datetime]:
        """
        获取指定年份的开始和结束日期

        Args:
            year: 年份

        Returns:
            (年初日期, 年末日期)
        """
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31, 23, 59, 59)

        return start_date, end_date

    @staticmethod
    def get_date_list(start_date: datetime, end_date: datetime,
                     freq: str = 'D') -> List[datetime]:
        """
        生成日期序列

        Args:
            start_date: 开始日期
            end_date: 结束日期
            freq: 频率 ('D'=日, 'M'=月, 'Y'=年)

        Returns:
            日期列表
        """
        if freq == 'D':
            delta = timedelta(days=1)
        elif freq == 'M':
            # 月频率需要特殊处理
            dates = []
            current = start_date.replace(day=1)
            while current <= end_date:
                dates.append(current)
                current = DateUtils.add_months(current, 1)
            return dates
        elif freq == 'Y':
            # 年频率
            dates = []
            current = start_date.replace(month=1, day=1)
            while current <= end_date:
                dates.append(current)
                current = current.replace(year=current.year + 1)
            return dates
        else:
            raise ValueError(f"不支持的频率: {freq}")

        dates = []
        current = start_date
        while current <= end_date:
            dates.append(current)
            current += delta

        return dates

    @staticmethod
    def get_month_name(month: int, language: str = 'zh') -> str:
        """
        获取月份名称

        Args:
            month: 月份（1-12）
            language: 语言 ('zh'=中文, 'en'=英文)

        Returns:
            月份名称
        """
        if month < 1 or month > 12:
            raise ValueError("月份必须是1-12")

        if language == 'zh':
            month_names = [
                '一月', '二月', '三月', '四月', '五月', '六月',
                '七月', '八月', '九月', '十月', '十一月', '十二月'
            ]
        else:  # 英文
            month_names = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ]

        return month_names[month - 1]

    @staticmethod
    def calculate_age(birth_date: datetime, reference_date: datetime = None) -> int:
        """
        计算年龄

        Args:
            birth_date: 出生日期
            reference_date: 参考日期，默认为当前日期

        Returns:
            年龄
        """
        if reference_date is None:
            reference_date = datetime.now()

        age = reference_date.year - birth_date.year

        # 检查是否还未过生日
        if (reference_date.month, reference_date.day) < (birth_date.month, birth_date.day):
            age -= 1

        return age

    @staticmethod
    def get_business_days(start_date: datetime, end_date: datetime) -> List[datetime]:
        """
        获取两个日期之间的工作日

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            工作日列表
        """
        business_days = []
        current = start_date

        while current <= end_date:
            # 0=Monday, 6=Sunday
            if current.weekday() < 5:  # Monday to Friday
                business_days.append(current)
            current += timedelta(days=1)

        return business_days

    @staticmethod
    def is_weekend(dt: datetime) -> bool:
        """
        判断是否为周末

        Args:
            dt: 日期时间

        Returns:
            是否为周末
        """
        return dt.weekday() >= 5  # Saturday=5, Sunday=6

    @staticmethod
    def get_days_difference(date1: datetime, date2: datetime) -> int:
        """
        计算两个日期之间的天数差

        Args:
            date1: 日期1
            date2: 日期2

        Returns:
            天数差（date2 - date1）
        """
        return (date2.date() - date1.date()).days

    @staticmethod
    def get_months_difference(date1: datetime, date2: datetime) -> int:
        """
        计算两个日期之间的月数差

        Args:
            date1: 日期1
            date2: 日期2

        Returns:
            月数差（date2 - date1）
        """
        return (date2.year - date1.year) * 12 + (date2.month - date1.month)

    @staticmethod
    def extract_date_features(dt: datetime) -> dict:
        """
        提取日期特征

        Args:
            dt: 日期时间

        Returns:
            日期特征字典
        """
        return {
            'year': dt.year,
            'month': dt.month,
            'day': dt.day,
            'quarter': (dt.month - 1) // 3 + 1,
            'weekday': dt.weekday() + 1,  # 1=Monday, 7=Sunday
            'is_weekend': DateUtils.is_weekend(dt),
            'is_month_start': dt.day == 1,
            'is_month_end': dt.day == calendar.monthrange(dt.year, dt.month)[1],
            'day_of_year': dt.timetuple().tm_yday,
            'week_of_year': dt.isocalendar()[1]
        }

    @staticmethod
    def create_date_range_filter(start_date: str, end_date: str,
                                date_column: str = 'date') -> str:
        """
        创建日期范围过滤条件（用于pandas查询）

        Args:
            start_date: 开始日期字符串
            end_date: 结束日期字符串
            date_column: 日期列名

        Returns:
            过滤条件字符串
        """
        return f"'{start_date}' <= {date_column} <= '{end_date}'"

    @staticmethod
    def convert_to_fiscal_year(dt: datetime, fiscal_start_month: int = 4) -> int:
        """
        转换为财政年度

        Args:
            dt: 日期时间
            fiscal_start_month: 财政年度开始月份（默认4月）

        Returns:
            财政年度
        """
        if dt.month >= fiscal_start_month:
            return dt.year
        else:
            return dt.year - 1

    @staticmethod
    def get_relative_date_ranges(reference_date: datetime = None) -> dict:
        """
        获取相对日期范围（如上月、上季度、去年同期等）

        Args:
            reference_date: 参考日期，默认为当前日期

        Returns:
            日期范围字典
        """
        if reference_date is None:
            reference_date = datetime.now()

        current_year = reference_date.year
        current_month = reference_date.month
        current_quarter = (current_month - 1) // 3 + 1

        ranges = {}

        # 当月
        ranges['current_month'] = DateUtils.get_month_range(current_year, current_month)

        # 上月
        last_month_date = DateUtils.add_months(reference_date, -1)
        ranges['last_month'] = DateUtils.get_month_range(
            last_month_date.year, last_month_date.month
        )

        # 当季度
        ranges['current_quarter'] = DateUtils.get_quarter_range(current_year, current_quarter)

        # 上季度
        if current_quarter == 1:
            ranges['last_quarter'] = DateUtils.get_quarter_range(current_year - 1, 4)
        else:
            ranges['last_quarter'] = DateUtils.get_quarter_range(current_year, current_quarter - 1)

        # 当年
        ranges['current_year'] = DateUtils.get_year_range(current_year)

        # 去年
        ranges['last_year'] = DateUtils.get_year_range(current_year - 1)

        # 去年同月
        ranges['same_month_last_year'] = DateUtils.get_month_range(
            current_year - 1, current_month
        )

        return ranges