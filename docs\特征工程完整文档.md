# 滞后量预测模型 - 特征工程最新实现说明（基于 2025-09-14 日志与源码）

## 📊 概览（当前实际版本与历史设计差异）

最新训练日志显示（本地网 919）：
```
步骤 4: 特征工程
... 时间特征提取完成
... 月度按档位最大时间戳聚合: 原始 48193 行 -> 聚合后 47032 行 (含档位维度)
... 时序特征创建完成，共 47032 条 (缺失历史统一填 0)
数据集 919: 提取了 16 个特征   <-- 训练前特征提取阶段列数
... 开始拟合并转换特征
... 识别到 10 个数值特征, 1 个分类特征
... 业务转换完成
... 识别到 16 个数值特征, 1 个分类特征
... 分类特征编码完成，使用方法: onehot
... 数值特征缩放完成，使用方法: standard
... 创建了 3 个交互特征
特征转换完成，最终特征数: 25
```

与早期文档“动态 49± 特征”设计存在明显差异。当前发布版本（源码与日志对应）特征工程链路更“精简聚焦”：

| 阶段 | 列数/新增 | 说明 |
|------|-----------|------|
| 原始聚合+时序输出 | 16 | 仅基础+前2个月历史（第三个月窗口逻辑暂未实际启用）|
| 业务转换后（含新派生） | +7(约) | lag_percentage / gear_score / historical_lag_stability / lag_change_1m / lag_change_2m / lag_trend / (可能的稳定性重算) |
| 再识别特征类型 | - | 数值特征扩展到 16 个（含新增业务派生）|
| One-Hot 编码 | +1 | 唯一年低基数字段 business_type -> business_type_1（drop first）|
| 数值缩放 | 0 | 仅缩放 16 个数值特征，不改变列数 |
| 交互特征 | +3 | trigger_usage_x_gear_label, prev_1m_total_volume_x_total_volume, prev_1m_trigger_usage_x_trigger_usage |
| 最终训练输入 | 25 | 与日志完全一致 |

因此：本文件更新为“当前真实实现文档”，同时保留“可扩展计划”章节说明未来如何回归/升级到包含第三个月及更多统计特征的版本。

> 结论：当前训练使用 25 列（含目标列 lag_amount）。特征数量减少的主要原因是：
> 1) FeatureExtractor 新接口 `create_training_dataset` 仅输出固定基线列（未集成统计/分类派生函数）。
> 2) 第三个月窗口虽在代码中具备条件分支（prev_3m_*），但默认训练脚本只加载到“训练月+前两个月”，未触发第3月生成。
> 3) 统计、分类、One-Hot 扩展未在当前调用路径中显式执行（统计与分类旧实现函数未合并到新流程）。
> 4) 交互特征保持精简，仅 3 个数值乘积（含 gear_label）。

---

## 📂 原始数据结构

从CC_DAT_AI_TRAIN文件中读取的13个基础字段（包含文件元信息）：

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| order_id | str | 订单ID | "WO123456789" |
| remind_time | str | 提醒时间 | "2025-07-29 02:48:38" |
| product_instance_id | str | 产品实例ID | "PI48425911111" |
| local_network | str | 本地网 | "290" |
| business_type | int | 业务类型 | 0(语音), 1(数据) |
| volume_type | int | 容量类型 | 1 |
| gear_label | float | 档位标签 | 0.2, 0.4, 0.6, 0.8 |
| total_volume | float | 总量 | 21052.0 |
| trigger_usage | float | 触发时使用量 | 12323.0 |
| lag_amount | float | 滞后量（目标变量） | 308.2 |
| source_file | str | 源文件名 | "CC_DAT_AI_TRAIN_290_202507_1.txt" |
| file_local_network | str | 文件本地网 | "290" |
| file_time | str | 文件时间 | "202507" |

## 🔧 当前代码中的特征工程管道

### 1. 输入与窗口策略
训练脚本 `train_model.py` 通过：预测月(P) -> 训练月(T=P-1) -> 加载 {T, T-1, T-2} 三个月原始数据。第三个月历史 (T-3) 暂未加载，因此 `prev_3m_*` 分支虽存在于 `FeatureExtractor.create_time_series_features`，但当前不会触发。

### 2. 特征提取阶段 (FeatureExtractor)
使用的新接口：`extract_all_features` -> `create_training_dataset`，直接输出固定列集合，不再调用旧的统计 / 分类扩展函数。输出列（16）：
```
user, year, month, local_network, business_type, volume_type, gear_label,
total_volume, trigger_usage, lag_amount,
prev_1m_total_volume, prev_1m_trigger_usage, prev_1m_lag_amount,
prev_2m_total_volume, prev_2m_trigger_usage, prev_2m_lag_amount
```
特点：
* 历史缺失统一填 0（日志：“缺失历史统一填 0”）。
* 不包含：day/hour/weekday/is_weekend/year_month、统计变化率、分类衍生、第三个月 prev_3m_*。
* 设计与“原始文档”显著不同——那套更丰富的特征未纳入当前调用链。

### 3. 特征转换阶段 (FeatureTransformer.fit_transform)
调用顺序：识别类型 -> 缺失值填充 -> 业务转换 -> 再识别 -> 编码 -> 数值缩放 -> 交互特征。

#### 3.1 首轮识别（日志：10 数值 + 1 分类）
初始 16 列中：
* 数值：year, month, local_network, volume_type, gear_label, total_volume, trigger_usage, lag_amount, prev_1m_total_volume, prev_1m_trigger_usage
* 分类：business_type（被识别为分类；其余 prev_2m_*、prev_1m_lag_amount、prev_2m_total_volume 等因识别逻辑与 exclude 列策略，需要结合配置；日志仅显示 10 数值，说明 exclude_numeric_scaling 列表或 dtype 判定导致部分列暂未计入，此处以日志为准）

#### 3.2 业务转换新增列（约 6~7）
在 `apply_business_transformations` 中，根据已有列生成：
* lag_percentage
* gear_score
* historical_lag_stability（方差，后续可能重算）
* lag_change_1m
* lag_change_2m
* lag_trend
(第三个月相关特征当前未生成)

#### 3.3 再识别特征类型（日志：16 数值 + 1 分类）
新增业务派生列全部归为数值，使数值总数达到 16。

#### 3.4 分类编码（onehot）
仅 `business_type` 一列 -> drop first 后生成 `business_type_1` (0=voice,1=data => 只保留“1”指示列)。

#### 3.5 数值缩放
对 numeric_features 进行 standard scaler。时间列 year/month 未明确被排除（需观察配置 exclude_numeric_scaling；若存在则不缩放）。日志无排除警告，视为已缩放的集合稳定。

#### 3.6 交互特征（3 个成功）
函数内候选包含 6 对，但日志结果为 “创建了 3 个交互特征”，实际生成：
* trigger_usage_x_gear_label
* prev_1m_total_volume_x_total_volume
* prev_1m_trigger_usage_x_trigger_usage
说明：
* gear_label_x_business_type 被跳过（business_type 已编码为 business_type_1，或原列非纯数值）
* prev_3m_* 相关对不存在（无第三个月列）

#### 3.7 最终列清单（日志回显顺序）
```
user, year, month, local_network, volume_type, gear_label, total_volume,
trigger_usage, lag_amount,
prev_1m_total_volume, prev_1m_trigger_usage, prev_1m_lag_amount,
prev_2m_total_volume, prev_2m_trigger_usage, prev_2m_lag_amount,
lag_percentage, gear_score, historical_lag_stability,
lag_change_1m, lag_change_2m, lag_trend,
business_type_1,
trigger_usage_x_gear_label, prev_1m_total_volume_x_total_volume, prev_1m_trigger_usage_x_trigger_usage
```
共 25 列（含目标 lag_amount）。

---

## 🧪 与旧版本（设计稿）差异对照

| 维度 | 旧文档描述 | 当前真实实现 | 差异原因 | 影响 |
|------|------------|--------------|----------|------|
| 时间基础特征 | year, month, day, hour, weekday, is_weekend, year_month | 仅 year, month | 新接口未保留时间拆分 | 潜在季节/周期信息缺失 |
| 历史窗口 | 1~3月 (可包含 prev_3m_*) | 固定 2 月 (prev_1m / prev_2m) | 训练脚本仅加载 3 个月数据 | 历史趋势深度降低 |
| 统计变化率 | 各月增量/比例 12~18 项 | 全部缺失 | 未调用 `extract_statistical_features` | 模型对变化敏感性降低 |
| 分类派生 | gear_category/business_category/season | 缺失 | 未调用旧分类函数，业务类型直接 one-hot | 分类语义压缩 |
| One-Hot 结果 | 多分类展开后 4~5 列 | 仅 business_type_1 | 仅 1 个分类字段 | 低风险，特征稀疏性下降 |
| 交互特征 | 3~5 (含 3m) | 3 | 无 3m 列/跳过非数值 | 轻微影响非线性表达 |
| 业务扩展 | 含三月趋势/加速度 | 无第三个月派生 | prev_3m_* 缺失 | 趋势稳定性刻画不足 |
| 特征总数 | 49± | 25 | 多阶段函数未融入 | 模型表达力受限 |

---

## 📌 现版本 25 列分组说明

| 分组 | 列 | 说明 |
|------|----|------|
| 标识与基本 | user, local_network, volume_type | user 暂未做基数过滤；local_network 在单本地网训练实际恒定 |
| 时间 | year, month | 最少时间粒度，仅用于账期区分 |
| 档位与核心数值 | gear_label, total_volume, trigger_usage, lag_amount | 业务核心字段，lag_amount 亦作目标列（训练时分离）|
| 历史数值 | prev_1m_total_volume, prev_1m_trigger_usage, prev_1m_lag_amount, prev_2m_total_volume, prev_2m_trigger_usage, prev_2m_lag_amount | 缺失填 0，窗口深度=2 |
| 业务派生 | lag_percentage, gear_score, historical_lag_stability, lag_change_1m, lag_change_2m, lag_trend | 稳定性采用方差；无加速度/3m 变化 |
| 分类编码 | business_type_1 | 单一 one-hot (data 指示) |
| 交互 | trigger_usage_x_gear_label, prev_1m_total_volume_x_total_volume, prev_1m_trigger_usage_x_trigger_usage | 简单乘积放大非线性 |

---

## 🧩 何处可以扩展回“丰富版本”
| 目标 | 建议动作 | 需修改文件 | 复杂度 |
|------|----------|-----------|--------|
| 恢复时间粒度 | 在 `create_training_dataset` 前调用 `extract_time_features` 并合并 day/hour/weekday/is_weekend/year_month | feature_extractor.py | 低 |
| 引入统计变化率 | 在训练脚本调用 `extract_statistical_features` 并追加列 | feature_extractor.py & train_model.py | 中 |
| 引入分类派生 | 新增 `extract_categorical_features` 调用，再进入转换器 | feature_extractor.py | 低 |
| 激活第三个月 | 训练加载四个月数据或修改窗口 + 配置 time_window_months=3 | train_model.py | 中 |
| 高阶趋势 (加速度) | 需 prev_3m_* + 差值计算 | feature_transformer.py | 中 |
| 更多交互 | 基于筛选(如互信息 / SHAP)动态生成 | transformer + 选择模块 | 中-高 |

---

## 🔐 业务规则当前执行情况
| 规则 | 现状 | 说明 |
|------|------|------|
| 档位过滤 0 / 1 | 在 `export_business_features` 中执行（面向业务文件导出） | 训练前未再过滤（需确认是否符合策略） |
| 滞后量百分比 0.5%~3% 约束 | 训练阶段未裁剪，仅导出时日志提示 | 可考虑在清洗阶段硬性截断 |
| 历史缺失处理 | 直接填 0 | 与旧文档“前向填充”不同，需评估是否引入偏差 |

---

## ⚙️ 配置相关点
当前 `config.yaml` 中：
```
feature_engineering:
  time_window_months: 3   # 代码具备三月逻辑，但训练脚本未加载第3历史月 -> 实际等同2
  feature_transformation:
    categorical_encoding: onehot
    max_onehot_categories: 50
    exclude_numeric_scaling: [year, month, day, hour, weekday, is_weekend, year_month]
```
若想使 year/month 避免缩放，请确保它们列在 exclude_numeric_scaling（当前示例中已包含）。

---

## ✅ 当前版本特征流程要点速览
1. 数据聚合：按 (user, local_network, business_type, volume_type, gear_label, year_month) 取该月最新记录。
2. 历史拼接：生成 prev_1m / prev_2m 六列，缺失填 0。
3. 基线列集：16 列进入转换器。
4. 业务派生：增加 6（~7）个趋势与比例指标。
5. 编码 & 缩放：仅 business_type one-hot 后产生 1 指示列；数值列标准化。
6. 交互生成：3 个乘积特征。
7. 最终 25 列送入模型训练（lag_amount 为 y）。

---

## 🚀 升级建议优先级（从收益 / 成本比排序）
| 优先级 | 建议 | 预期收益 |
|--------|------|----------|
| 高 | 恢复统计变化率特征 (usage_rate / change_rate) | 提升趋势敏感度，常对回归显著增益 |
| 高 | 引入第三个月历史 (prev_3m_*) | 更稳健的趋势 + 方差稳定性指标改进 |
| 中 | 增加时间模式 (weekday/season) | 捕捉账期内周期结构 |
| 中 | 分类派生 (gear_category/season) | 支撑非线性分段解释 |
| 中 | 添加加速度 (lag_trend_acceleration) | 强化变化拐点捕捉 |
| 低 | 扩展更多交互 | 需防止维度爆炸，结合特征选择更佳 |

---

## 📌 附：仍可复用的旧文档要素（留档参考）
旧版本描述的统计 / 分类 / 第三个月逻辑在源码中仍留存（函数级能力存在），但未在当前训练主路径被调用。后续若恢复，可直接扩展 `train_model.py` 中“步骤 4”调用链。

---

## 🧾 最终列清单（再次确认）
```
25 列 = 16 基础 + 6 业务派生 + 1 OneHot + 3 交互 (含目标)
目标列: lag_amount
```

---

## 🛡️ 质量与一致性校验建议
| 检查 | 方法 | 期望 |
|------|------|------|
| 列计数回归 | 训练日志解析自动断言 | ==25 |
| 缺失率 | df.isnull().sum() | 0（历史已填 0） |
| 目标范围 | (lag_amount/total_volume)*100 | 统计越界比例 < 5% |
| 稳定性指标 | historical_lag_stability>=0 | 非负 |
| 交互合法性 | 无全 0 或全常数列 | True |

---

## 🧠 FAQ
Q: 为什么只有 1 个分类特征？
A: 训练路径未调用分类派生函数，原始 business_type 直接 one-hot（drop first）后仅保留 1 列。

Q: 如何启用第三个月 prev_3m_* ？
A: 训练阶段需再向前加载 1 个月历史，并保持 `time_window_months>=3`，然后在 `create_training_dataset` 改造或改用 `create_time_series_features` 全量输出。

Q: 能否在不改训练脚本情况下补充统计特征？
A: 可在 `create_training_dataset` 返回前手动合并 `extract_statistical_features` 结果，或在训练脚本“特征提取”后追加一步。

---

## 🔚 总结
当前生产代码使用“精简 25 列”方案完成训练；为提升模型表现，建议逐步引入：统计变化率 -> 第三个月历史 -> 时间/分类语义 -> 加速度与更多交互。本文档已校准日志与源码现状，后续扩展请在提交说明中引用本版本作为基线。

（文档更新日期：2025-09-14）

## 🧾 25 个训练期特征字段逐一说明

说明模板：字段名 | 数据类型 | 来源阶段 | 计算/生成逻辑 | 业务意义 | 注意事项 / 风险

1. user | str | 特征提取(聚合) | product_instance_id 重命名 | 用户/实例唯一标识 | 高基数；当前未编码；不参与缩放（数值模型若需要需做 embedding 或哈希）
2. year | int | 时间解析 | remind_time→year | 账期年份 | 不参与缩放（建议保持原值）
3. month | int | 时间解析 | remind_time→month | 账期月份 | 与 year 组合区分时间窗口
4. local_network | str/int | 原始字段 | 账期文件中本地网编码 | 区域差异控制 | 单本地网训练时为常量；对模型贡献有限
5. volume_type | int | 原始字段 | 透传 | 区分套餐/资源类型 | 若恒定建议剔除避免噪声
6. gear_label | float | 原始字段 | 档位标签 {0.2,0.4,0.6,0.8} | 直接参与核心公式 h=总量×档位−触发使用量 | 值域离散；可派生 gear_score 等
7. total_volume | float | 原始字段 | 透传 | 用户套餐总量基数 | 与触发使用量/滞后量构成核心比例
8. trigger_usage | float | 原始字段 | 透传 | 触发时已使用量 | 反映使用进度；与 total_volume 比形成使用率
9. lag_amount | float | 原始字段 | 透传 | 目标变量（真实滞后量） | 训练时需从特征矩阵分离，防止泄漏
10. prev_1m_total_volume | float | 时序构造 | 上一账期 total_volume；缺失填 0 | 形成环比趋势基线 | 0 代表缺历史，需模型学习“缺省=新用户”语义
11. prev_1m_trigger_usage | float | 时序构造 | 上一账期 trigger_usage | 近月使用惯性 | 受活动/异常波动影响
12. prev_1m_lag_amount | float | 时序构造 | 上一账期 lag_amount | 滞后惯性 | 0 代表缺历史并非真实 0
13. prev_2m_total_volume | float | 时序构造 | 上两账期 total_volume | 更长趋势 | 与 prev_1m_* 构成加速度潜在基础
14. prev_2m_trigger_usage | float | 时序构造 | 上两账期 trigger_usage | 跨期稳定性度量 | 缺失填 0
15. prev_2m_lag_amount | float | 时序构造 | 上两账期 lag_amount | 更稳定滞后模式参考 | 可后续用于方差 & 加速度
16. lag_percentage | float | 业务转换 | lag_amount / total_volume *100 | 滞后占比（核心健康指标） | total_volume=0 时已置 0；预测需截断到0.5%-3%
17. gear_score | int | 业务转换 | gear_label 映射 {0.2:1,...,0.8:4,1.0:5} | 离散档位序数化 | 当前无 1.0 档；若未来出现自动适配
18. historical_lag_stability | float | 业务转换 | 对 (prev_2m_lag_amount, prev_1m_lag_amount, lag_amount) 求方差 | 滞后量稳定性 | 较低=稳定；0/缺失历史会低估波动
19. lag_change_1m | float | 业务转换 | lag_amount - prev_1m_lag_amount | 最近月绝对变化 | 历史缺失时=lag_amount
20. lag_change_2m | float | 业务转换 | prev_1m_lag_amount - prev_2m_lag_amount | 前一阶段变化 | 两段差分可构成加速度
21. lag_trend | int {-1,0,1} | 业务转换 | 两段变化同向>0=>1，同向<0=>-1，否则0 | 简化趋势符号 | 粗粒度，易受噪声影响
22. business_type_1 | int {0/1} | One-Hot | 原 business_type onehot(drop first) | 区分语音/数据（1=data） | 原始列被删除；解释需映射
23. trigger_usage_x_gear_label | float | 交互 | trigger_usage * gear_label | 使用量与档位强度耦合 | 与 h 公式结构相关
24. prev_1m_total_volume_x_total_volume | float | 交互 | prev_1m_total_volume * total_volume | 总量乘积刻画规模惯性 | 大值需缩放防止数值漂移
25. prev_1m_trigger_usage_x_trigger_usage | float | 交互 | prev_1m_trigger_usage * trigger_usage | 使用粘性强度 | 易与单项高度相关

### 目标变量与特征矩阵说明
训练阶段应：
1. y = lag_amount
2. X = 其余 24 列（可考虑是否保留 high-cardinality 的 user 与近常量的 local_network / volume_type）
3. 预测输出再按业务规则裁剪：lag_percentage ∈ [0.5%,3%]（或经模型预测的 lag_amount 转化后裁剪）

### 建议的潜在再加工（未实现）
| 字段 | 建议衍生 | 价值 |
|------|----------|------|
lag_percentage | 分箱(低/中/高) | 稳定树模型分裂，增强可解释性 |
historical_lag_stability | log(1+x) | 缩小长尾，提高线性可分性 |
user | 哈希/频次编码 | 降低高基数噪声，提高泛化 |
trend相关 (lag_change_1m, lag_change_2m) | 加速度=lag_change_1m-lag_change_2m | 捕捉拐点 |
交互乘积 | 比率型替代 (trigger_usage/prev_1m_trigger_usage) | 减少规模放大效应 |

---