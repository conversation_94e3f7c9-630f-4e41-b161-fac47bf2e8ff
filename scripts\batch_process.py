"""
批量调度脚本 (新版)

目的:
 统一按照当前单本地网脚本 (train_model.py / predict_model.py / evaluate_model.py) 最新参数约束, 
 为多本地网批量执行训练 / 预测 / 评估, 提供最小且可靠的并行与报告能力。

"""

from __future__ import annotations

import os
import sys
import argparse
import json
import time
import yaml
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import List, Dict, Any, Optional

project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils import init_logging_system, setup_project_logging, DateUtils, FileUtils  # type: ignore

# ============================= CLI ============================= #

def parse_arguments() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="批量调度训练/预测/评估 (多本地网并行, 统一入口)"
    )
    parser.add_argument('--config', '-c', default='config/config.yaml', help='配置文件路径')
    parser.add_argument('--mode', choices=['train', 'predict', 'evaluate', 'full'], default='full',
                        help='执行模式: train / predict / evaluate / full (依次执行 train->predict->evaluate)')
    parser.add_argument('--latnids', '-n', nargs='+', help='指定本地网列表; 若缺省则自动扫描 raw 目录')
    parser.add_argument('--exclude-latnids', nargs='+', help='需要排除的本地网列表')
    parser.add_argument('--bill-cycle', '--training-target-month', dest='bill_cycle',
                        help='训练用预测目标月 (传给 train_model.py 的 --bill-cycle)')
    parser.add_argument('--prediction-month', help='预测阶段 future 目标月 (传给 predict_model.py)')
    parser.add_argument('--evaluation-month', help='评估阶段真实已发生月 (传给 evaluate_model.py)')
    parser.add_argument('--model-timestamp', help='评估指定模型时间戳 (可选)')
    # 精简版: 移除对配置并发控制的依赖，如需并行可后续扩展独立参数
    parser.add_argument('--max-workers', type=int, help='并行本地网最大数量 (不再读取配置文件)')
    parser.add_argument('--dry-run', action='store_true', help='仅显示计划命令, 不真正执行')
    parser.add_argument('--continue-on-error', action='store_true', help='遇到失败继续后续任务')
    parser.add_argument('--log-level', help='覆盖配置日志等级 (INFO/DEBUG 等)')
    parser.add_argument('--report-prefix', help='批处理报告文件名前缀 (默认 batch_summary)')
    parser.add_argument('--generate-plots', action='store_true', help='评估阶段传递 --generate-plots')
    parser.add_argument('--percentage-plots', action='store_true', help='评估阶段传递 --percentage-plots (需配合 --generate-plots)')
    parser.add_argument('--strict-month-check', action='store_true', default=False,
                        help='评估阶段启用严格三个月文件存在校验 (默认关闭, 单脚本默认开启)')
    parser.add_argument('--output-prefix', help='评估阶段报告文件名前缀 (透传 evaluate_model.py)')
    return parser.parse_args()

# ============================= 配置与日志 ============================= #

def load_config(path: str) -> Dict[str, Any]:
    try:
        with open(path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        print(f"加载配置失败 {path}: {e}")
        sys.exit(1)

def setup_logging(config: Dict[str, Any], override_level: Optional[str]) -> logging.Logger:
    log_cfg = config.get('logging', {}) or {}
    log_dir = log_cfg.get('log_dir') or log_cfg.get('directory') or 'logs'
    # 内部等级映射 (与项目现有 utils 保持一致)
    name_to_level = {'CRITICAL':1,'ERROR':2,'WARN':3,'WARNING':3,'INFO':4,'DVIEW':5,'DEBUG':6,'TRACE':7}
    if override_level:
        console_level = name_to_level.get(override_level.upper(), 4)
    else:
        raw_level = log_cfg.get('level', 4)
        if isinstance(raw_level, int):
            console_level = raw_level
        else:
            console_level = name_to_level.get(str(raw_level).upper(), 4)
    file_level = 6
    init_logging_system(log_dir, console_level, file_level)
    return setup_project_logging('batch_pipeline', log_dir)

# ============================= 工具函数 ============================= #

def discover_latnids(raw_dir: str, logger: logging.Logger) -> List[str]:
    """扫描原始目录下符合 CC_DAT_AI_TRAIN_{latnid}_{YYYYMM}_*.txt 的文件提取 latnid。"""
    if not os.path.isdir(raw_dir):
        logger.error(f"原始数据目录不存在: {raw_dir}")
        return []
    pattern_prefix = 'CC_DAT_AI_TRAIN_'
    latnids: set[str] = set()
    try:
        for p in Path(raw_dir).glob('*.txt'):
            name = p.name
            if not name.startswith(pattern_prefix):
                continue
            # 结构: CC_DAT_AI_TRAIN_{latnid}_{YYYYMM}_{SEQ}.txt
            parts = name.split('_')
            # 期望长度 >= 6 (TRAIN + latnid + yyyymm + seq)
            if len(parts) < 6:
                continue
            # index: 0:CC,1:DAT,2:AI,3:TRAIN,4:{latnid},5:{yyyymm},6:{seq.txt}
            latnid = parts[4]
            if latnid.isdigit():
                latnids.add(latnid)
    except Exception as e:
        logger.error(f"扫描本地网失败: {e}")
    collected = sorted(latnids)
    logger.info(f"自动发现本地网 {len(collected)} 个: {collected}")
    return collected

def build_command(script: str, args: Dict[str, Any]) -> List[str]:
    cmd = [sys.executable, str(project_root / 'scripts' / script)]
    for k, v in args.items():
        if v is None or v is False:
            continue
        flag = f"--{k.replace('_','-')}"
        if v is True:  # bool 开关
            cmd.append(flag)
        else:
            cmd.extend([flag, str(v)])
    return cmd

def run_subprocess(cmd: List[str], logger: logging.Logger, dry_run: bool) -> Dict[str, Any]:
    import subprocess
    start = time.time()
    cmd_str = ' '.join(cmd)
    logger.info(f"执行: {cmd_str}")
    if dry_run:
        return {'success': True, 'returncode': 0, 'stdout': '', 'stderr': '', 'duration_sec': 0.0, 'cmd': cmd_str}
    try:
        completed = subprocess.run(cmd, capture_output=True, text=True)
        duration = time.time() - start
        success = completed.returncode == 0
        if not success:
            logger.error(f"命令失败 rc={completed.returncode}\nSTDERR: {completed.stderr.strip()[:500]}")
        return {
            'success': success,
            'returncode': completed.returncode,
            'stdout': completed.stdout[-4000:],  # 裁剪防止报告过大
            'stderr': completed.stderr[-4000:],
            'duration_sec': round(duration, 3),
            'cmd': cmd_str
        }
    except Exception as e:
        duration = time.time() - start
        logger.error(f"执行异常: {e}")
        return {'success': False, 'returncode': -1, 'stdout': '', 'stderr': str(e), 'duration_sec': round(duration,3), 'cmd': cmd_str}

# ============================= 阶段执行 ============================= #

def execute_phase(phase: str, latnids: List[str], args: argparse.Namespace, config: Dict[str, Any], logger: logging.Logger) -> Dict[str, Any]:
    """通用阶段执行: 根据 phase 构建对应脚本命令并并行运行。

    phase: train | predict | evaluate
    返回: {latnid: result_dict}
    """
    if not latnids:
        return {}
    max_workers = args.max_workers or 1
    try:
        max_workers = max(1, int(max_workers))
    except Exception:
        max_workers = 1
    if max_workers > 1:
        logger.info(f"开始阶段: {phase}  本地网数={len(latnids)}  并行度={max_workers} (配置 processing 已移除, 使用命令行参数)")
    else:
        logger.info(f"开始阶段: {phase}  本地网数={len(latnids)}  串行执行 (max_workers={max_workers})")

    tasks: List[tuple[str, List[str]]] = []
    for ln in latnids:
        if phase == 'train':
            cmd_args = {
                'config': args.config,
                'latnid': ln,
                'bill_cycle': args.bill_cycle
            }
            script = 'train_model.py'
        elif phase == 'predict':
            cmd_args = {
                'config': args.config,
                'latnid': ln,
                'prediction_month': args.prediction_month
            }
            script = 'predict_model.py'
        elif phase == 'evaluate':
            cmd_args = {
                'config': args.config,
                'latnid': ln,
                'evaluation_month': args.evaluation_month,
                'model_timestamp': args.model_timestamp,
                'generate_plots': args.generate_plots,
                'percentage_plots': args.percentage_plots,
                'strict_month_check': args.strict_month_check,
                'log_level': args.log_level,
                'output_prefix': args.output_prefix
            }
            script = 'evaluate_model.py'
        else:
            raise ValueError(f"未知阶段: {phase}")
        cmd = build_command(script, cmd_args)
        tasks.append((ln, cmd))

    results: Dict[str, Any] = {}

    def _runner(item):
        ln, cmd = item
        res = run_subprocess(cmd, logger, args.dry_run)
        res['latnid'] = ln
        res['phase'] = phase
        return ln, res

    if max_workers == 1:
        for t in tasks:
            ln, res = _runner(t)
            results[ln] = res
            if not res['success'] and not args.continue_on_error:
                logger.error("检测到失败且未开启 continue-on-error, 终止剩余任务")
                break
    else:
        with ThreadPoolExecutor(max_workers=max_workers) as pool:
            future_map = {pool.submit(_runner, t): t[0] for t in tasks}
            for fut in as_completed(future_map):
                ln, res = fut.result()
                results[ln] = res
                status = '成功' if res['success'] else '失败'
                logger.info(f"[{phase}] {ln} 完成 -> {status} (耗时 {res.get('duration_sec','?')}s)")
                if not res['success'] and not args.continue_on_error:
                    logger.error("检测到失败且未开启 continue-on-error, 后续任务将跳过")
                    break
    return results

# ============================= 报告 ============================= #

def summarize(all_phase_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    summary: Dict[str, Any] = {'phases': {}, 'overall': {}}
    total_jobs = 0
    total_success = 0
    for phase, res_map in all_phase_results.items():
        phase_jobs = len(res_map)
        phase_success = sum(1 for r in res_map.values() if r.get('success'))
        total_jobs += phase_jobs
        total_success += phase_success
        summary['phases'][phase] = {
            'jobs': phase_jobs,
            'success': phase_success,
            'failed': phase_jobs - phase_success,
            'success_rate': round(phase_success / phase_jobs, 4) if phase_jobs else None
        }
    summary['overall'] = {
        'total_jobs': total_jobs,
        'total_success': total_success,
        'total_failed': total_jobs - total_success,
        'overall_success_rate': round(total_success / total_jobs, 4) if total_jobs else None
    }
    return summary

def save_report(report: Dict[str, Any], prefix: str, logger: logging.Logger) -> str:
    reports_dir = 'reports'
    FileUtils.ensure_dir(reports_dir)
    ts = DateUtils.get_current_timestamp()
    name = f"{prefix or 'batch_summary'}_{ts}.json"
    path = os.path.join(reports_dir, name)
    ok = FileUtils.save_json(report, path)
    if ok:
        logger.info(f"批处理报告已保存 -> {path}")
        return path
    else:
        logger.error("保存批处理报告失败")
        return ''

# ============================= 主流程 ============================= #

def main():  # pragma: no cover
    args = parse_arguments()
    config = load_config(args.config)
    logger = setup_logging(config, args.log_level)

    raw_dir = config.get('data', {}).get('raw_data_dir', 'data/raw')

    # 解析本地网集合
    if args.latnids:
        latnids = list(dict.fromkeys(args.latnids))  # 去重保持顺序
    else:
        latnids = discover_latnids(raw_dir, logger)

    if args.exclude_latnids:
        excl = set(args.exclude_latnids)
        latnids = [l for l in latnids if l not in excl]

    if not latnids:
        logger.error("未发现任何待处理本地网 (为空)")
        sys.exit(1)

    logger.info("=" * 60)
    logger.info(f"批量模式: {args.mode}")
    logger.info(f"目标本地网: {latnids}")
    logger.info(f"dry-run: {args.dry_run}")
    logger.info("=" * 60)

    phase_results: Dict[str, Dict[str, Any]] = {}
    exit_due_to_failure = False

    try:
        if args.mode in ('train', 'full'):
            r = execute_phase('train', latnids, args, config, logger)
            phase_results['train'] = r
            if any(not v.get('success') for v in r.values()) and not args.continue_on_error:
                exit_due_to_failure = True
        if not exit_due_to_failure and args.mode in ('predict', 'full'):
            r = execute_phase('predict', latnids, args, config, logger)
            phase_results['predict'] = r
            if any(not v.get('success') for v in r.values()) and not args.continue_on_error:
                exit_due_to_failure = True
        if not exit_due_to_failure and args.mode in ('evaluate', 'full'):
            r = execute_phase('evaluate', latnids, args, config, logger)
            phase_results['evaluate'] = r
            if any(not v.get('success') for v in r.values()) and not args.continue_on_error:
                exit_due_to_failure = True
    except KeyboardInterrupt:
        logger.warning("收到中断信号, 终止执行")
        sys.exit(130)
    except Exception as e:
        logger.error(f"批处理发生未捕获异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)

    # 汇总 / 报告
    summary = summarize(phase_results)
    report_payload = {
        'args': vars(args),
        'summary': summary,
        'details': phase_results
    }
    save_report(report_payload, args.report_prefix or 'batch_summary', logger)

    overall_success = summary['overall'].get('total_failed', 0) == 0
    if overall_success or args.dry_run:
        logger.info("批量执行完成: 全部成功")
        sys.exit(0)
    else:
        logger.error("批量执行结束: 存在失败任务")
        sys.exit(1)


if __name__ == '__main__':  # pragma: no cover
    main()
