"""
训练流水线

封装完整的模型训练流程，包括数据加载、清洗、特征工程、模型训练等步骤
"""

import os
import sys
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
import joblib

from ..utils.base_component import BaseComponent
from ..utils import DateUtils, FileUtils
from ..data_processor import DataLoader, DataCleaner, DataValidator
from ..feature_engineer import FeatureExtractor, FeatureTransformer
from ..model import ModelTrainer


class TrainingPipeline(BaseComponent):
    """
    训练流水线类
    
    封装完整的模型训练流程
    """
    
    def __init__(self, config_path: str = None):
        """
        初始化训练流水线
        
        Args:
            config_path: 配置文件路径
        """
        super().__init__(config_path, "TrainingPipeline")
        
        # 初始化各个组件
        self.data_loader = DataLoader(config_path)
        self.data_cleaner = DataCleaner(config_path)
        self.data_validator = DataValidator(config_path)
        self.feature_extractor = FeatureExtractor(config_path)
        self.feature_transformer = FeatureTransformer(config_path)
        self.model_trainer = ModelTrainer(config_path)
        
        # 流水线状态
        self.pipeline_results = {}
        self.current_step = ""
    
    def validate_inputs(self, local_network: str, prediction_month: str) -> Tuple[str, List[str]]:
        """
        验证输入参数并计算所需的历史月份
        
        Args:
            local_network: 本地网标识
            prediction_month: 预测目标月份 (YYYYMM)
            
        Returns:
            (training_month, required_months)
        """
        self.current_step = "输入验证"
        self.logger.info(f"开始{self.current_step}")
        
        # 验证预测月份格式
        if not (len(prediction_month) == 6 and prediction_month.isdigit()):
            raise ValueError(f"账期格式错误，应为YYYYMM: {prediction_month}")
        
        # 计算训练月份和所需历史月份
        training_month = DateUtils.shift_year_month_str(prediction_month, -1)
        hist_prev1 = DateUtils.shift_year_month_str(training_month, -1)
        hist_prev2 = DateUtils.shift_year_month_str(training_month, -2)
        required_months = [training_month, hist_prev1, hist_prev2]
        
        self.logger.info(
            f"预测目标月: {prediction_month}; 训练月: {training_month}; "
            f"所需历史窗口: {required_months}"
        )
        
        return training_month, required_months
    
    def load_data(self, local_network: str, required_months: List[str]) -> Dict[str, pd.DataFrame]:
        """
        加载训练数据
        
        Args:
            local_network: 本地网标识
            required_months: 所需的月份列表
            
        Returns:
            加载的数据字典
        """
        self.current_step = "数据加载"
        self.logger.info(f"开始{self.current_step}")
        
        # 选择和加载文件
        selected_files, missing = self.data_loader.select_files_for_months(
            local_network, set(required_months)
        )
        
        if missing:
            raise ValueError(f"本地网 {local_network} 缺少所需月份文件: {missing}")
        
        if not selected_files:
            raise ValueError(f"未找到本地网 {local_network} 对应月份的任何文件")
        
        data_dict = self.data_loader.load_specific_files_grouped(local_network, selected_files)
        
        if not data_dict:
            raise ValueError("文件加载结果为空")
        
        # 验证数据完整性
        df_total = next(iter(data_dict.values()))
        months_present = set(df_total['file_time'].astype(str).unique()) \
            if not df_total.empty and 'file_time' in df_total.columns else set()
        
        if not set(required_months).issubset(months_present):
            raise ValueError(
                f"存在月份文件空/无有效数据。需要: {required_months}; 实际: {sorted(months_present)}"
            )
        
        self.logger.info(f"数据加载完成: 行数 {len(df_total)}; 覆盖月份 {sorted(months_present)}")
        return data_dict
    
    def clean_and_validate_data(self, data_dict: Dict[str, pd.DataFrame]) -> Tuple[Dict[str, pd.DataFrame], Dict[str, pd.DataFrame]]:
        """
        清洗和验证数据
        
        Args:
            data_dict: 原始数据字典
            
        Returns:
            (cleaned_data, filtered_data)
        """
        self.current_step = "数据清洗和验证"
        self.logger.info(f"开始{self.current_step}")
        
        cleaned_data = {}
        filtered_data_by_net = {}
        
        # 数据清洗
        for key, df in data_dict.items():
            cleaned_df, cleaning_report, filtered_df = self.data_cleaner.clean_data(
                df, collect_filtered=True
            )
            
            if not cleaned_df.empty:
                cleaned_data[key] = cleaned_df
                self.logger.info(f"数据集 {key}: 清洗前 {len(df)} 行，清洗后 {len(cleaned_df)} 行")
            else:
                self.logger.warning(f"数据集 {key} 清洗后为空，跳过")
            
            if filtered_df is not None and not filtered_df.empty:
                filtered_data_by_net[key] = filtered_df
                self.logger.info(f"数据集 {key}: 记录过滤行 {len(filtered_df)} 行")
        
        if not cleaned_data:
            raise ValueError("所有数据集清洗后都为空")
        
        # 数据验证
        for key, df in cleaned_data.items():
            validation_result = self.data_validator.validate_all(df)
            if not validation_result['overall_valid']:
                self.logger.warning(f"数据集 {key} 验证失败: {validation_result['validation_results']}")
            
            # 收集验证阶段无效行
            invalid_rows = self.data_validator.identify_invalid_rows(df)
            if invalid_rows is not None and not invalid_rows.empty:
                if key not in filtered_data_by_net:
                    filtered_data_by_net[key] = invalid_rows
                else:
                    # 合并并去重
                    combined = pd.concat([filtered_data_by_net[key], invalid_rows], ignore_index=True)
                    key_cols = [c for c in ['order_id', 'product_instance_id', 'remind_time'] 
                               if c in combined.columns]
                    if key_cols:
                        combined = combined.drop_duplicates(subset=key_cols + ['filter_reason'], keep='first')
                    filtered_data_by_net[key] = combined
                
                self.logger.info(f"数据集 {key}: 验证阶段新增无效标记 {len(invalid_rows)} 行")
        
        return cleaned_data, filtered_data_by_net
    
    def extract_and_transform_features(self, cleaned_data: Dict[str, pd.DataFrame], 
                                     training_month: str) -> Tuple[Dict[str, pd.DataFrame], Dict[str, pd.DataFrame]]:
        """
        特征提取和转换
        
        Args:
            cleaned_data: 清洗后的数据
            training_month: 训练月份
            
        Returns:
            (transformed_data, raw_feature_data)
        """
        self.current_step = "特征工程"
        self.logger.info(f"开始{self.current_step}")
        
        # 特征提取
        feature_data = {}
        for key, df in cleaned_data.items():
            features_df = self.feature_extractor.extract_all_features(df, training_month=training_month)
            if not features_df.empty:
                feature_data[key] = features_df
                self.logger.info(f"数据集 {key}: 提取了 {len(features_df.columns)} 个特征")
        
        # 保留原始特征数据
        raw_feature_data = feature_data.copy()
        
        # 特征转换
        transformed_data = {}
        for key, df in feature_data.items():
            feature_transformer = FeatureTransformer(self.config.get('_config_path'))
            transformed_df = feature_transformer.fit_transform(df)
            transformed_data[key] = transformed_df
            self.logger.info(f"数据集 {key}: 特征转换完成 (列数: {len(transformed_df.columns)})")
        
        return transformed_data, raw_feature_data

    def save_features(self, raw_feature_data: Dict[str, pd.DataFrame], training_month: str):
        """
        保存特征工程结果

        Args:
            raw_feature_data: 原始特征数据
            training_month: 训练月份
        """
        features_dir = self.get_config_value('data.processed_data_dir', 'data/processed')
        self.ensure_directory(features_dir)

        for key, df in raw_feature_data.items():
            if df is None or df.empty:
                self.logger.warning(f"本地网 {key} 特征数据为空，跳过保存")
                continue

            try:
                if hasattr(self.feature_extractor, 'export_business_features'):
                    self.logger.info("调用 export_business_features 生成业务标准特征文件")
                    biz_df = self.feature_extractor.export_business_features(df, ensure_raw=True)
                else:
                    self.logger.warning("FeatureExtractor 缺少 export_business_features 方法，使用原始特征")
                    biz_df = df

                self.feature_extractor.split_large_file(biz_df, key, features_dir, training_month)
                self.logger.info(f"本地网 {key} 的业务标准特征数据已保存 -> 目录: {features_dir}")
            except Exception as e:
                self.handle_error(e, f"本地网 {key} 特征文件保存失败", raise_error=False)

    def train_model(self, transformed_data: Dict[str, pd.DataFrame],
                   local_network: str, training_month: str) -> Dict[str, Any]:
        """
        训练模型

        Args:
            transformed_data: 转换后的特征数据
            local_network: 本地网标识
            training_month: 训练月份

        Returns:
            训练结果
        """
        self.current_step = "模型训练"
        self.logger.info(f"开始{self.current_step}")

        # 训练配置
        training_config = {
            'algorithm': self.get_config_value('model.algorithm'),
            'enable_parallel': False
        }

        # 单本地网数据字典
        local_network_data = {local_network: transformed_data[local_network]}

        try:
            results = self.model_trainer.batch_train_by_local_network(
                local_network_data, training_timestamp=training_month, **training_config
            )
        except TypeError:
            # 兼容旧版本
            self.logger.warning("ModelTrainer 未接受 training_timestamp 参数，使用当前时间命名")
            results = self.model_trainer.batch_train_by_local_network(
                local_network_data, **training_config
            )

        return results

    def evaluate_feature_importance(self, results: Dict[str, Any]):
        """
        评估特征重要性

        Args:
            results: 训练结果
        """
        self.current_step = "特征重要性评估"
        self.logger.info(f"开始{self.current_step}")

        def _unwrap_model(obj):
            if isinstance(obj, dict) and 'base_model' in obj:
                return obj.get('base_model')
            return obj

        for local_network, res in results.items():
            try:
                if not res or not res.get('success', False):
                    continue

                model_path = res.get('model_path')
                feature_names = list(res.get('feature_names', []) or [])

                if not model_path or not os.path.exists(model_path):
                    self.logger.warning(f"本地网 {local_network}: 模型文件缺失，跳过特征重要性评估")
                    continue

                try:
                    loaded = joblib.load(model_path)
                except Exception as e:
                    self.logger.error(f"本地网 {local_network}: 加载模型失败: {e}")
                    continue

                model = _unwrap_model(loaded)
                importances = self._extract_feature_importance(model, feature_names)

                if importances is not None:
                    self._log_feature_importance(local_network, feature_names, importances)
                else:
                    self.logger.warning(f"本地网 {local_network}: 无法获取特征重要性")

            except Exception as e:
                self.handle_error(e, f"本地网 {local_network} 特征重要性评估异常", raise_error=False)

    def _extract_feature_importance(self, model, feature_names: List[str]) -> Optional[np.ndarray]:
        """提取模型特征重要性"""
        try:
            if hasattr(model, 'feature_importances_') and getattr(model, 'feature_importances_') is not None:
                return np.asarray(model.feature_importances_, dtype=float)
            elif hasattr(model, 'coef_') and getattr(model, 'coef_') is not None:
                return np.abs(np.ravel(model.coef_)).astype(float)
            elif hasattr(model, 'get_booster'):
                # XGBoost
                booster = model.get_booster()
                score_dict = booster.get_score(importance_type='gain')
                return np.array([score_dict.get(f"f{i}", 0.0) for i in range(len(feature_names))], dtype=float)
        except Exception:
            pass
        return None

    def _log_feature_importance(self, local_network: str, feature_names: List[str], importances: np.ndarray):
        """记录特征重要性"""
        if len(feature_names) != len(importances):
            min_len = min(len(feature_names), len(importances))
            feature_names = feature_names[:min_len]
            importances = importances[:min_len]

        if not feature_names:
            feature_names = [f"f{i}" for i in range(len(importances))]

        order = np.argsort(importances)[::-1]
        total = float(importances.sum()) if importances.size else 0.0

        self.logger.info("-" * 50)
        self.logger.info(f"本地网 {local_network} 特征重要性排行榜（共{len(importances)}列）:")

        for rank, idx in enumerate(order, 1):
            name = str(feature_names[idx]) if idx < len(feature_names) else f"f{idx}"
            score = float(importances[idx])
            if total > 0 and np.isfinite(total):
                pct = score / total * 100.0
                self.logger.info(f"  {rank:>2d}. {name:<40} 重要性: {score:.6f} ({pct:.2f}%)")
            else:
                self.logger.info(f"  {rank:>2d}. {name:<40} 重要性: {score:.6f}")

        self.logger.info("-" * 50)

    def run_training_pipeline(self, local_network: str, prediction_month: str) -> Dict[str, Any]:
        """
        运行完整的训练流水线

        Args:
            local_network: 本地网标识
            prediction_month: 预测目标月份

        Returns:
            训练结果
        """
        try:
            self.logger.info("=" * 50)
            self.logger.info(f"开始模型训练流水线 - 本地网: {local_network}")
            self.logger.info("=" * 50)

            # 1. 输入验证
            training_month, required_months = self.validate_inputs(local_network, prediction_month)

            # 2. 数据加载
            data_dict = self.load_data(local_network, required_months)

            # 3. 数据清洗和验证
            cleaned_data, filtered_data = self.clean_and_validate_data(data_dict)

            # 4. 特征工程
            transformed_data, raw_feature_data = self.extract_and_transform_features(
                cleaned_data, training_month
            )

            # 5. 保存特征
            self.save_features(raw_feature_data, training_month)

            # 6. 模型训练
            results = self.train_model(transformed_data, local_network, training_month)

            # 7. 特征重要性评估
            self.evaluate_feature_importance(results)

            # 8. 结果汇总
            self._summarize_results(results)

            self.logger.info("=" * 50)
            self.logger.info("训练流水线完成")
            self.logger.info("=" * 50)

            return results

        except Exception as e:
            self.handle_error(e, f"训练流水线执行失败 - 步骤: {self.current_step}")

    def _summarize_results(self, results: Dict[str, Any]):
        """汇总训练结果"""
        successful_models = 0
        failed_models = 0

        for local_network, result in results.items():
            if result.get('success', False):
                successful_models += 1
                self.logger.info(f"本地网 {local_network}: 训练成功")
                self.logger.info(f"  - 模型文件: {result.get('model_path', 'N/A')}")

                # 获取测试指标
                test_metrics = result.get('test_metrics', {})
                if test_metrics and 'rmse' in test_metrics:
                    self.logger.info(f"  - 测试RMSE: {test_metrics['rmse']:.4f}")
                if test_metrics and 'r2' in test_metrics:
                    self.logger.info(f"  - R²得分: {test_metrics['r2']:.4f}")

                # 获取交叉验证结果
                cv_results = result.get('cv_results', {})
                if cv_results and 'cv_rmse_mean' in cv_results:
                    self.logger.info(f"  - CV RMSE均值: {cv_results['cv_rmse_mean']:.4f}")
            else:
                failed_models += 1
                self.logger.error(f"本地网 {local_network}: 训练失败 - {result.get('error', 'Unknown error')}")

        self.logger.info("训练完成汇总:")
        self.logger.info(f"成功训练模型: {successful_models}")
        self.logger.info(f"训练失败模型: {failed_models}")
        self.logger.info(f"总模型数: {len(results)}")

        if successful_models > 0:
            self.logger.info(f"模型保存目录: {self.get_config_value('model.trained_models_dir')}")
            self.logger.info(f"元数据保存目录: {self.get_config_value('model.metadata_dir')}")
