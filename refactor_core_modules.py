#!/usr/bin/env python3
"""
核心模块重构脚本

批量重构data_processor、model、feature_engineer模块，让它们继承BaseComponent
"""

import os
import re
from pathlib import Path


def refactor_class_to_inherit_base_component(file_path: str, class_name: str):
    """
    重构类继承BaseComponent
    
    Args:
        file_path: 文件路径
        class_name: 类名
    """
    print(f"重构文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加BaseComponent导入
    if 'from ..utils.base_component import BaseComponent' not in content:
        # 找到合适的位置插入导入
        import_pattern = r'(from \.\..* import.*\n)'
        if re.search(import_pattern, content):
            content = re.sub(
                import_pattern,
                r'\1from ..utils.base_component import BaseComponent\n',
                content,
                count=1
            )
        else:
            # 如果没有相对导入，在import语句后添加
            content = re.sub(
                r'(import .*\n)',
                r'\1from ..utils.base_component import BaseComponent\n',
                content,
                count=1
            )
    
    # 修改类定义继承BaseComponent
    class_pattern = rf'class {class_name}:'
    if re.search(class_pattern, content):
        content = re.sub(
            class_pattern,
            f'class {class_name}(BaseComponent):',
            content
        )
    
    # 修改__init__方法调用super().__init__
    init_pattern = rf'(class {class_name}\(BaseComponent\):.*?def __init__\(self, config_path: str = None\):.*?""".*?""")'
    match = re.search(init_pattern, content, re.DOTALL)
    if match:
        # 替换初始化逻辑
        old_init = match.group(1)
        new_init = old_init + f'\n        super().__init__(config_path, "{class_name}")'
        content = content.replace(old_init, new_init)
    
    # 移除重复的_load_config和_setup_logger方法
    content = re.sub(
        r'\n    def _load_config\(self.*?\n        return.*?\n',
        '',
        content,
        flags=re.DOTALL
    )
    
    content = re.sub(
        r'\n    def _setup_logger\(self.*?\n        return.*?\n',
        '',
        content,
        flags=re.DOTALL
    )
    
    # 保存修改后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"完成重构: {file_path}")


def main():
    """主函数"""
    # 定义需要重构的文件和类
    refactor_targets = [
        ('src/data_processor/data_cleaner.py', 'DataCleaner'),
        ('src/data_processor/data_validator.py', 'DataValidator'),
        ('src/feature_engineer/feature_extractor.py', 'FeatureExtractor'),
        ('src/feature_engineer/feature_transformer.py', 'FeatureTransformer'),
        ('src/feature_engineer/feature_selector.py', 'FeatureSelector'),
        ('src/model/trainer.py', 'ModelTrainer'),
        ('src/model/predictor.py', 'ModelPredictor'),
        ('src/model/evaluator.py', 'ModelEvaluator'),
    ]
    
    for file_path, class_name in refactor_targets:
        if os.path.exists(file_path):
            try:
                refactor_class_to_inherit_base_component(file_path, class_name)
            except Exception as e:
                print(f"重构失败 {file_path}: {e}")
        else:
            print(f"文件不存在: {file_path}")


if __name__ == "__main__":
    main()
