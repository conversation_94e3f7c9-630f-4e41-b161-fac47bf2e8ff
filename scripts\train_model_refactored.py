"""
重构后的模型训练脚本

使用TrainingPipeline类封装训练流程，简化脚本逻辑
"""

import os
import sys
import argparse
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.pipelines.training_pipeline import TrainingPipeline
from src.utils import init_logging_system, setup_project_logging, get_config


class TrainingScriptBase:
    """训练脚本基类"""
    
    def __init__(self):
        self.args = None
        self.config = None
        self.logger = None
        self.pipeline = None
    
    def parse_arguments(self):
        """解析命令行参数"""
        parser = argparse.ArgumentParser(description='训练滞后量预测模型 (重构版)')
        
        parser.add_argument('--config', '-c', type=str,
                           default='config/config.yaml',
                           help='配置文件路径')
        
        parser.add_argument('--latnid', '--local-network', '-n', type=str,
                           required=True,
                           help='必须指定本地网（一次仅训练该本地网模型）')
        
        parser.add_argument('--bill-cycle', '--billing-month', '--target-month', '-m', type=str,
                           required=False,
                           help='要训练的模型账期 (YYYYMM)。若不提供，默认为当前系统月份。')
        
        self.args = parser.parse_args()
        return self.args
    
    def setup_logging(self):
        """设置日志系统"""
        config_manager = get_config()
        
        # 如果提供了配置文件路径，重新加载配置
        if self.args.config != 'config/config.yaml':
            config_manager.reload()
        
        log_dir = config_manager.get('logging.log_dir', 'logs')
        config_level = config_manager.get('logging.level', 4)  # 默认INFO
        console_level = config_level
        file_level = 6  # 文件使用DEBUG级别
        
        init_logging_system(log_dir, console_level, file_level)
        self.logger = setup_project_logging('model_training', log_dir)
        
        return self.logger
    
    def validate_config(self):
        """验证配置"""
        config_manager = get_config()
        required_sections = ['data', 'model', 'feature_engineering']
        
        for section in required_sections:
            if not config_manager.get(section):
                self.logger.error(f"配置文件缺少必需的部分: {section}")
                return False
        
        # 验证数据目录
        data_dir = config_manager.get('data.raw_data_dir')
        if not os.path.exists(data_dir):
            self.logger.error(f"数据目录不存在: {data_dir}")
            return False
        
        return True
    
    def initialize_pipeline(self):
        """初始化训练流水线"""
        self.pipeline = TrainingPipeline(self.args.config)
        return self.pipeline
    
    def run_training(self):
        """运行训练流程"""
        try:
            # 确定预测月份
            if self.args.bill_cycle:
                prediction_month = self.args.bill_cycle
            else:
                prediction_month = datetime.now().strftime('%Y%m')
            
            self.logger.info("=" * 50)
            self.logger.info("开始模型训练流程 (重构版)")
            self.logger.info(f"配置文件: {self.args.config}")
            self.logger.info(f"本地网: {self.args.latnid}")
            self.logger.info(f"预测目标月: {prediction_month}")
            self.logger.info("=" * 50)
            
            # 运行训练流水线
            results = self.pipeline.run_training_pipeline(self.args.latnid, prediction_month)
            
            # 检查结果
            success = any(result.get('success', False) for result in results.values())
            
            if success:
                self.logger.info("训练流程成功完成")
                return 0
            else:
                self.logger.error("训练流程失败")
                return 1
                
        except Exception as e:
            self.logger.error(f"训练流程异常: {e}", exc_info=True)
            return 1
    
    def main(self):
        """主函数"""
        try:
            # 1. 解析参数
            self.parse_arguments()
            
            # 2. 设置日志
            self.setup_logging()
            
            # 3. 验证配置
            if not self.validate_config():
                return 1
            
            # 4. 初始化流水线
            self.initialize_pipeline()
            
            # 5. 运行训练
            return self.run_training()
            
        except KeyboardInterrupt:
            print("\n训练被用户中断")
            return 1
        except Exception as e:
            if self.logger:
                self.logger.error(f"脚本执行异常: {e}", exc_info=True)
            else:
                print(f"脚本执行异常: {e}")
            return 1


def main():
    """入口函数"""
    script = TrainingScriptBase()
    exit_code = script.main()
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
