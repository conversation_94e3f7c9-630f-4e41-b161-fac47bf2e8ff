"""
文件工具模块

提供文件操作相关的工具函数
"""

import os
import shutil
import json
import csv
import pickle
import joblib
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import logging
import hashlib
from datetime import datetime
import zipfile
import pandas as pd

class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def ensure_dir(path: str) -> str:
        """
        确保目录存在，如果不存在则创建
        
        Args:
            path: 目录路径
            
        Returns:
            目录路径
        """
        os.makedirs(path, exist_ok=True)
        return path
    
    @staticmethod
    def list_files(directory: str, pattern: str = "*", recursive: bool = False) -> List[str]:
        """
        列出目录下的文件
        
        Args:
            directory: 目录路径
            pattern: 文件匹配模式
            recursive: 是否递归查找
            
        Returns:
            文件路径列表
        """
        directory_path = Path(directory)
        
        if not directory_path.exists():
            return []
        
        if recursive:
            files = list(directory_path.rglob(pattern))
        else:
            files = list(directory_path.glob(pattern))
        
        return [str(f) for f in files if f.is_file()]
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {}
        
        stat = file_path.stat()
        
        return {
            'name': file_path.name,
            'path': str(file_path.absolute()),
            'size': stat.st_size,
            'size_mb': round(stat.st_size / (1024 * 1024), 2),
            'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'extension': file_path.suffix,
            'is_file': file_path.is_file(),
            'is_dir': file_path.is_dir()
        }
    
    @staticmethod
    def calculate_md5(file_path: str) -> str:
        """
        计算文件MD5哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            MD5哈希值
        """
        hash_md5 = hashlib.md5()
        
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    @staticmethod
    def copy_file(src: str, dst: str, overwrite: bool = False) -> bool:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            是否成功
        """
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                return False
            
            if dst_path.exists() and not overwrite:
                return False
            
            # 确保目标目录存在
            FileUtils.ensure_dir(str(dst_path.parent))
            
            shutil.copy2(src, dst)
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def move_file(src: str, dst: str, overwrite: bool = False) -> bool:
        """
        移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            是否成功
        """
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                return False
            
            if dst_path.exists() and not overwrite:
                return False
            
            # 确保目标目录存在
            FileUtils.ensure_dir(str(dst_path.parent))
            
            shutil.move(src, dst)
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            if file_path.exists():
                if file_path.is_file():
                    file_path.unlink()
                elif file_path.is_dir():
                    shutil.rmtree(file_path)
                return True
            return False
        except Exception:
            return False
    
    @staticmethod
    def clean_directory(directory: str, keep_patterns: List[str] = None) -> int:
        """
        清理目录，删除不需要的文件
        
        Args:
            directory: 目录路径
            keep_patterns: 保留的文件模式列表
            
        Returns:
            删除的文件数量
        """
        if keep_patterns is None:
            keep_patterns = []
        
        directory_path = Path(directory)
        if not directory_path.exists():
            return 0
        
        deleted_count = 0
        
        for file_path in directory_path.iterdir():
            if file_path.is_file():
                # 检查是否需要保留
                should_keep = False
                for pattern in keep_patterns:
                    if file_path.match(pattern):
                        should_keep = True
                        break
                
                if not should_keep:
                    try:
                        file_path.unlink()
                        deleted_count += 1
                    except Exception:
                        pass
        
        return deleted_count
    
    @staticmethod
    def save_json(data: Dict[str, Any], file_path: str, indent: int = 2) -> bool:
        """
        保存JSON文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
            indent: 缩进
            
        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            FileUtils.ensure_dir(str(file_path.parent))
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=indent)
            return True
        except Exception:
            return False
    
    @staticmethod
    def load_json(file_path: str) -> Optional[Dict[str, Any]]:
        """
        加载JSON文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            JSON数据或None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return None
    
    @staticmethod
    def save_pickle(obj: Any, file_path: str) -> bool:
        """
        保存Pickle文件
        
        Args:
            obj: 要保存的对象
            file_path: 文件路径
            
        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            FileUtils.ensure_dir(str(file_path.parent))
            
            with open(file_path, 'wb') as f:
                pickle.dump(obj, f)
            return True
        except Exception:
            return False
    
    @staticmethod
    def load_pickle(file_path: str) -> Optional[Any]:
        """
        加载Pickle文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            对象或None
        """
        try:
            with open(file_path, 'rb') as f:
                return pickle.load(f)
        except Exception:
            return None
    
    @staticmethod
    def save_joblib(obj: Any, file_path: str) -> bool:
        """
        保存Joblib文件
        
        Args:
            obj: 要保存的对象
            file_path: 文件路径
            
        Returns:
            是否成功
        """
        try:
            file_path = Path(file_path)
            FileUtils.ensure_dir(str(file_path.parent))
            
            joblib.dump(obj, file_path)
            return True
        except Exception:
            return False
    
    @staticmethod
    def load_joblib(file_path: str) -> Optional[Any]:
        """
        加载Joblib文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            对象或None
        """
        try:
            return joblib.load(file_path)
        except Exception:
            return None
    
    @staticmethod
    def archive_files(files: List[str], archive_path: str, 
                     compression: str = 'zip') -> bool:
        """
        压缩文件
        
        Args:
            files: 要压缩的文件列表
            archive_path: 压缩包路径
            compression: 压缩类型
            
        Returns:
            是否成功
        """
        try:
            archive_path = Path(archive_path)
            FileUtils.ensure_dir(str(archive_path.parent))
            
            if compression.lower() == 'zip':
                with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for file_path in files:
                        file_path = Path(file_path)
                        if file_path.exists():
                            zipf.write(file_path, file_path.name)
                return True
            else:
                # 可以添加其他压缩格式支持
                return False
        except Exception:
            return False
    
    @staticmethod
    def read_csv_chunked(file_path: str, chunk_size: int = 10000, 
                        **kwargs) -> pd.DataFrame:
        """
        分块读取CSV文件
        
        Args:
            file_path: CSV文件路径
            chunk_size: 每块大小
            **kwargs: pandas.read_csv的其他参数
            
        Returns:
            合并后的DataFrame
        """
        chunks = []
        
        try:
            for chunk in pd.read_csv(file_path, chunksize=chunk_size, **kwargs):
                chunks.append(chunk)
            
            if chunks:
                return pd.concat(chunks, ignore_index=True)
            else:
                return pd.DataFrame()
                
        except Exception:
            return pd.DataFrame()
    
    @staticmethod
    def get_directory_size(directory: str) -> Dict[str, Any]:
        """
        获取目录大小信息
        
        Args:
            directory: 目录路径
            
        Returns:
            大小信息字典
        """
        directory_path = Path(directory)
        
        if not directory_path.exists():
            return {'size': 0, 'size_mb': 0, 'file_count': 0}
        
        total_size = 0
        file_count = 0
        
        for file_path in directory_path.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
                file_count += 1
        
        return {
            'size': total_size,
            'size_mb': round(total_size / (1024 * 1024), 2),
            'size_gb': round(total_size / (1024 * 1024 * 1024), 2),
            'file_count': file_count
        }
    
    @staticmethod
    def backup_file(file_path: str, backup_dir: str = None, 
                   timestamp: bool = True) -> Optional[str]:
        """
        备份文件
        
        Args:
            file_path: 要备份的文件路径
            backup_dir: 备份目录
            timestamp: 是否添加时间戳
            
        Returns:
            备份文件路径
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return None
            
            if backup_dir is None:
                backup_dir = file_path.parent / 'backup'
            
            FileUtils.ensure_dir(backup_dir)
            
            # 构建备份文件名
            backup_name = file_path.name
            if timestamp:
                timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
                stem = file_path.stem
                suffix = file_path.suffix
                backup_name = f"{stem}_{timestamp_str}{suffix}"
            
            backup_path = Path(backup_dir) / backup_name
            
            # 复制文件
            shutil.copy2(file_path, backup_path)
            
            return str(backup_path)
            
        except Exception:
            return None