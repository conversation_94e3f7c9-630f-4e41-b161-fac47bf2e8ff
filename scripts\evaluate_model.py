"""
模型评估脚

核心目标：与最新训练/预测流水线保持一致的特征与窗口语义。

评估关注点：
1. 评估对象是“已发生真实月” M （evaluation_month），使用该月真实 lag_amount。
2. 支撑特征窗口：M, M-1, M-2 三个自然月原始训练数据文件。只对 M 当月样本计算指标。
3. 与训练阶段一致：使用 FeatureExtractor.extract_all_features(mode='training', training_month=M) 生成窗口样本，再过滤 M。
4. 特征转换：在线 fit_transform（当前项目未持久化转换器）。
5. 模型特征列对齐：读取模型 metadata.feature_names；缺失列补 0，多余列忽略；顺序严格匹配。
6. 档位过滤：过滤 gear_label in (0,1)。
7. 业务指标：使用 total_volume（当月真实）计算滞后百分比，不裁剪预测值，仅统计越界率。
8. 回退策略：若 metadata 无 feature_names，则基于列排除清单自动推导。

后续可扩展：回测 (--backtest N)、分月趋势、百分比分布漂移指标等。
"""

import os
import sys
import argparse
import yaml
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any

import pandas as pd
import numpy as np

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data_processor import DataLoader, DataCleaner, DataValidator  # type: ignore
from src.feature_engineer import FeatureExtractor, FeatureTransformer  # type: ignore
from src.model import ModelPredictor, ModelEvaluator  # type: ignore
from src.utils import init_logging_system, setup_project_logging, DateUtils, FileUtils  # type: ignore

# ============================= 参数解析 ============================= #

def parse_arguments():
    parser = argparse.ArgumentParser(description="评估已训练模型在指定已发生账期 (evaluation-month) 的表现 (单本地网模式)")
    parser.add_argument('--config', '-c', default='config/config.yaml', help='配置文件路径')
    parser.add_argument('--latnid', '-n', required=True, help='本地网编码 (必填)')
    parser.add_argument('--evaluation-month', '-m', help='评估目标月 YYYYMM；缺省=当前系统月-1')
    parser.add_argument('--model-timestamp', '-t', help='指定模型时间戳（若不提供则选最新）')
    parser.add_argument('--generate-plots', action='store_true', default=False, help='生成散点/残差/百分比图')
    parser.add_argument('--percentage-plots', action='store_true', help='额外生成滞后量百分比分析图 (需 --generate-plots)')
    parser.add_argument('--output-prefix', help='输出报告文件名前缀 (可选)')
    parser.add_argument('--strict-month-check', action='store_true', default=True, help='严格要求窗口三个月文件均存在 (默认开启)')
    parser.add_argument('--log-level', default=None, help='覆盖配置的控制台日志级别，例如 INFO/DEBUG')

    return parser.parse_args()

# ============================= 配置与日志 ============================= #

def load_config(config_path: str) -> Dict[str, Any]:
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        sys.exit(1)

def setup_logging(config: Dict[str, Any], override_level: str = None):
    log_dir = config.get('logging', {}).get('log_dir') or config.get('logging', {}).get('directory', 'logs')
    console_level = config.get('logging', {}).get('level', 4)  # 内部等级约定
    if override_level:
        mapping = {'CRITICAL':1,'ERROR':2,'WARNING':3,'INFO':4,'DEBUG':6}
        console_level = mapping.get(override_level.upper(), console_level)
    file_level = 6
    init_logging_system(log_dir, console_level, file_level)
    logger = setup_project_logging('model_evaluation', log_dir)
    return logger

# ============================= 工具函数 ============================= #

def month_shift(ym: str, shift: int) -> str:
    y = int(ym[:4]); m = int(ym[4:6])
    total = y * 12 + m - 1 + shift
    ny = total // 12; nm = total % 12 + 1
    return f"{ny}{nm:02d}"

def ensure_ym_format(ym: str) -> bool:
    return ym and len(ym) == 6 and ym.isdigit()

# ============================= 数据准备流程 ============================= #

class EvaluationDataBuilder:
    """负责加载窗口三个月原始数据 -> 清洗 -> 特征提取 -> 过滤当月样本"""
    def __init__(self, config_path: str, logger: logging.Logger):
        self.config_path = config_path
        self.logger = logger
        self.config = load_config(config_path)
        self.raw_dir = self.config.get('data', {}).get('raw_data_dir', 'data/raw')

    def load_window_raw(self, latnid: str, evaluation_month: str, strict: bool = True) -> pd.DataFrame:
        required = {evaluation_month, month_shift(evaluation_month,-1), month_shift(evaluation_month,-2)}
        loader = DataLoader(self.config_path)
        selected, missing = loader.select_files_for_months(latnid, required, data_dir=self.raw_dir)
        if missing and strict:
            raise FileNotFoundError(f"本地网 {latnid} 缺失窗口月份文件: {missing}")
        if not selected:
            raise FileNotFoundError(f"未找到本地网 {latnid} 所需窗口文件 {sorted(required)}")
        grouped = loader.load_specific_files_grouped(latnid, selected)
        df = grouped.get(latnid, pd.DataFrame())
        if df.empty:
            raise ValueError("窗口原始数据加载为空")
        # 过滤仅保留 required 月份
        if 'file_time' in df.columns:
            df = df[df['file_time'].astype(str).isin(required)]
        return df

    def clean_and_validate(self, df: pd.DataFrame) -> pd.DataFrame:
        cleaner = DataCleaner(self.config_path)
        cleaned = cleaner.clean_data(df)
        if isinstance(cleaned, tuple):
            cleaned = cleaned[0]
        if cleaned is None:
            return pd.DataFrame()
        if cleaned.empty:
            return cleaned
        validator = DataValidator(self.config_path)
        try:
            _ = validator.validate_all(cleaned)
        except Exception as e:
            self.logger.warning(f"数据验证过程出现非致命异常: {e}")
        # 档位过滤 0/1
        if 'gear_label' in cleaned.columns:
            before = len(cleaned)
            cleaned = cleaned[~cleaned['gear_label'].isin([0,1,'0','1'])]
            removed = before - len(cleaned)
            if removed > 0:
                self.logger.info(f"过滤 gear_label=0/1 行 {removed}")
        return cleaned

    def extract_features_for_eval_month(self, df: pd.DataFrame, evaluation_month: str) -> pd.DataFrame:
        extractor = FeatureExtractor(self.config_path)
        features = extractor.extract_all_features(df, training_month=evaluation_month, mode='training')
        if features.empty:
            return features
        # 仅取 evaluation_month 样本
        if {'year','month'}.issubset(features.columns):
            ym = features['year'] * 100 + features['month']
            target_int = int(evaluation_month)
            before = len(features)
            features = features[ym == target_int]
            self.logger.info(f"过滤窗口仅保留当月 {evaluation_month} 样本: {before} -> {len(features)}")
        # 再次兜底档位过滤
        if 'gear_label' in features.columns:
            before = len(features)
            features = features[~features['gear_label'].isin([0,1,'0','1'])]
            if len(features) != before:
                self.logger.info(f"特征阶段再次剔除 gear_label=0/1 行 {before - len(features)}")
        return features

    def build(self, latnid: str, evaluation_month: str, strict: bool = True) -> pd.DataFrame:
        self.logger.info("加载窗口原始数据 ...")
        raw_df = self.load_window_raw(latnid, evaluation_month, strict)
        self.logger.info(f"窗口原始行数: {len(raw_df)}")
        self.logger.info("清洗与验证 ...")
        cleaned = self.clean_and_validate(raw_df)
        if cleaned.empty:
            self.logger.error("清洗后无有效数据")
            return pd.DataFrame()
        self.logger.info("特征提取 ...")
        feat_df = self.extract_features_for_eval_month(cleaned, evaluation_month)
        return feat_df

# ============================= 特征对齐与预测 ============================= #

def transform_and_align(features_df: pd.DataFrame, config_path: str, metadata: Dict[str, Any], logger: logging.Logger) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame]:
    if features_df.empty:
        return features_df, pd.Series(dtype=float), features_df
    if 'lag_amount' not in features_df.columns:
        raise ValueError("特征数据缺少目标列 'lag_amount'")
    transformer = FeatureTransformer(config_path)
    transformed = transformer.fit_transform(features_df)
    # 对齐列
    feature_names = None
    if metadata:
        feature_names = metadata.get('feature_names') or metadata.get('model_feature_names')
    exclude = {
        'lag_amount','user','local_network','order_id','remind_time','source_file','file_time',
        'product_instance_id','file_local_network'
    }
    if feature_names:
        # 训练时记录的列
        for col in feature_names:
            if col not in transformed.columns:
                transformed[col] = 0
        X = transformed[feature_names].copy()
        logger.info(f"按元数据特征对齐: {len(feature_names)} 列")
    else:
        # 回退：自动推导
        auto_cols = [c for c in transformed.columns if c not in exclude]
        X = transformed[auto_cols].copy()
        logger.warning(f"元数据缺少 feature_names，使用自动推导列 {len(auto_cols)}")
    y = transformed['lag_amount'].copy()
    return X, y, transformed

# ============================= 评估主逻辑 ============================= #

def evaluate_single_month(args, config, logger) -> int:
    latnid = args.latnid
    evaluation_month = args.evaluation_month
    if not evaluation_month:
        # 默认使用当前系统月 -1
        cur = datetime.now().strftime('%Y%m')
        evaluation_month = month_shift(cur, -1)
    if not ensure_ym_format(evaluation_month):
        logger.error(f"evaluation-month 格式错误: {evaluation_month}")
        return 1

    logger.info("="*60)
    logger.info(f"开始评估: 本地网={latnid} 账期={evaluation_month} 模型时间戳={args.model_timestamp or 'latest'}")
    logger.info("="*60)

    # 构建特征数据
    builder = EvaluationDataBuilder(args.config, logger)
    try:
        feat_df = builder.build(latnid, evaluation_month, strict=args.strict_month_check)
    except FileNotFoundError as fe:
        logger.error(str(fe))
        return 1
    except Exception as e:
        logger.error(f"数据准备失败: {e}")
        return 1

    if feat_df.empty:
        logger.error("评估数据为空，终止")
        return 1

    logger.info(f"评估当月样本数: {len(feat_df)}")
    if 'total_volume' not in feat_df.columns:
        logger.warning("评估数据缺少 total_volume 列，业务百分比指标将不可用")

    # 加载模型
    predictor = ModelPredictor(args.config)
    try:
        model, metadata = predictor.load_model_by_local_network(latnid, args.model_timestamp)
    except Exception as e:
        logger.error(f"加载模型失败: {e}")
        return 1

    # 特征对齐
    try:
        X, y_true, transformed_full = transform_and_align(feat_df, args.config, metadata or {}, logger)
    except Exception as e:
        logger.error(f"特征转换/对齐失败: {e}")
        return 1

    if X.empty or y_true.empty:
        logger.error("对齐后数据为空")
        return 1

    # 预测
    try:
        y_pred = predictor.predict(model, X)
    except Exception as e:
        logger.error(f"模型预测失败: {e}")
        return 1

    # 业务指标 total_volume
    total_volumes = None
    if 'total_volume' in transformed_full.columns:
        total_volumes = transformed_full['total_volume'].values

    # 评估
    evaluator = ModelEvaluator(args.config)
    eval_result = evaluator.evaluate_predictions(y_true.values, y_pred, total_volumes, dataset_name=f"{latnid}_{evaluation_month}")

    # 增补越界率（预测值用 total_volume 计算滞后百分比）
    if total_volumes is not None and len(total_volumes) == len(y_pred):
        with np.errstate(divide='ignore', invalid='ignore'):
            pct_pred = (y_pred / total_volumes) * 100
        min_p = evaluator.min_lag_percentage
        max_p = evaluator.max_lag_percentage
        out_low = float(np.sum(pct_pred < min_p)) / len(pct_pred)
        out_high = float(np.sum(pct_pred > max_p)) / len(pct_pred)
        eval_result['prediction_percentage_out_of_range_low'] = out_low
        eval_result['prediction_percentage_out_of_range_high'] = out_high

    # 保存报告
    timestamp = DateUtils.get_current_timestamp()
    report_prefix = args.output_prefix or f"evaluation_{latnid}_{evaluation_month}"
    evaluator_report_name = f"{report_prefix}_{timestamp}"
    report_path = evaluator.generate_evaluation_report([eval_result], evaluator_report_name)

    # 生成图表
    if args.generate_plots:
        try:
            evaluator.plot_prediction_scatter(y_true.values, y_pred, dataset_name=f"{latnid}_{evaluation_month}")
            evaluator.plot_residuals(y_true.values, y_pred, dataset_name=f"{latnid}_{evaluation_month}")
            if args.percentage_plots and total_volumes is not None:
                evaluator.plot_percentage_analysis(y_true.values, y_pred, total_volumes, dataset_name=f"{latnid}_{evaluation_month}")
        except Exception as e:
            logger.warning(f"生成图表出现非致命异常: {e}")

    # 简要日志输出
    basic = eval_result.get('basic_metrics', {})
    business = eval_result.get('business_metrics', {})
    logger.info("-"*40)
    logger.info(f"RMSE: {basic.get('rmse','NA'):.4f}  MAE: {basic.get('mae','NA'):.4f}  R2: {basic.get('r2','NA'):.4f}  MAPE: {basic.get('mape','NA'):.2f}%")
    if business:
        logger.info(f"Pred In-Range Rate: {business.get('pred_in_range_rate','NA')}")
    logger.info(f"预测百分比越界率 <min: {eval_result.get('prediction_percentage_out_of_range_low','NA'):.4f}, >max: {eval_result.get('prediction_percentage_out_of_range_high','NA'):.4f}")
    logger.info(f"报告文件: {report_path}")
    logger.info("评估完成")
    logger.info("="*60)
    return 0

def main():
    args = parse_arguments()
    config = load_config(args.config)
    logger = setup_logging(config, args.log_level)

    exit_code = evaluate_single_month(args, config, logger)
    sys.exit(exit_code)

if __name__ == '__main__':
    main()
"""
模型评估脚本

用于评估训练好的模型性能
"""

import os
import sys
import argparse
import yaml
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data_processor import DataLoader, DataCleaner, DataValidator
from src.feature_engineer import FeatureExtractor, FeatureTransformer, FeatureSelector
from src.model import ModelPredictor, ModelEvaluator
from src.utils import setup_project_logging, get_module_logger, DateUtils, FileUtils

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='评估训练好的模型性能')
    
    parser.add_argument('--config', '-c', type=str, 
                       default='config/config.yaml',
                       help='配置文件路径')
    
    parser.add_argument('--data-dir', '-d', type=str,
                       help='评估数据目录路径（覆盖配置文件中的设置）')
    
    parser.add_argument('--model-dir', '-m', type=str,
                       help='模型目录路径（覆盖配置文件中的设置）')
    
    parser.add_argument('--output-dir', '-o', type=str,
                       help='评估结果输出目录路径（覆盖配置文件中的设置）')
    
    parser.add_argument('--latnid', '-n', type=str,
                       help='指定本地网（仅评估指定本地网的模型）')
    
    parser.add_argument('--model-timestamp', '-t', type=str,
                       help='指定模型时间戳（评估特定版本的模型）')
    
    parser.add_argument('--test-file', type=str,
                       help='测试数据文件路径（用于评估单个文件）')
    
    parser.add_argument('--validation-split', '-v', type=float,
                       help='验证集比例（用于分割数据集）')
    
    parser.add_argument('--compare-models', action='store_true',
                       help='比较多个模型的性能')
    
    parser.add_argument('--generate-plots', action='store_true',
                       default=True,
                       help='生成评估图表（默认启用）')
    
    parser.add_argument('--no-plots', action='store_true',
                       help='禁用图表生成')
    
    parser.add_argument('--detailed-analysis', action='store_true',
                       help='进行详细分析（包括残差分析、分布分析等）')
    
    parser.add_argument('--business-metrics', action='store_true',
                       default=True,
                       help='计算业务指标（默认启用）')
    
    parser.add_argument('--segment-analysis', action='store_true',
                       help='进行细分市场分析')
    
    parser.add_argument('--log-level', type=str,
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO',
                       help='日志级别')
    
    # 已移除 --parallel-jobs 参数
    
    parser.add_argument('--random-seed', type=int,
                       default=42,
                       help='随机种子')
    
    return parser.parse_args()

def load_and_update_config(config_path: str, args) -> dict:
    """加载并更新配置"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        sys.exit(1)
    
    # 使用命令行参数更新配置
    if args.data_dir:
        config['data']['raw_data_dir'] = args.data_dir
    
    if args.model_dir:
        config['model']['trained_models_dir'] = args.model_dir
        config['model']['metadata_dir'] = args.model_dir
    
    if args.output_dir:
        config['model']['evaluations_dir'] = args.output_dir
    
    if args.validation_split:
        config['model']['validation_split'] = args.validation_split
    
    # 不再支持命令行覆盖并行度
    
    return config

def setup_logging(config: dict, log_level: str):
    """设置日志"""
    log_dir = config.get('logging', {}).get('directory', 'logs')
    
    # 设置项目日志
    logger = setup_project_logging('model_evaluation', log_dir)
    
    # 设置日志级别
    level = getattr(logging, log_level.upper())
    logger.setLevel(level)
    
    return logger

def load_test_data(args, config: dict, logger):
    """加载测试数据"""
    if args.test_file:
        # 加载单个测试文件
        logger.info(f"加载测试文件: {args.test_file}")
        
        data_loader = DataLoader(config)
        try:
            df = data_loader.load_single_file(args.test_file)
            if df.empty:
                logger.error(f"测试文件为空或格式不正确: {args.test_file}")
                return None
            
            # 提取本地网信息
            local_network = df['local_network'].iloc[0] if 'local_network' in df.columns else 'unknown'
            return {local_network: df}
            
        except Exception as e:
            logger.error(f"加载测试文件失败: {e}")
            return None
    else:
        # 加载目录数据
        data_dir = args.data_dir or config['data']['raw_data_dir']
        if not os.path.exists(data_dir):
            logger.error(f"数据目录不存在: {data_dir}")
            return None
        
        logger.info(f"加载测试数据目录: {data_dir}")
        
        data_loader = DataLoader(config)
        
        if args.local_network:
            # 只加载指定本地网的数据
            data_dict = data_loader.load_data_by_local_network([args.local_network])
            if not data_dict:
                logger.error(f"未找到本地网 {args.local_network} 的数据")
                return None
        else:
            # 加载所有数据
            data_dict = data_loader.load_all_data()
            if not data_dict:
                logger.error("未找到任何数据文件")
                return None
        
        return data_dict

def prepare_test_data(data_dict: dict, config: dict, validation_split: float, random_seed: int, logger):
    """准备测试数据"""
    logger.info("准备测试数据")
    
    # 数据清洗
    data_cleaner = DataCleaner(config)
    cleaned_data = {}
    
    for key, df in data_dict.items():
        cleaned_df = data_cleaner.clean_data(df)
        if not cleaned_df.empty:
            cleaned_data[key] = cleaned_df
            logger.info(f"数据集 {key}: 清洗前 {len(df)} 行，清洗后 {len(cleaned_df)} 行")
        else:
            logger.warning(f"数据集 {key} 清洗后为空，跳过")
    
    if not cleaned_data:
        logger.error("所有数据集清洗后都为空")
        return None
    
    # 特征工程
    feature_extractor = FeatureExtractor(config)
    feature_transformer = FeatureTransformer(config)
    feature_selector = FeatureSelector(config)
    
    processed_data = {}
    
    for key, df in cleaned_data.items():
        # 特征提取
        features_df = feature_extractor.extract_features(df)
        if features_df.empty:
            logger.warning(f"数据集 {key} 特征提取后为空，跳过")
            continue
        
        # 特征转换
        transformed_df = feature_transformer.transform_features(features_df)
        
        # 特征选择
        selected_df = feature_selector.select_features(transformed_df)
        
        # 数据分割（如果指定了验证集比例）
        if validation_split and validation_split > 0:
            # 使用验证集作为测试集
            np.random.seed(random_seed)
            sample_indices = np.random.permutation(len(selected_df))
            split_idx = int(len(selected_df) * (1 - validation_split))
            test_indices = sample_indices[split_idx:]
            test_df = selected_df.iloc[test_indices].reset_index(drop=True)
        else:
            test_df = selected_df
        
        processed_data[key] = test_df
        logger.info(f"数据集 {key}: 测试样本数 {len(test_df)}")
    
    return processed_data

def evaluate_single_model(local_network: str, test_data: pd.DataFrame, 
                         predictor: ModelPredictor, evaluator: ModelEvaluator,
                         model_timestamp: str, config: dict, logger):
    """评估单个模型"""
    logger.info(f"评估本地网 {local_network} 的模型")
    
    try:
        # 加载模型
        model, metadata = predictor.load_model_by_local_network(local_network, model_timestamp)
        
        # 准备特征和目标变量
        exclude_columns = [
            'current_lag_amount', 'product_instance_id', 'local_network',
            'order_id', 'remind_time', 'source_file', 'file_local_network', 'file_time'
        ]
        
        feature_columns = [col for col in test_data.columns if col not in exclude_columns]
        X_test = test_data[feature_columns]
        
        if 'current_lag_amount' not in test_data.columns:
            logger.error(f"测试数据缺少目标变量 'current_lag_amount'")
            return None
        
        y_true = test_data['current_lag_amount'].values
        
        # 执行预测
        y_pred = predictor.predict(model, X_test)
        
        # 获取总量（用于业务指标计算）
        total_volumes = None
        if 'current_total_volume' in test_data.columns:
            total_volumes = test_data['current_total_volume'].values
        
        # 获取细分信息（用于细分分析）
        segments = None
        if 'gear_label' in test_data.columns:
            segments = test_data['gear_label'].values
        
        # 执行评估
        evaluation_result = evaluator.evaluate_predictions(
            y_true, y_pred, total_volumes, f"{local_network}_{model_timestamp or 'latest'}"
        )
        
        # 添加模型元数据
        evaluation_result['model_metadata'] = metadata
        evaluation_result['test_samples'] = len(test_data)
        
        return {
            'evaluation': evaluation_result,
            'predictions': y_pred,
            'actuals': y_true,
            'total_volumes': total_volumes,
            'segments': segments,
            'test_data': test_data
        }
        
    except Exception as e:
        logger.error(f"评估本地网 {local_network} 的模型失败: {e}")
        return None

def generate_evaluation_plots(evaluation_results: dict, evaluator: ModelEvaluator, 
                            generate_plots: bool, detailed_analysis: bool, logger):
    """生成评估图表"""
    if not generate_plots:
        return {}
    
    logger.info("生成评估图表")
    plot_files = {}
    
    for local_network, result in evaluation_results.items():
        if result is None:
            continue
        
        try:
            y_true = result['actuals']
            y_pred = result['predictions']
            total_volumes = result['total_volumes']
            dataset_name = f"{local_network}"
            
            # 预测vs真实值散点图
            scatter_path = evaluator.plot_prediction_scatter(y_true, y_pred, dataset_name)
            if scatter_path:
                plot_files[f"{local_network}_scatter"] = scatter_path
            
            if detailed_analysis:
                # 残差分析图
                residuals_path = evaluator.plot_residuals(y_true, y_pred, dataset_name)
                if residuals_path:
                    plot_files[f"{local_network}_residuals"] = residuals_path
                
                # 百分比分析图
                if total_volumes is not None:
                    percentage_path = evaluator.plot_percentage_analysis(
                        y_true, y_pred, total_volumes, dataset_name
                    )
                    if percentage_path:
                        plot_files[f"{local_network}_percentage"] = percentage_path
            
        except Exception as e:
            logger.error(f"生成 {local_network} 的图表失败: {e}")
    
    return plot_files

def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()
    
    # 加载配置
    config = load_and_update_config(args.config, args)
    
    # 设置日志
    logger = setup_logging(config, args.log_level)
    
    # 设置随机种子
    np.random.seed(args.random_seed)
    
    # 确定是否生成图表
    generate_plots = args.generate_plots and not args.no_plots
    
    logger.info("=" * 50)
    logger.info("开始模型评估流程")
    logger.info(f"配置文件: {args.config}")
    logger.info(f"模型目录: {config['model']['trained_models_dir']}")
    logger.info(f"生成图表: {generate_plots}")
    logger.info(f"详细分析: {args.detailed_analysis}")
    logger.info("=" * 50)
    
    try:
        # 1. 加载测试数据
        logger.info("步骤 1: 加载测试数据")
        
        raw_data = load_test_data(args, config, logger)
        if not raw_data:
            logger.error("未能加载任何测试数据")
            sys.exit(1)
        
        logger.info(f"成功加载 {len(raw_data)} 个测试数据集")
        
        # 2. 准备测试数据
        logger.info("步骤 2: 准备测试数据")
        
        test_data = prepare_test_data(
            raw_data, config, args.validation_split, args.random_seed, logger
        )
        
        if not test_data:
            logger.error("测试数据准备失败")
            sys.exit(1)
        
        # 3. 初始化评估器
        logger.info("步骤 3: 初始化评估器")
        
        predictor = ModelPredictor(args.config)
        evaluator = ModelEvaluator(args.config)
        
        # 检查可用模型
        available_models = predictor.list_available_models()
        if not available_models:
            logger.error("未找到任何可用的模型")
            sys.exit(1)
        
        logger.info(f"找到 {len(available_models)} 个可用模型")
        
        # 4. 执行评估
        logger.info("步骤 4: 执行模型评估")
        
        evaluation_results = {}
        
        for local_network, df in test_data.items():
            if args.local_network and local_network != args.local_network:
                continue
            
            result = evaluate_single_model(
                local_network, df, predictor, evaluator,
                args.model_timestamp, config, logger
            )
            
            evaluation_results[local_network] = result
        
        # 过滤成功的评估结果
        successful_evaluations = {k: v for k, v in evaluation_results.items() if v is not None}
        
        if not successful_evaluations:
            logger.error("所有模型评估都失败了")
            sys.exit(1)
        
        logger.info(f"成功评估 {len(successful_evaluations)} 个模型")
        
        # 5. 生成图表
        if generate_plots:
            logger.info("步骤 5: 生成评估图表")
            plot_files = generate_evaluation_plots(
                successful_evaluations, evaluator, generate_plots, args.detailed_analysis, logger
            )
            
            if plot_files:
                logger.info(f"生成了 {len(plot_files)} 个图表文件")
                for plot_name, plot_path in plot_files.items():
                    logger.info(f"  - {plot_name}: {plot_path}")
        
        # 6. 保存评估结果
        logger.info("步骤 6: 保存评估结果")
        
        # 提取评估指标
        all_evaluations = []
        for local_network, result in successful_evaluations.items():
            if result:
                all_evaluations.append(result['evaluation'])
        
        # 生成评估报告
        timestamp = DateUtils.get_current_timestamp()
        report_name = f"evaluation_report_{timestamp}"
        
        if args.local_network:
            report_name = f"evaluation_{args.local_network}_{timestamp}"
        
        report_path = evaluator.generate_evaluation_report(all_evaluations, report_name)
        
        if report_path:
            logger.info(f"评估报告已保存: {report_path}")
        
        # 7. 模型比较
        if args.compare_models and len(successful_evaluations) > 1:
            logger.info("步骤 7: 模型性能比较")
            
            comparison_name = f"model_comparison_{timestamp}"
            comparison_path = evaluator.compare_models(all_evaluations, comparison_name)
            
            if comparison_path:
                logger.info(f"模型比较结果已保存: {comparison_path}")
        
        # 8. 输出评估摘要
        logger.info("步骤 8: 评估结果摘要")
        
        logger.info("=" * 50)
        logger.info("评估结果摘要:")
        
        for local_network, result in successful_evaluations.items():
            if result:
                evaluation = result['evaluation']
                basic_metrics = evaluation.get('basic_metrics', {})
                business_metrics = evaluation.get('business_metrics', {})
                
                logger.info(f"\n本地网: {local_network}")
                logger.info(f"  测试样本数: {evaluation.get('sample_count', 0)}")
                logger.info(f"  RMSE: {basic_metrics.get('rmse', 0):.4f}")
                logger.info(f"  MAE: {basic_metrics.get('mae', 0):.4f}")
                logger.info(f"  R²: {basic_metrics.get('r2', 0):.4f}")
                logger.info(f"  MAPE: {basic_metrics.get('mape', 0):.2f}%")
                
                if business_metrics:
                    logger.info(f"  百分比RMSE: {business_metrics.get('percentage_rmse', 0):.4f}")
                    logger.info(f"  预测范围内比例: {business_metrics.get('pred_in_range_rate', 0):.2%}")
                    logger.info(f"  预测高风险比例: {business_metrics.get('pred_high_risk_rate', 0):.2%}")
        
        # 9. 整体统计
        if len(successful_evaluations) > 1:
            # 计算整体统计
            all_rmse = [result['evaluation']['basic_metrics'].get('rmse', 0) 
                       for result in successful_evaluations.values() if result]
            all_r2 = [result['evaluation']['basic_metrics'].get('r2', 0) 
                     for result in successful_evaluations.values() if result]
            
            if all_rmse and all_r2:
                logger.info(f"\n整体统计:")
                logger.info(f"  平均RMSE: {np.mean(all_rmse):.4f}")
                logger.info(f"  平均R²: {np.mean(all_r2):.4f}")
                logger.info(f"  RMSE标准差: {np.std(all_rmse):.4f}")
                logger.info(f"  R²标准差: {np.std(all_r2):.4f}")
        
        logger.info("=" * 50)
        logger.info("评估完成")
        
        if report_path:
            logger.info(f"详细评估报告: {report_path}")
        
        evaluations_dir = config.get('model', {}).get('evaluations_dir', 'models/evaluations')
        logger.info(f"评估结果保存目录: {evaluations_dir}")
        
        logger.info("=" * 50)
        
        sys.exit(0)
        
    except KeyboardInterrupt:
        logger.info("评估被用户中断")
        sys.exit(130)
    
    except Exception as e:
        logger.error(f"评估过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()