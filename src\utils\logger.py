"""
日志工具模块

提供统一的日志配置和管理功能
"""

import logging
import sys
import os
import threading
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import json
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler

# 日志级别定义映射
TELECOM_LOG_LEVELS = {
    0: logging.NOTSET,    # DCLOG_LEVEL_NONE - 无日志级别
    1: logging.CRITICAL,  # DCLOG_LEVEL_FATAL - 严重错误级别  
    2: logging.ERROR,     # DCLOG_LEVEL_ERROR - 一般错误级别
    3: logging.WARNING,   # DCLOG_LEVEL_WARN - 警告日志级别
    4: logging.INFO,      # DCLOG_LEVEL_INFO - 信息日志级别
    5: 15,               # DCLOG_LEVEL_DVIEW - 简略调试日志级别(自定义级别)
    6: logging.DEBUG,     # DCLOG_LEVEL_DEBUG - 一般调试日志级别
    7: 5                 # DCLOG_LEVEL_TRACE - 详尽调试日志级别(自定义级别)
}


class TelecomLogFormatter(logging.Formatter):
    """
    日志格式化器
    格式：系统时间|模块名|进程号|线程号|日志级别|源码文件:行号|日志信息
    """
    
    def format(self, record):
        # 获取系统时间，格式化为 YYYY-MM-DD HH:MM:SS:ffffff（微秒部分用冒号分隔）
        dt = datetime.fromtimestamp(record.created)
        timestamp = dt.strftime('%Y-%m-%d %H:%M:%S') + ':' + dt.strftime('%f')
        
        # 模块名
        module_name = record.name
        
        # 进程号
        process_id = os.getpid()
        
        # 线程号
        thread_id = threading.current_thread().ident
        
        # 日志级别
        level_name = record.levelname
        
        # 源码文件:行号
        filename = os.path.basename(record.pathname) if record.pathname else 'unknown'
        line_number = record.lineno
        source_location = f"{filename}:{line_number}"
        
        # 日志信息
        message = record.getMessage()
        
        # 组装最终格式：系统时间|模块名|进程号|线程号|日志级别|源码文件:行号|日志信息
        formatted_message = f"{timestamp}|{module_name}|{process_id}|{thread_id}|{level_name}|{source_location}|{message}"
        
        # 如果有异常信息，附加到消息后面
        if record.exc_info:
            exc_text = self.formatException(record.exc_info)
            formatted_message += f"\n{exc_text}"
        
        return formatted_message


# 日志级别定义映射
TELECOM_LOG_LEVELS = {
    0: logging.NOTSET,    # DCLOG_LEVEL_NONE - 无日志级别
    1: logging.CRITICAL,  # DCLOG_LEVEL_FATAL - 严重错误级别  
    2: logging.ERROR,     # DCLOG_LEVEL_ERROR - 一般错误级别
    3: logging.WARNING,   # DCLOG_LEVEL_WARN - 警告日志级别
    4: logging.INFO,      # DCLOG_LEVEL_INFO - 信息日志级别
    5: 15,               # DCLOG_LEVEL_DVIEW - 简略调试日志级别(自定义级别)
    6: logging.DEBUG,     # DCLOG_LEVEL_DEBUG - 一般调试日志级别
    7: 5                 # DCLOG_LEVEL_TRACE - 详尽调试日志级别(自定义级别)
}

# 反向映射，用于配置验证
LEVEL_NAMES = {
    0: 'NONE',
    1: 'FATAL', 
    2: 'ERROR',
    3: 'WARN',
    4: 'INFO',
    5: 'DVIEW',
    6: 'DEBUG', 
    7: 'TRACE'
}

def get_logging_level(telecom_level):
    """
    日志级别转换为Python logging级别
    
    Args:
        telecom_level: 日志级别(0-7)或字符串级别

    Returns:
        Python logging级别
    """
    # 如果是字符串，先尝试转换为数字
    if isinstance(telecom_level, str):
        if telecom_level.upper() in ['NONE', 'FATAL', 'ERROR', 'WARN', 'INFO', 'DVIEW', 'DEBUG', 'TRACE']:
            # 从LEVEL_NAMES反向查找
            for level_num, level_name in LEVEL_NAMES.items():
                if level_name == telecom_level.upper():
                    telecom_level = level_num
                    break
        else:
            # 尝试作为标准Python logging级别处理
            return getattr(logging, telecom_level.upper(), logging.INFO)
    
    # 如果是数字，直接映射
    if isinstance(telecom_level, int):
        return TELECOM_LOG_LEVELS.get(telecom_level, logging.INFO)
    
    return logging.INFO


def setup_custom_levels():
    """设置自定义日志级别"""
    # 添加DVIEW级别
    logging.addLevelName(15, 'DVIEW')
    def dview(self, message, *args, **kwargs):
        if self.isEnabledFor(15):
            self._log(15, message, args, **kwargs)
    logging.Logger.dview = dview
    
    # 添加TRACE级别  
    logging.addLevelName(5, 'TRACE')
    def trace(self, message, *args, **kwargs):
        if self.isEnabledFor(5):
            self._log(5, message, args, **kwargs)
    logging.Logger.trace = trace

# 初始化自定义级别
setup_custom_levels()

class LoggerManager:
    """日志管理器"""
    
    _loggers: Dict[str, logging.Logger] = {}
    _handlers: Dict[str, logging.Handler] = {}
    _root_configured: bool = False
    
    @classmethod
    def _configure_root_logger(cls, config: Dict[str, Any] = None):
        """配置根日志器，确保所有子日志器使用统一格式"""
        if cls._root_configured:
            return
            
        if config is None:
            config = cls._get_default_config()
        
        root_logger = logging.getLogger()
        
        # 清除根日志器的已有处理器
        root_logger.handlers.clear()
        
        # 设置根日志器级别
        root_level = get_logging_level(config.get('level', 4))
        root_logger.setLevel(root_level)
        
        # 添加控制台处理器到根日志器
        if config.get('console', {}).get('enabled', True):
            console_handler = cls._create_console_handler(config.get('console', {}))
            root_logger.addHandler(console_handler)
        
        # 强制更新所有已存在的日志器，清除其处理器，让它们继承根日志器的处理器
        cls._update_existing_loggers()
        
        cls._root_configured = True
    
    @classmethod
    def _update_existing_loggers(cls):
        """更新所有已存在的日志器，清除其处理器"""
        # 获取日志器管理器中的所有日志器
        logger_dict = logging.Logger.manager.loggerDict
        
        for name, logger_obj in logger_dict.items():
            if isinstance(logger_obj, logging.Logger):
                # 清除已存在日志器的处理器，让它们继承根日志器的处理器
                logger_obj.handlers.clear()
                # 确保它们不会阻止向根日志器传播
                logger_obj.propagate = True
    
    @classmethod
    def get_logger(cls, name: str, config: Dict[str, Any] = None) -> logging.Logger:
        """
        获取或创建日志器
        
        Args:
            name: 日志器名称
            config: 日志配置
            
        Returns:
            日志器实例
        """
        # 首先配置根日志器，确保所有子日志器使用统一格式
        cls._configure_root_logger(config)
        
        if name in cls._loggers:
            return cls._loggers[name]
        
        logger = cls._create_logger(name, config)
        cls._loggers[name] = logger
        
        return logger
    
    @classmethod
    def _create_logger(cls, name: str, config: Dict[str, Any] = None) -> logging.Logger:
        """创建日志器"""
        if config is None:
            config = cls._get_default_config()
        
        logger = logging.getLogger(name)
        # 日志级别映射
        log_level = get_logging_level(config.get('level', 4))  # 默认INFO级别
        logger.setLevel(log_level)
        
        # 为具体模块创建独立的文件处理器（使用模块名作为文件前缀）
        # 根日志器只处理控制台输出，各模块管理自己的文件输出
        if config and config.get('file', {}).get('enabled', True):
            file_handler = cls._create_file_handler(name, config.get('file', {}))
            if file_handler:
                logger.addHandler(file_handler)
                # 存储处理器引用以便后续管理
                cls._handlers[f"{name}_file"] = file_handler
        
        return logger
    
    @classmethod
    def _create_console_handler(cls, console_config: Dict[str, Any]) -> logging.StreamHandler:
        """创建控制台处理器"""
        handler = logging.StreamHandler(sys.stdout)
        # 使用日志级别映射
        console_level = get_logging_level(console_config.get('level', 4))  # 默认INFO级别
        handler.setLevel(console_level)

        # 使用固定的日志格式
        formatter = TelecomLogFormatter()
        handler.setFormatter(formatter)
        
        return handler
    
    @classmethod
    def _create_file_handler(cls, name: str, file_config: Dict[str, Any]) -> Optional[logging.Handler]:
        """创建文件处理器"""
        try:
            log_dir = file_config.get('directory', 'logs')
            Path(log_dir).mkdir(parents=True, exist_ok=True)
            
            # 日志文件名格式：模块名_进程号.log
            process_id = os.getpid()
            log_file = os.path.join(log_dir, f"{name}_{process_id}.log")
            
            # 选择处理器类型
            rotation_type = file_config.get('rotation_type', 'size')
            
            if rotation_type == 'size':
                handler = RotatingFileHandler(
                    log_file,
                    maxBytes=file_config.get('max_bytes', 10 * 1024 * 1024),  # 10MB
                    backupCount=file_config.get('backup_count', 5),
                    encoding='utf-8'
                )
            elif rotation_type == 'time':
                handler = TimedRotatingFileHandler(
                    log_file,
                    when=file_config.get('when', 'midnight'),
                    interval=file_config.get('interval', 1),
                    backupCount=file_config.get('backup_count', 30),
                    encoding='utf-8'
                )
            else:
                handler = logging.FileHandler(log_file, encoding='utf-8')
            
            handler.setLevel(get_logging_level(file_config.get('level', 6)))  # 默认DEBUG级别

            # 使用固定的日志格式
            formatter = TelecomLogFormatter()
            handler.setFormatter(formatter)
            
            return handler
            
        except Exception as e:
            print(f"创建文件处理器失败: {e}")
            return None
    

    
    @classmethod
    def _get_default_config(cls) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'level': 4,  # INFO级别
            'console': {
                'enabled': True,
                'level': 4  # INFO级别
            },
            'file': {
                'enabled': True,
                'level': 6,  # DEBUG级别
                'directory': 'logs',
                'rotation_type': 'size',
                'max_bytes': 10 * 1024 * 1024,
                'backup_count': 5
            }
        }
    
    @classmethod
    def configure_from_file(cls, config_file: str):
        """从配置文件加载日志配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 清除已有的日志器
            cls._loggers.clear()
            
            # 设置根日志器
            root_config = config.get('root', {})
            if root_config:
                root_logger = logging.getLogger()
                root_level = get_logging_level(root_config.get('level', 4))  # 默认INFO级别
                root_logger.setLevel(root_level)
                
        except Exception as e:
            print(f"加载日志配置失败: {e}")
    
    @classmethod
    def set_level(cls, logger_name: str, level):
        """设置日志级别"""
        if logger_name in cls._loggers:
            logging_level = get_logging_level(level)
            cls._loggers[logger_name].setLevel(logging_level)
    
    @classmethod
    def add_handler(cls, logger_name: str, handler: logging.Handler):
        """添加处理器"""
        if logger_name in cls._loggers:
            cls._loggers[logger_name].addHandler(handler)
    
    @classmethod
    def remove_handler(cls, logger_name: str, handler: logging.Handler):
        """移除处理器"""
        if logger_name in cls._loggers:
            cls._loggers[logger_name].removeHandler(handler)


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.logger = LoggerManager.get_logger(name, config)
        self.context = {}
    
    def set_context(self, **kwargs):
        """设置上下文信息"""
        self.context.update(kwargs)
    
    def clear_context(self):
        """清除上下文信息"""
        self.context.clear()
    
    def _format_message(self, message: str, extra: Dict[str, Any] = None) -> str:
        """格式化消息"""
        data = {
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'context': self.context.copy()
        }
        
        if extra:
            data['extra'] = extra
        
        return json.dumps(data, ensure_ascii=False)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        formatted_msg = self._format_message(message, kwargs)
        self.logger.debug(formatted_msg)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        formatted_msg = self._format_message(message, kwargs)
        self.logger.info(formatted_msg)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        formatted_msg = self._format_message(message, kwargs)
        self.logger.warning(formatted_msg)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        formatted_msg = self._format_message(message, kwargs)
        self.logger.error(formatted_msg)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        formatted_msg = self._format_message(message, kwargs)
        self.logger.critical(formatted_msg)


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.start_times = {}
    
    def start_timer(self, operation: str):
        """开始计时"""
        self.start_times[operation] = datetime.now()
        self.logger.info(f"开始执行: {operation}")
    
    def end_timer(self, operation: str, extra_info: str = ""):
        """结束计时并记录"""
        if operation in self.start_times:
            start_time = self.start_times[operation]
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.info(
                f"完成执行: {operation} - 耗时: {duration:.2f}秒 {extra_info}"
            )
            
            del self.start_times[operation]
            return duration
        else:
            self.logger.warning(f"未找到操作的开始时间: {operation}")
            return None
    
    def log_memory_usage(self, operation: str = ""):
        """记录内存使用情况"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            
            self.logger.info(
                f"内存使用情况 {operation}: "
                f"RSS={memory_info.rss / 1024 / 1024:.2f}MB, "
                f"VMS={memory_info.vms / 1024 / 1024:.2f}MB"
            )
            
        except ImportError:
            self.logger.warning("psutil未安装，无法记录内存使用情况")
        except Exception as e:
            self.logger.error(f"记录内存使用情况失败: {e}")


def setup_project_logging(project_name: str, log_dir: str = "logs") -> logging.Logger:
    """
    设置项目日志
    
    Args:
        project_name: 项目名称
        log_dir: 日志目录
        
    Returns:
        主日志器
    """
    config = {
        'level': 4,  # INFO级别
        'console': {
            'enabled': True,
            'level': 4  # INFO级别
        },
        'file': {
            'enabled': True,
            'level': 6,  # DEBUG级别
            'directory': log_dir,
            'rotation_type': 'time',
            'when': 'midnight',
            'backup_count': 30
        }
    }
    
    # 配置根日志器，确保所有模块使用统一格式
    LoggerManager._configure_root_logger(config)
    
    return LoggerManager.get_logger(project_name, config)


def init_logging_system(log_dir: str = "logs", console_level: int = 4, file_level: int = 6):
    """
    初始化日志系统，配置根日志器使用电信格式
    
    在项目启动时调用此函数，确保所有使用 logging.getLogger() 的模块
    都使用统一的电信日志格式
    
    Args:
        log_dir: 日志目录
        console_level: 控制台日志级别 (0-7)
        file_level: 文件日志级别 (0-7)
    """
    config = {
        'level': min(console_level, file_level),
        'console': {
            'enabled': True,
            'level': console_level
        },
        'file': {
            'enabled': True,
            'level': file_level,
            'directory': log_dir,
            'rotation_type': 'time',
            'when': 'midnight',
            'backup_count': 30
        }
    }
    
    LoggerManager._configure_root_logger(config)


def get_module_logger(module_name: str) -> logging.Logger:
    """
    获取模块日志器
    
    Args:
        module_name: 模块名称
        
    Returns:
        日志器
    """
    return LoggerManager.get_logger(module_name)


def log_function_call(logger: logging.Logger):
    """
    函数调用日志装饰器
    
    Args:
        logger: 日志器
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_name = func.__name__
            logger.debug(f"调用函数: {func_name}, args={args}, kwargs={kwargs}")
            
            try:
                start_time = datetime.now()
                result = func(*args, **kwargs)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                logger.debug(f"函数 {func_name} 执行完成，耗时: {duration:.4f}秒")
                return result
                
            except Exception as e:
                logger.error(f"函数 {func_name} 执行失败: {e}")
                raise
        
        return wrapper
    return decorator