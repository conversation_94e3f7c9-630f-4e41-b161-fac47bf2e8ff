"""
模型预测器

负责模型加载和预测，支持批量预测和结果后处理
"""

import pandas as pd
import numpy as np
import joblib
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
import yaml

class ModelPredictor:
    """模型预测器类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化模型预测器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 加载的模型缓存
        self.loaded_models = {}
        
        # 路径配置
        self.models_dir = self.config.get('model', {}).get('trained_models_dir', 'models/trained')
        self.metadata_dir = self.config.get('model', {}).get('metadata_dir', 'models/metadata')
        self.predictions_dir = self.config.get('data', {}).get('predictions_dir', 'data/predictions')
        
        # 创建预测结果目录
        os.makedirs(self.predictions_dir, exist_ok=True)
        
        # 业务规则配置
        self.min_lag_percentage = self.config.get('business_rules', {}).get('min_lag_percentage', 0.5)
        self.max_lag_percentage = self.config.get('business_rules', {}).get('max_lag_percentage', 3.0)
        self.risk_threshold = self.config.get('business_rules', {}).get('risk_threshold', 0.5)
        # 目标与校准开关
        self._hard_target_type = 'lag_percentage'
        self._hard_enable_calibration = True
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/config.yaml')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"警告: 无法加载配置文件: {e}")
            return {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置统一日志: 复用根日志器(已在项目启动处初始化)"""
        logger = logging.getLogger(__name__)
        try:
            from src.utils.logger import TelecomLogFormatter
            for h in list(logger.handlers):
                if isinstance(h, logging.StreamHandler) and not isinstance(getattr(h, 'formatter', None), TelecomLogFormatter):
                    logger.removeHandler(h)
        except Exception:
            pass
        logger.propagate = True
        return logger
    
    def list_available_models(self) -> List[Dict[str, Any]]:
        """
        列出可用的模型
        
        Returns:
            模型信息列表
        """
        available_models = []
        
        try:
            # 遍历模型目录
            model_files = Path(self.models_dir).glob('*.pkl')
            
            for model_file in model_files:
                # 查找对应的元数据文件
                metadata_file = Path(self.metadata_dir) / (model_file.stem + '_metadata.json')
                
                if metadata_file.exists():
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    model_info = {
                        'model_file': str(model_file),
                        'model_name': metadata.get('model_name', model_file.stem),
                        'local_network': metadata.get('local_network'),
                        'model_type': metadata.get('model_type'),
                        'save_timestamp': metadata.get('save_timestamp'),
                        'metadata': metadata
                    }
                    
                    available_models.append(model_info)
                else:
                    # 没有元数据的模型
                    model_info = {
                        'model_file': str(model_file),
                        'model_name': model_file.stem,
                        'local_network': None,
                        'model_type': 'Unknown',
                        'save_timestamp': None,
                        'metadata': None
                    }
                    
                    available_models.append(model_info)
            
            self.logger.info(f"找到 {len(available_models)} 个可用模型")
            
        except Exception as e:
            self.logger.error(f"列出可用模型失败: {e}")
        
        return available_models
    
    def load_model(self, model_path: str, cache_key: str = None) -> Any:
        """
        加载模型
        
        Args:
            model_path: 模型文件路径
            cache_key: 缓存键名
            
        Returns:
            加载的模型
        """
        if cache_key is None:
            cache_key = Path(model_path).stem
        
        # 检查缓存
        if cache_key in self.loaded_models:
            self.logger.info(f"从缓存加载模型: {cache_key}")
            return self.loaded_models[cache_key]
        
        try:
            # 加载模型
            model = joblib.load(model_path)
            
            # 缓存模型
            self.loaded_models[cache_key] = model
            
            self.logger.info(f"成功加载模型: {model_path}")
            
            return model
            
        except Exception as e:
            self.logger.error(f"加载模型失败 {model_path}: {e}")
            raise
    
    def load_model_by_local_network(self, local_network: str, 
                                  timestamp: str = None) -> Tuple[Any, Dict]:
        """
        根据本地网加载模型
        
        Args:
            local_network: 本地网标识
            timestamp: 时间戳，如果为None则加载最新的
            
        Returns:
            (模型, 元数据)
        """
        available_models = self.list_available_models()
        
        # 筛选指定本地网的模型
        network_models = [m for m in available_models if m['local_network'] == local_network]
        
        if not network_models:
            raise ValueError(f"未找到本地网 {local_network} 的模型")
        
        # 选择模型
        if timestamp:
            # 查找指定时间戳的模型
            target_model = None
            for model in network_models:
                if model['save_timestamp'] == timestamp:
                    target_model = model
                    break
            
            if not target_model:
                raise ValueError(f"未找到本地网 {local_network} 时间戳 {timestamp} 的模型")
        else:
            # 选择最新的模型
            network_models.sort(key=lambda x: x['save_timestamp'] or '', reverse=True)
            target_model = network_models[0]
        
        # 加载模型 (保持方法内缩进)
        model = self.load_model(target_model['model_file'])

        self.logger.info(f"为本地网 {local_network} 加载了模型: {target_model['model_name']}")

        return model, target_model['metadata']
    
    def predict(self, model: Any, X: pd.DataFrame) -> np.ndarray:
        """
        执行预测
        
        Args:
            model: 训练好的模型
            X: 特征数据
            
        Returns:
            预测结果数组
        """
        try:
            # 封装字典 {base_model, calibrator, target_type, calibration}
            base_model = model
            calibrator = None
            target_type = self._hard_target_type
            if isinstance(model, dict) and 'base_model' in model:
                base_model = model.get('base_model')
                calibrator = model.get('calibrator')
                target_type = model.get('target_type', target_type)
            predictions = base_model.predict(X)
            if calibrator is not None and target_type == 'lag_percentage':
                try:
                    predictions = calibrator.transform(predictions)
                except Exception:
                    try:
                        predictions = calibrator.predict(predictions)
                    except Exception as ce:
                        self.logger.warning(f"应用校准器失败: {ce}")
            self.logger.info(f"完成预测，样本数: {len(X)}")
            return predictions
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def apply_business_rules(self, predictions: np.ndarray, 
                           total_volumes: np.ndarray = None) -> np.ndarray:
        """
        应用业务规则约束
        
        Args:
            predictions: 原始预测结果
            total_volumes: 总量数组（用于计算百分比）
            
        Returns:
            应用约束后的预测结果
        """
        # 向量化裁剪: 先算百分比, 再 clip, 再反推预测值
        if total_volumes is None:
            return predictions
        total_volumes = np.asarray(total_volumes, dtype=float)
        preds = np.asarray(predictions, dtype=float).copy()
        valid_mask = (total_volumes > 0) & np.isfinite(total_volumes) & np.isfinite(preds)
        pct = np.full_like(preds, np.nan)
        pct[valid_mask] = preds[valid_mask] / total_volumes[valid_mask] * 100
        before_max = np.nanmax(pct) if np.any(valid_mask) else None
        before_min = np.nanmin(pct) if np.any(valid_mask) else None
        clipped = np.clip(pct, self.min_lag_percentage, self.max_lag_percentage)
        # 统计被裁剪
        high_clip = np.sum((~np.isnan(pct)) & (pct > self.max_lag_percentage))
        low_clip = np.sum((~np.isnan(pct)) & (pct < self.min_lag_percentage))
        adjusted = preds.copy()
        adjusted[valid_mask] = clipped[valid_mask] / 100 * total_volumes[valid_mask]
        if high_clip or low_clip:
            self.logger.info(
                f"业务约束裁剪: high>{self.max_lag_percentage}% {high_clip} 条, low<{self.min_lag_percentage}% {low_clip} 条; pct范围 {before_min:.4f}~{before_max:.4f}"
            )
        return adjusted
    
    def batch_predict(self, model: Any, data_dict: Dict[str, pd.DataFrame],
                     apply_constraints: bool = True) -> Dict[str, pd.DataFrame]:
        """
        批量预测
        
        Args:
            model: 训练好的模型
            data_dict: 数据字典
            apply_constraints: 是否应用业务规则约束
            
        Returns:
            预测结果字典
        """
        prediction_results = {}
        
        for data_key, df in data_dict.items():
            try:
                self.logger.info(f"开始预测数据集: {data_key}")
                
                # 准备特征数据 - 不排除原始字段，只排除目标变量和文件相关字段
                exclude_columns = [
                    'lag_amount', 'current_lag_amount',  # 目标变量
                    'order_id', 'remind_time', 'source_file', 'file_local_network', 'file_time'  # 文件相关字段
                ]
                
                # NOTE: 保留 current_total_volume 等反推用列在 df 中，但不进入 X
                feature_columns = [col for col in df.columns if col not in exclude_columns]
                X = df[feature_columns].copy()

                # === 特征对齐与数据类型修复 ===
                # 1. 若模型是封装字典，定位实际底层模型
                base_model = model.get('base_model') if isinstance(model, dict) and 'base_model' in model else model

                # 2. 获取训练期特征列表 (基于底层模型 feature_names_in_)
                expected_feature_names = getattr(base_model, 'feature_names_in_', None)
                if expected_feature_names is not None and not isinstance(expected_feature_names, list):
                    try:
                        expected_feature_names = list(expected_feature_names)
                    except Exception:
                        expected_feature_names = None

                # 3. 先处理 object dtype 列：尝试安全转换，否则剔除
                object_cols = [c for c in X.columns if X[c].dtype == 'object']
                dropped_object_cols = []
                if object_cols:
                    for oc in object_cols:
                        # 若列值全部是数字字符串，转换为 float
                        sample_vals = X[oc].dropna().head(10).astype(str)
                        if len(sample_vals):
                            if all(v.replace('.', '', 1).isdigit() for v in sample_vals if v not in ('', 'nan', 'None')):
                                try:
                                    X[oc] = pd.to_numeric(X[oc], errors='coerce')
                                    continue
                                except Exception:
                                    pass
                        # 若唯一值数较小，尝试类别编码为整数索引
                        nunique = X[oc].nunique(dropna=True)
                        if 0 < nunique <= 50:
                            mapping = {val: idx for idx, val in enumerate(X[oc].dropna().unique())}
                            X[oc] = X[oc].map(mapping).astype(float)
                        else:
                            # 删除高基数 object 列，防止 LightGBM 抛错
                            dropped_object_cols.append(oc)
                    if dropped_object_cols:
                        X.drop(columns=dropped_object_cols, inplace=True, errors='ignore')
                        self.logger.warning(
                            f"数据集 {data_key}: 删除高基数/无法转换 object 列 {len(dropped_object_cols)} 个: {dropped_object_cols[:6]}{'...' if len(dropped_object_cols)>6 else ''}" )

                # 4. 按训练特征列表对齐 (如存在)
                if expected_feature_names:
                    # Fast path: 列集合完全一致且无 object 类型 -> 直接重排即可
                    if set(X.columns) == set(expected_feature_names) and not any(X.dtypes == 'object'):
                        try:
                            X = X[expected_feature_names]
                            self.logger.debug(f"数据集 {data_key}: fast path 特征对齐 (无需逐列补/删)")
                        except Exception:
                            pass
                    else:
                        current_set = set(X.columns)
                        expected_set = set(expected_feature_names)
                        missing = [c for c in expected_feature_names if c not in current_set]
                        extra = [c for c in X.columns if c not in expected_set]
                        # 补齐缺失特征
                        for mcol in missing:
                            X[mcol] = 0.0
                        # 移除额外特征
                        if extra:
                            X.drop(columns=extra, inplace=True, errors='ignore')
                        # 重排列顺序
                        try:
                            X = X[expected_feature_names]
                        except Exception:
                            pass
                        if missing:
                            self.logger.warning(
                                f"数据集 {data_key}: 补齐缺失特征 {len(missing)} 个: {missing[:10]}{'...' if len(missing)>10 else ''}" )
                        if extra:
                            self.logger.info(
                                f"数据集 {data_key}: 移除未在训练中出现特征 {len(extra)} 个: {extra[:10]}{'...' if len(extra)>10 else ''}" )
                else:
                    self.logger.warning(f"数据集 {data_key}: 模型缺少 feature_names_in_，跳过特征严格对齐（可能为旧模型或基础估计器）")
                
                # 执行预测
                predictions = self.predict(model, X)

                # 判定模型是否为百分比目标
                target_type = None
                if isinstance(model, dict):
                    target_type = model.get('target_type')
                if target_type is None:
                    target_type = self._hard_target_type

                # 若是百分比目标, predictions 表示百分比 (%), 需要裁剪后再反推 amount
                if target_type == 'lag_percentage':
                    # 选择用于反推的总量列 (硬编码优先级): current_total_volume -> total_volume -> (0.6 prev1 + 0.4 prev2) -> prev1 -> prev2
                    denom_col = None
                    denom_source = 'none'
                    # 初始化缺失标记列
                    if 'flag_missing_total' not in df.columns:
                        df['flag_missing_total'] = 0
                    if 'current_total_volume' in df.columns:
                        denom_col = 'current_total_volume'; denom_source = 'current_total_volume'
                    elif 'total_volume' in df.columns:
                        denom_col = 'total_volume'; denom_source = 'total_volume'
                    else:
                        prev1 = df['prev_1m_total_volume'] if 'prev_1m_total_volume' in df.columns else None
                        prev2 = df['prev_2m_total_volume'] if 'prev_2m_total_volume' in df.columns else None
                        if prev1 is not None and prev2 is not None:
                            est_total = 0.6 * prev1.astype(float) + 0.4 * prev2.astype(float)
                            df['estimated_total_volume'] = est_total
                            denom_col = 'estimated_total_volume'; denom_source = 'est_w_avg_0.6_0.4'
                        elif prev1 is not None:
                            df['estimated_total_volume'] = prev1.astype(float)
                            denom_col = 'estimated_total_volume'; denom_source = 'est_prev1_only'
                        elif prev2 is not None:
                            df['estimated_total_volume'] = prev2.astype(float)
                            denom_col = 'estimated_total_volume'; denom_source = 'est_prev2_only'
                    pct_preds = predictions.astype(float)
                    # 直接 clip 百分比 (校准后输出) —— 不再调用 apply_business_rules 里重复裁剪
                    pct_clipped = np.clip(pct_preds, self.min_lag_percentage, self.max_lag_percentage)
                    if denom_col is not None:
                        total_vals = df[denom_col].replace(0, np.nan).astype(float).values
                        amount_preds = pct_clipped / 100.0 * total_vals
                        # 对无法计算 (nan) 的置 0 并打标
                        missing_mask = ~np.isfinite(amount_preds)
                        if missing_mask.any():
                            amount_preds[missing_mask] = 0.0
                            df.loc[missing_mask, 'flag_missing_total'] = 1
                    else:
                        amount_preds = np.zeros_like(pct_clipped)
                        df['flag_missing_total'] = 1
                    df['denom_source'] = denom_source
                    predictions_amount = amount_preds
                    predictions_percentage = pct_clipped
                else:
                    predictions_amount = predictions
                    predictions_percentage = None
                
                # 若目标为 lag_amount 才需要额外业务规则裁剪；lag_percentage 已在上面裁剪
                if target_type != 'lag_percentage' and apply_constraints:
                    volume_col = None
                    for cand in ['current_total_volume','total_volume']:
                        if cand in df.columns:
                            volume_col = cand; break
                    if volume_col is not None:
                        predictions_amount = self.apply_business_rules(
                            predictions_amount, df[volume_col].replace(0, np.nan).values
                        )
                    else:
                        self.logger.warning(f"数据集 {data_key}: 未找到可用于业务约束的总量列 (amount 模式)")
                
                # 创建结果DataFrame - 保留所有原始字段
                result_df = df.copy()
                result_df['predicted_lag_amount'] = predictions_amount
                
                # 计算预测百分比 (安全防护)
                if predictions_percentage is not None:
                    result_df['predicted_lag_percentage'] = predictions_percentage
                else:
                    denom_col = 'current_total_volume' if 'current_total_volume' in result_df.columns else None
                    if denom_col:
                        with np.errstate(divide='ignore', invalid='ignore'):
                            result_df['predicted_lag_percentage'] = (
                                result_df['predicted_lag_amount'] / result_df[denom_col].replace(0, np.nan)
                            ) * 100
                            result_df['predicted_lag_percentage'].replace([np.inf, -np.inf], np.nan, inplace=True)
                # 统计日志：百分比列有效值数量
                if 'predicted_lag_percentage' in result_df.columns:
                    valid_pct_cnt = result_df['predicted_lag_percentage'].replace([np.inf,-np.inf], np.nan).dropna().shape[0]
                    self.logger.info(f"数据集 {data_key}: predicted_lag_percentage 有效值 {valid_pct_cnt}/{len(result_df)}")
                
                prediction_results[data_key] = result_df
                
                self.logger.info(f"数据集 {data_key} 预测完成")
                
            except Exception as e:
                self.logger.error(f"数据集 {data_key} 预测失败: {e}")
                prediction_results[data_key] = pd.DataFrame()
        
        return prediction_results
    
    def predict_by_local_network(self, data_dict: Dict[str, pd.DataFrame],
                               apply_constraints: bool = True) -> Dict[str, pd.DataFrame]:
        """
        按本地网预测（每个本地网使用自己的模型）
        
        Args:
            data_dict: 本地网为键，DataFrame为值的字典
            apply_constraints: 是否应用业务规则约束
            
        Returns:
            预测结果字典
        """
        prediction_results = {}
        
        for local_network, df in data_dict.items():
            try:
                self.logger.info(f"开始预测本地网: {local_network}")
                
                # 加载对应本地网的模型
                model, metadata = self.load_model_by_local_network(local_network)
                
                # 执行预测
                single_result = self.batch_predict(
                    model, {local_network: df}, apply_constraints
                )
                
                prediction_results[local_network] = single_result[local_network]
                
                self.logger.info(f"本地网 {local_network} 预测完成")
                
            except Exception as e:
                self.logger.error(f"本地网 {local_network} 预测失败: {e}")
                prediction_results[local_network] = pd.DataFrame()
        
        return prediction_results
    
    def save_predictions(self, predictions_dict: Dict[str, pd.DataFrame],
                        timestamp: str = None, user_mappings: Dict[str, list] = None) -> Dict[str, str]:
        """
        保存预测结果
        
        Args:
            predictions_dict: 预测结果字典
            timestamp: 时间戳
            user_mappings: 原始用户ID映射
            
        Returns:
            保存的文件路径字典
        """
        if timestamp is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        saved_files = {}

        for data_key, df in predictions_dict.items():
            try:
                if df.empty:
                    self.logger.warning(f"数据集 {data_key} 为空，跳过保存")
                    continue
                
                # 构建文件名
                filename = f"CC_DAT_AI_PRE_OUT_{data_key}_{timestamp}.txt"
                filepath = os.path.join(self.predictions_dir, filename)
                
                # 准备输出DataFrame，按照设计文档格式
                output_df = pd.DataFrame()
                
                # 映射字段（输出统一采用 prev_* 命名；保留对旧 lag_* 源列的兼容作为候选）
                field_mapping = {
                    'user': ['user', 'product_instance_id'],
                    'year': ['year'],
                    'month': ['month'],
                    'local_network': ['local_network'],
                    'business_type': ['business_type'],
                    'volume_type': ['volume_type'],
                    'gear_label': ['gear_label'],
                    'prev_1m_total_volume': ['prev_1m_total_volume'],
                    'prev_1m_trigger_usage': ['prev_1m_trigger_usage'],
                    'prev_1m_lag_amount': ['prev_1m_lag_amount'],
                    'prev_2m_total_volume': ['prev_2m_total_volume'],
                    'prev_2m_trigger_usage': ['prev_2m_trigger_usage'],
                    'prev_2m_lag_amount': ['prev_2m_lag_amount'],
                    'predicted_lag_amount': ['predicted_lag_amount']
                }
                
                # 构建输出数据
                for output_col, possible_cols in field_mapping.items():
                    selected_source = None
                    for col in possible_cols:
                        if col in df.columns:
                            output_df[output_col] = df[col]
                            selected_source = col
                            if col != possible_cols[0]:
                                self.logger.info(
                                    f"数据集 {data_key}: 输出列 {output_col} 使用兼容字段 {col} (首选 {possible_cols[0]} 缺失)"
                                )
                            break
                    else:
                        # 如果字段不存在，填充默认值
                        if output_col == 'user':
                            # 优先级：用户映射 > product_instance_id > user > 生成默认值
                            if user_mappings and data_key in user_mappings:
                                # 使用原始用户ID映射
                                user_ids = user_mappings[data_key]
                                if len(user_ids) >= len(df):
                                    output_df[output_col] = user_ids[:len(df)]
                                else:
                                    # 如果原始ID不够，用原始ID + 生成ID补足
                                    extended_ids = user_ids + [f"USER_{data_key}_" + str(i).zfill(6) 
                                                              for i in range(len(user_ids), len(df))]
                                    output_df[output_col] = extended_ids
                            elif 'product_instance_id' in df.columns:
                                output_df[output_col] = df['product_instance_id']
                            elif 'user' in df.columns:
                                output_df[output_col] = df['user']
                            else:
                                output_df[output_col] = f"USER_{data_key}_" + df.index.astype(str).str.zfill(6)
                        elif output_col == 'local_network':
                            output_df[output_col] = data_key  # 使用数据集键作为本地网
                        elif 'predicted' not in output_col:
                            output_df[output_col] = 0  # 其他字段填充0
                            self.logger.warning(
                                f"数据集 {data_key}: 输出列 {output_col} 无匹配字段且置0 (候选={possible_cols})"
                            )
                
                # 保存为管道分隔的文件
                output_df.to_csv(filepath, sep='|', index=False, encoding='utf-8')
                
                saved_files[data_key] = filepath
                
                self.logger.info(f"预测结果已保存: {filepath}")
                
            except Exception as e:
                self.logger.error(f"保存数据集 {data_key} 的预测结果失败: {e}")
        
        return saved_files
    
    def generate_prediction_summary(self, predictions_dict: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        生成预测摘要
        
        Args:
            predictions_dict: 预测结果字典
            
        Returns:
            预测摘要
        """
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_datasets': len(predictions_dict),
            'dataset_summaries': {},
            'overall_statistics': {}
        }
        
        all_predictions = []
        total_samples = 0
        
        for data_key, df in predictions_dict.items():
            if df.empty:
                continue

            dataset_summary = {
                'sample_count': len(df),
                'features_count': len([col for col in df.columns if 'predicted' not in col]),
                'has_predictions': 'predicted_lag_amount' in df.columns
            }

            # 预测数值统计 (强制转数值并过滤 NaN)
            if 'predicted_lag_amount' in df.columns:
                pred_series = pd.to_numeric(df['predicted_lag_amount'], errors='coerce')
                valid_amount = pred_series[np.isfinite(pred_series)]
                if len(valid_amount):
                    dataset_summary.update({
                        'prediction_mean': float(valid_amount.mean()),
                        'prediction_std': float(valid_amount.std()),
                        'prediction_min': float(valid_amount.min()),
                        'prediction_max': float(valid_amount.max()),
                        'valid_amount_count': int(len(valid_amount)),
                        'nan_amount_count': int(len(pred_series) - len(valid_amount))
                    })
                    all_predictions.extend(valid_amount.tolist())
                else:
                    dataset_summary.update({
                        'prediction_mean': None,
                        'prediction_std': None,
                        'prediction_min': None,
                        'prediction_max': None,
                        'valid_amount_count': 0,
                        'nan_amount_count': int(len(pred_series))
                    })

            # 百分比统计
            if 'predicted_lag_percentage' in df.columns:
                pct_series = pd.to_numeric(df['predicted_lag_percentage'], errors='coerce').replace([np.inf, -np.inf], np.nan)
                valid_pct = pct_series.dropna()
                if len(valid_pct):
                    pct_array = valid_pct.values
                    in_range_mask = (pct_array >= self.min_lag_percentage) & (pct_array <= self.max_lag_percentage)
                    dist_stats = {
                        'min': float(np.min(pct_array)),
                        'p25': float(np.percentile(pct_array, 25)),
                        'median': float(np.percentile(pct_array, 50)),
                        'p75': float(np.percentile(pct_array, 75)),
                        'p95': float(np.percentile(pct_array, 95)),
                        'max': float(np.max(pct_array))
                    }
                    dataset_summary.update({
                        'percentage_mean': float(valid_pct.mean()),
                        'percentage_in_range': float(in_range_mask.mean() * 100),
                        'valid_percentage_count': int(len(valid_pct)),
                        'clip_high_count': int((pct_array > self.max_lag_percentage).sum()),
                        'clip_low_count': int((pct_array < self.min_lag_percentage).sum()),
                        'percentage_distribution': dist_stats
                    })
                else:
                    dataset_summary.update({
                        'percentage_mean': None,
                        'percentage_in_range': 0.0,
                        'valid_percentage_count': 0,
                        'clip_high_count': 0,
                        'clip_low_count': 0,
                        'percentage_distribution': None
                    })

            # 缺失总量标记统计
            if 'flag_missing_total' in df.columns:
                missing_cnt = int(df['flag_missing_total'].fillna(0).sum())
                dataset_summary['missing_total_rows'] = missing_cnt
                dataset_summary['missing_total_ratio'] = float(missing_cnt / len(df)) if len(df) else 0.0
            if 'denom_source' in df.columns:
                denom_counts = df['denom_source'].value_counts(dropna=False)
                dataset_summary['denom_source_counts'] = denom_counts.to_dict()
                dataset_summary['denom_source_ratio'] = {k: float(v/len(df)) for k, v in denom_counts.items()}

            summary['dataset_summaries'][data_key] = dataset_summary
            total_samples += len(df)
        
        # 整体统计
        if all_predictions:
            all_pred_array = np.array(all_predictions, dtype=float)
            valid_mask = np.isfinite(all_pred_array)
            if valid_mask.any():
                v = all_pred_array[valid_mask]
                summary['overall_statistics'] = {
                    'total_samples': total_samples,
                    'overall_prediction_mean': float(v.mean()),
                    'overall_prediction_std': float(v.std()),
                    'overall_prediction_min': float(v.min()),
                    'overall_prediction_max': float(v.max()),
                    'overall_valid_amount_count': int(len(v))
                }
            else:
                summary['overall_statistics'] = {
                    'total_samples': total_samples,
                    'overall_prediction_mean': None,
                    'overall_prediction_std': None,
                    'overall_prediction_min': None,
                    'overall_prediction_max': None,
                    'overall_valid_amount_count': 0
                }

        # 汇总整体百分比（若任何数据集包含 predicted_lag_percentage）
        try:
            pct_series_all = []
            for df in predictions_dict.values():
                if 'predicted_lag_percentage' in df.columns:
                    s = pd.to_numeric(df['predicted_lag_percentage'], errors='coerce').replace([np.inf,-np.inf], np.nan).dropna()
                    if not s.empty:
                        pct_series_all.append(s)
            if pct_series_all:
                concat_pct = pd.concat(pct_series_all)
                pct_vals = concat_pct.values
                in_range = (pct_vals >= self.min_lag_percentage) & (pct_vals <= self.max_lag_percentage)
                summary.setdefault('overall_statistics', {})['overall_percentage_mean'] = float(concat_pct.mean())
                summary['overall_statistics']['overall_percentage_in_range'] = float(in_range.mean() * 100)
                summary['overall_statistics']['overall_percentage_count'] = int(concat_pct.shape[0])
        except Exception:
            pass
        
        return summary
    
    def clear_model_cache(self):
        """清除模型缓存"""
        self.loaded_models.clear()
        self.logger.info("模型缓存已清除")