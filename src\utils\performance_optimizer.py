"""
性能优化工具

提供内存优化、并发处理优化等功能
"""

import gc
import psutil
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from functools import wraps
import time
import threading
from .base_component import BaseComponent


class PerformanceOptimizer(BaseComponent):
    """
    性能优化器
    
    提供内存优化、数据类型优化、并发处理等功能
    """
    
    def __init__(self, config_path: str = None):
        """初始化性能优化器"""
        super().__init__(config_path, "PerformanceOptimizer")
        self.memory_threshold = self.get_config_value('performance.memory_threshold_mb', 1024)
        self.gc_frequency = self.get_config_value('performance.gc_frequency', 100)
        self.operation_count = 0
    
    def optimize_dataframe_memory(self, df: pd.DataFrame, 
                                 aggressive: bool = False) -> pd.DataFrame:
        """
        优化DataFrame内存使用
        
        Args:
            df: 待优化的DataFrame
            aggressive: 是否使用激进优化
            
        Returns:
            优化后的DataFrame
        """
        if df.empty:
            return df
        
        original_memory = df.memory_usage(deep=True).sum() / 1024**2
        self.logger.info(f"开始内存优化，原始内存使用: {original_memory:.2f} MB")
        
        df_optimized = df.copy()
        
        # 优化数值类型
        for col in df_optimized.select_dtypes(include=['int64']).columns:
            col_min = df_optimized[col].min()
            col_max = df_optimized[col].max()
            
            if col_min >= 0:  # 无符号整数
                if col_max < 255:
                    df_optimized[col] = df_optimized[col].astype(np.uint8)
                elif col_max < 65535:
                    df_optimized[col] = df_optimized[col].astype(np.uint16)
                elif col_max < 4294967295:
                    df_optimized[col] = df_optimized[col].astype(np.uint32)
            else:  # 有符号整数
                if col_min > -128 and col_max < 127:
                    df_optimized[col] = df_optimized[col].astype(np.int8)
                elif col_min > -32768 and col_max < 32767:
                    df_optimized[col] = df_optimized[col].astype(np.int16)
                elif col_min > -2147483648 and col_max < 2147483647:
                    df_optimized[col] = df_optimized[col].astype(np.int32)
        
        # 优化浮点类型
        for col in df_optimized.select_dtypes(include=['float64']).columns:
            if not aggressive:
                # 保守优化：只在不损失精度的情况下转换
                if df_optimized[col].dtype == 'float64':
                    float32_test = df_optimized[col].astype(np.float32)
                    if np.allclose(df_optimized[col], float32_test, equal_nan=True):
                        df_optimized[col] = float32_test
            else:
                # 激进优化：直接转换为float32
                df_optimized[col] = df_optimized[col].astype(np.float32)
        
        # 优化字符串类型为category
        for col in df_optimized.select_dtypes(include=['object']).columns:
            if df_optimized[col].dtype == 'object':
                unique_ratio = len(df_optimized[col].unique()) / len(df_optimized[col])
                if unique_ratio < 0.5:  # 如果唯一值比例小于50%，转换为category
                    df_optimized[col] = df_optimized[col].astype('category')
        
        optimized_memory = df_optimized.memory_usage(deep=True).sum() / 1024**2
        memory_reduction = (original_memory - optimized_memory) / original_memory * 100
        
        self.logger.info(f"内存优化完成，优化后内存: {optimized_memory:.2f} MB，"
                        f"减少: {memory_reduction:.1f}%")
        
        return df_optimized
    
    def monitor_memory_usage(self):
        """监控内存使用情况"""
        memory_info = psutil.virtual_memory()
        memory_usage_mb = memory_info.used / 1024**2
        memory_percent = memory_info.percent
        
        self.logger.debug(f"当前内存使用: {memory_usage_mb:.2f} MB ({memory_percent:.1f}%)")
        
        if memory_usage_mb > self.memory_threshold:
            self.logger.warning(f"内存使用超过阈值 {self.memory_threshold} MB，触发垃圾回收")
            self.force_garbage_collection()
        
        return memory_usage_mb, memory_percent
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        before_memory = psutil.virtual_memory().used / 1024**2
        
        # 执行垃圾回收
        collected = gc.collect()
        
        after_memory = psutil.virtual_memory().used / 1024**2
        freed_memory = before_memory - after_memory
        
        self.logger.info(f"垃圾回收完成，回收对象: {collected}，释放内存: {freed_memory:.2f} MB")
    
    def auto_gc_decorator(self, func):
        """自动垃圾回收装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            self.operation_count += 1
            
            # 定期执行垃圾回收
            if self.operation_count % self.gc_frequency == 0:
                self.monitor_memory_usage()
            
            return func(*args, **kwargs)
        
        return wrapper
    
    def optimize_pandas_settings(self):
        """优化pandas全局设置"""
        # 设置pandas选项以提高性能
        pd.set_option('mode.chained_assignment', None)  # 禁用链式赋值警告
        pd.set_option('compute.use_bottleneck', True)   # 启用bottleneck加速
        pd.set_option('compute.use_numexpr', True)      # 启用numexpr加速
        
        self.logger.info("已优化pandas全局设置")
    
    def chunk_dataframe(self, df: pd.DataFrame, chunk_size: int = None) -> List[pd.DataFrame]:
        """
        将大DataFrame分块处理
        
        Args:
            df: 待分块的DataFrame
            chunk_size: 块大小，如果为None则自动计算
            
        Returns:
            DataFrame块列表
        """
        if chunk_size is None:
            chunk_size = self.get_config_value('processing.chunk_size', 10000)
        
        chunks = []
        total_rows = len(df)
        
        for i in range(0, total_rows, chunk_size):
            chunk = df.iloc[i:i + chunk_size].copy()
            chunks.append(chunk)
        
        self.logger.info(f"DataFrame分块完成，总行数: {total_rows}，块数: {len(chunks)}")
        return chunks
    
    def get_optimal_worker_count(self, task_count: int = None) -> int:
        """
        获取最优工作线程数
        
        Args:
            task_count: 任务数量
            
        Returns:
            最优线程数
        """
        cpu_count = psutil.cpu_count(logical=False)  # 物理核心数
        logical_cpu_count = psutil.cpu_count(logical=True)  # 逻辑核心数
        
        # 基于CPU核心数和任务特性确定线程数
        if task_count is not None and task_count < cpu_count:
            optimal_workers = task_count
        else:
            # I/O密集型任务可以使用更多线程
            optimal_workers = min(logical_cpu_count, 
                                self.get_config_value('processing.max_workers', 4))
        
        self.logger.info(f"推荐工作线程数: {optimal_workers} "
                        f"(物理核心: {cpu_count}, 逻辑核心: {logical_cpu_count})")
        
        return optimal_workers
    
    def profile_function(self, func, *args, **kwargs):
        """
        性能分析函数
        
        Args:
            func: 待分析的函数
            *args, **kwargs: 函数参数
            
        Returns:
            (result, execution_time, memory_delta)
        """
        # 记录开始状态
        start_time = time.time()
        start_memory = psutil.virtual_memory().used / 1024**2
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 记录结束状态
        end_time = time.time()
        end_memory = psutil.virtual_memory().used / 1024**2
        
        execution_time = end_time - start_time
        memory_delta = end_memory - start_memory
        
        self.logger.info(f"函数 {func.__name__} 执行完成:")
        self.logger.info(f"  - 执行时间: {execution_time:.2f} 秒")
        self.logger.info(f"  - 内存变化: {memory_delta:+.2f} MB")
        
        return result, execution_time, memory_delta


# 全局性能优化器实例
performance_optimizer = PerformanceOptimizer()


def optimize_memory(aggressive: bool = False):
    """
    内存优化装饰器
    
    Args:
        aggressive: 是否使用激进优化
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 如果返回值是DataFrame，进行内存优化
            if isinstance(result, pd.DataFrame):
                result = performance_optimizer.optimize_dataframe_memory(result, aggressive)
            elif isinstance(result, dict):
                # 如果返回字典，优化其中的DataFrame
                for key, value in result.items():
                    if isinstance(value, pd.DataFrame):
                        result[key] = performance_optimizer.optimize_dataframe_memory(value, aggressive)
            
            return result
        
        return wrapper
    return decorator


def profile_performance(func):
    """性能分析装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        return performance_optimizer.profile_function(func, *args, **kwargs)[0]
    
    return wrapper
