"""
数据验证器

负责数据质量检查和格式验证
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
import re
from datetime import datetime
import yaml
import os

class DataValidator:
    """数据验证器类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化数据验证器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 定义验证规则
        self.validation_rules = {
            'required_columns': [
                'order_id', 'product_instance_id', 'local_network', 
                'business_type', 'gear_label', 'total_volume', 
                'trigger_usage', 'lag_amount',
                'remind_time', 'volume_type'
            ],
            'numeric_columns': [
                'gear_label', 'total_volume', 'trigger_usage', 'lag_amount'
            ],
            'categorical_columns': {
                'business_type': ['0', '1'],
                'volume_type': [1],
                'gear_label': [0.2, 0.4, 0.6, 0.8, 1.0]
            }
        }
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/config.yaml')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"警告: 无法加载配置文件: {e}")
            return {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        return logger
    
    def validate_columns(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """
        验证列结构
        
        Args:
            df: 数据DataFrame
            
        Returns:
            验证结果字典
        """
        result = {
            'missing_columns': [],
            'extra_columns': [],
            'valid': True
        }
        
        required_columns = set(self.validation_rules['required_columns'])
        actual_columns = set(df.columns)
        
        # 检查缺失的必需列
        missing_columns = required_columns - actual_columns
        if missing_columns:
            result['missing_columns'] = list(missing_columns)
            result['valid'] = False
            self.logger.error(f"缺失必需列: {missing_columns}")
        
        # 检查额外的列
        extra_columns = actual_columns - required_columns - {
            'source_file', 'file_local_network', 'file_time', 'lag_percentage'
        }
        if extra_columns:
            result['extra_columns'] = list(extra_columns)
            self.logger.info(f"发现额外列: {extra_columns}")
        
        if result['valid']:
            self.logger.info("列结构验证通过")
        
        return result
    
    def validate_data_types(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """
        验证数据类型
        
        Args:
            df: 数据DataFrame
            
        Returns:
            验证结果字典
        """
        result = {
            'type_errors': {},
            'valid': True
        }
        
        # 验证数值列
        numeric_columns = self.validation_rules['numeric_columns']
        for col in numeric_columns:
            if col in df.columns:
                non_numeric = df[col].apply(lambda x: not pd.api.types.is_numeric_dtype(type(x)) and pd.notna(x))
                if non_numeric.any():
                    error_count = non_numeric.sum()
                    result['type_errors'][col] = f"{error_count} 个非数值项"
                    result['valid'] = False
                    self.logger.error(f"列 {col} 存在 {error_count} 个非数值项")
        
        if result['valid']:
            self.logger.info("数据类型验证通过")
        
        return result
    
    def validate_value_ranges(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """
        验证数值范围
        
        Args:
            df: 数据DataFrame
            
        Returns:
            验证结果字典
        """
        result = {
            'range_errors': {},
            'valid': True
        }
        
        # 验证档位标签范围
        if 'gear_label' in df.columns:
            valid_gears = set(self.validation_rules['categorical_columns']['gear_label'])
            invalid_gears = df[df['gear_label'].notna() & 
                             ~df['gear_label'].isin(valid_gears)]
            
            if not invalid_gears.empty:
                result['range_errors']['gear_label'] = f"{len(invalid_gears)} 个无效档位标签"
                result['valid'] = False
                self.logger.error(f"发现 {len(invalid_gears)} 个无效档位标签")
        
        # 验证业务类型
        if 'business_type' in df.columns:
            valid_types = set(self.validation_rules['categorical_columns']['business_type'])
            invalid_types = df[df['business_type'].notna() & 
                              ~df['business_type'].isin(valid_types)]
            
            if not invalid_types.empty:
                result['range_errors']['business_type'] = f"{len(invalid_types)} 个无效业务类型"
                result['valid'] = False
                self.logger.error(f"发现 {len(invalid_types)} 个无效业务类型")
        
        # 验证数值的逻辑关系
        if all(col in df.columns for col in ['total_volume', 'trigger_usage']):
            # 触发使用量应该小于等于总量
            invalid_usage = df[df['trigger_usage'] > df['total_volume']]
            if not invalid_usage.empty:
                result['range_errors']['usage_logic'] = f"{len(invalid_usage)} 个触发使用量大于总量的记录"
                result['valid'] = False
                self.logger.error(f"发现 {len(invalid_usage)} 个触发使用量大于总量的记录")
        
        # 验证滞后量计算正确性
        if all(col in df.columns for col in ['total_volume', 'gear_label', 'trigger_usage', 'lag_amount']):
            # 新业务公式: h = trigger_usage - (total_volume * gear_label)
            expected_lag = df['trigger_usage'] - (df['total_volume'] * df['gear_label'])
            diff_threshold = 0.01  # 允许的误差范围

            # 仅接受直接匹配（允许正/负），不再接受绝对值兼容
            direct_ok = (abs(df['lag_amount'] - expected_lag) <= diff_threshold)
            mismatch_mask = ~direct_ok

            if mismatch_mask.any():
                error_count = mismatch_mask.sum()
                result['range_errors']['lag_calculation'] = f"{error_count} 条记录滞后量与新公式不匹配"
                result['valid'] = False
                self.logger.error(f"发现 {error_count} 条滞后量计算不匹配记录（按新公式 trigger_usage - total_volume*gear_label）")

            # 识别可能的旧绝对值逻辑残留：lag_amount 接近 |旧公式| 且与新公式不符
            old_formula = (df['total_volume'] * df['gear_label']) - df['trigger_usage']
            abs_old_match = (abs(df['lag_amount'] - old_formula.abs()) <= diff_threshold) & mismatch_mask
            legacy_count = abs_old_match.sum()
            if legacy_count > 0:
                self.logger.warning(f"检测到 {legacy_count} 条可能按旧公式并取绝对值得到的滞后量，请确认数据清洗流程是否已迁移")
        
        if result['valid']:
            self.logger.info("数值范围验证通过")
        
        return result
    
    def validate_business_logic(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """
        验证业务逻辑
        
        Args:
            df: 数据DataFrame
            
        Returns:
            验证结果字典
        """
        result = {
            'logic_errors': {},
            'valid': True
        }
        
        # 验证滞后量百分比范围
        if 'lag_percentage' in df.columns:
            min_lag = self.config.get('business_rules', {}).get('min_lag_percentage', 0.5)
            max_lag = self.config.get('business_rules', {}).get('max_lag_percentage', 3.0)
            
            out_of_range = df[(df['lag_percentage'] < min_lag) | 
                             (df['lag_percentage'] > max_lag)]
            
            if not out_of_range.empty:
                result['logic_errors']['lag_percentage_range'] = f"{len(out_of_range)} 个滞后量百分比超出范围"
                self.logger.warning(f"发现 {len(out_of_range)} 个滞后量百分比超出业务范围")
        
        # 验证时间逻辑
        if 'remind_time' in df.columns:
            # 检查未来时间
            future_times = df[df['remind_time'] > datetime.now()]
            if not future_times.empty:
                result['logic_errors']['future_times'] = f"{len(future_times)} 个未来时间记录"
                result['valid'] = False
                self.logger.error(f"发现 {len(future_times)} 个未来时间记录")
        
        # 验证本地网一致性
        if all(col in df.columns for col in ['local_network', 'file_local_network']):
            inconsistent = df[df['local_network'] != df['file_local_network']]
            if not inconsistent.empty:
                result['logic_errors']['network_inconsistency'] = f"{len(inconsistent)} 个本地网信息不一致"
                self.logger.warning(f"发现 {len(inconsistent)} 个本地网信息不一致")
        
        if result['valid']:
            self.logger.info("业务逻辑验证通过")
        
        return result
    
    def validate_data_quality(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        验证数据质量
        
        Args:
            df: 数据DataFrame
            
        Returns:
            数据质量报告
        """
        quality_report = {
            'total_records': len(df),
            'quality_issues': {},
            'quality_score': 0.0
        }
        
        # 计算缺失值比例
        missing_rates = df.isnull().sum() / len(df)
        high_missing_cols = missing_rates[missing_rates > 0.1].to_dict()
        
        if high_missing_cols:
            quality_report['quality_issues']['high_missing_rate'] = high_missing_cols
            self.logger.warning(f"高缺失率列: {high_missing_cols}")
        
        # 计算重复率
        if len(df) > 0:
            duplicate_rate = df.duplicated().sum() / len(df)
            if duplicate_rate > 0.05:  # 重复率超过5%
                quality_report['quality_issues']['high_duplicate_rate'] = duplicate_rate
                self.logger.warning(f"高重复率: {duplicate_rate:.2%}")
        
        # 计算数据分布偏斜
        numeric_columns = ['total_volume', 'trigger_usage', 'lag_amount']
        for col in numeric_columns:
            if col in df.columns and df[col].notna().sum() > 0:
                skewness = df[col].skew()
                if abs(skewness) > 2:  # 偏斜度绝对值大于2
                    if 'high_skewness' not in quality_report['quality_issues']:
                        quality_report['quality_issues']['high_skewness'] = {}
                    quality_report['quality_issues']['high_skewness'][col] = skewness
                    self.logger.warning(f"列 {col} 偏斜度较高: {skewness:.2f}")
        
        # 计算质量分数 (0-100)
        total_issues = len(quality_report['quality_issues'])
        quality_score = max(0, 100 - total_issues * 10)  # 每个问题扣10分
        quality_report['quality_score'] = quality_score
        
        self.logger.info(f"数据质量分数: {quality_score}/100")
        
        return quality_report
    
    def validate_completeness(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        验证数据完整性
        
        Args:
            df: 数据DataFrame
            
        Returns:
            完整性报告
        """
        completeness_report = {
            'record_completeness': {},
            'field_completeness': {},
            'overall_completeness': 0.0
        }
        
        # 按记录维度统计完整性
        required_cols = [col for col in self.validation_rules['required_columns'] 
                        if col in df.columns]
        
        if required_cols:
            complete_records = df[required_cols].notna().all(axis=1).sum()
            record_completeness = complete_records / len(df) if len(df) > 0 else 0
            completeness_report['record_completeness'] = {
                'complete_records': complete_records,
                'total_records': len(df),
                'completeness_rate': record_completeness
            }
        
        # 按字段维度统计完整性
        for col in required_cols:
            non_null_count = df[col].notna().sum()
            field_completeness = non_null_count / len(df) if len(df) > 0 else 0
            completeness_report['field_completeness'][col] = {
                'non_null_count': non_null_count,
                'completeness_rate': field_completeness
            }
        
        # 计算整体完整性
        if completeness_report['field_completeness']:
            avg_completeness = np.mean([
                v['completeness_rate'] 
                for v in completeness_report['field_completeness'].values()
            ])
            completeness_report['overall_completeness'] = avg_completeness
        
        self.logger.info(f"数据整体完整性: {completeness_report['overall_completeness']:.2%}")
        
        return completeness_report
    
    def validate_all(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        执行全面的数据验证
        
        Args:
            df: 数据DataFrame
            
        Returns:
            完整的验证报告
        """
        self.logger.info("开始数据验证")
        
        validation_report = {
            'timestamp': datetime.now().isoformat(),
            'total_records': len(df),
            'validation_results': {},
            'overall_valid': True
        }
        
        # 1. 列结构验证
        column_validation = self.validate_columns(df)
        validation_report['validation_results']['columns'] = column_validation
        if not column_validation['valid']:
            validation_report['overall_valid'] = False
        
        # 2. 数据类型验证
        type_validation = self.validate_data_types(df)
        validation_report['validation_results']['data_types'] = type_validation
        if not type_validation['valid']:
            validation_report['overall_valid'] = False
        
        # 3. 数值范围验证
        range_validation = self.validate_value_ranges(df)
        validation_report['validation_results']['value_ranges'] = range_validation
        if not range_validation['valid']:
            validation_report['overall_valid'] = False
        
        # 4. 业务逻辑验证
        logic_validation = self.validate_business_logic(df)
        validation_report['validation_results']['business_logic'] = logic_validation
        if not logic_validation['valid']:
            validation_report['overall_valid'] = False
        
        # 5. 数据质量评估
        quality_report = self.validate_data_quality(df)
        validation_report['validation_results']['data_quality'] = quality_report
        
        # 6. 完整性评估
        completeness_report = self.validate_completeness(df)
        validation_report['validation_results']['completeness'] = completeness_report
        
        # 生成验证总结
        if validation_report['overall_valid']:
            self.logger.info("数据验证通过")
        else:
            self.logger.error("数据验证失败，存在数据质量问题")
        
        return validation_report
    
    def generate_validation_summary(self, validation_report: Dict) -> str:
        """
        生成验证摘要
        
        Args:
            validation_report: 验证报告
            
        Returns:
            验证摘要字符串
        """
        summary_lines = [
            "=" * 50,
            "数据验证摘要报告",
            "=" * 50,
            f"验证时间: {validation_report['timestamp']}",
            f"总记录数: {validation_report['total_records']:,}",
            f"整体验证结果: {'通过' if validation_report['overall_valid'] else '失败'}",
            ""
        ]
        
        # 添加各项验证结果
        results = validation_report['validation_results']
        
        # 列结构
        if 'columns' in results:
            col_result = results['columns']
            summary_lines.append(f"列结构验证: {'通过' if col_result['valid'] else '失败'}")
            if col_result['missing_columns']:
                summary_lines.append(f"  缺失列: {', '.join(col_result['missing_columns'])}")
        
        # 数据类型
        if 'data_types' in results:
            type_result = results['data_types']
            summary_lines.append(f"数据类型验证: {'通过' if type_result['valid'] else '失败'}")
            if type_result['type_errors']:
                for col, error in type_result['type_errors'].items():
                    summary_lines.append(f"  {col}: {error}")
        
        # 数值范围
        if 'value_ranges' in results:
            range_result = results['value_ranges']
            summary_lines.append(f"数值范围验证: {'通过' if range_result['valid'] else '失败'}")
            if range_result['range_errors']:
                for field, error in range_result['range_errors'].items():
                    summary_lines.append(f"  {field}: {error}")
        
        # 数据质量
        if 'data_quality' in results:
            quality_result = results['data_quality']
            summary_lines.append(f"数据质量分数: {quality_result['quality_score']}/100")
        
        # 完整性
        if 'completeness' in results:
            completeness_result = results['completeness']
            summary_lines.append(f"数据完整性: {completeness_result['overall_completeness']:.2%}")
        
        summary_lines.append("=" * 50)
        
        return "\n".join(summary_lines)

    # 新增: 标记不满足验证逻辑的行，返回包含 filter_reason 列的 DataFrame
    def identify_invalid_rows(self, df: pd.DataFrame) -> pd.DataFrame:
        """识别不满足验证规则的行并给出原因标签。

        仅分析，不修改原 df。可能一个样本多个原因，用 ';' 连接。
        原始字段保持不变，新增列 filter_reason。
        """
        if df.empty:
            return pd.DataFrame()

        issues = []  # list of (index, reason_list)

        # 准备列存在性
        cols = df.columns

        # 1. 无效档位标签（非允许集合且非缺失）
        if 'gear_label' in cols:
            valid_gears = set(self.validation_rules['categorical_columns']['gear_label'])
            mask = df['gear_label'].notna() & ~df['gear_label'].isin(valid_gears)
            for idx in df[mask].index:
                issues.append((idx, 'invalid_gear_label'))

        # 2. 无效业务类型
        if 'business_type' in cols:
            valid_bt = set(self.validation_rules['categorical_columns']['business_type'])
            mask = df['business_type'].notna() & ~df['business_type'].isin(valid_bt)
            for idx in df[mask].index:
                issues.append((idx, 'invalid_business_type'))

        # 3. 触发使用量大于总量
        if all(c in cols for c in ['trigger_usage', 'total_volume']):
            mask = df['trigger_usage'] > df['total_volume']
            for idx in df[mask].index:
                issues.append((idx, 'trigger_usage_gt_total'))

        # 4. 滞后量计算不匹配 (考虑绝对值转换)
        if all(c in cols for c in ['total_volume', 'gear_label', 'trigger_usage', 'lag_amount']):
            expected_lag = df['trigger_usage'] - (df['total_volume'] * df['gear_label'])
            diff_threshold = 0.01
            direct_ok = (abs(df['lag_amount'] - expected_lag) <= diff_threshold)
            mismatch_mask = ~direct_ok
            for idx in df[mismatch_mask].index:
                issues.append((idx, 'lag_calculation_mismatch'))

        # 5. 滞后量百分比范围问题（如果存在 lag_percentage）
        if 'lag_percentage' in cols:
            min_lag = self.config.get('business_rules', {}).get('min_lag_percentage', 0.5)
            max_lag = self.config.get('business_rules', {}).get('max_lag_percentage', 3.0)
            mask = (df['lag_percentage'] < min_lag) | (df['lag_percentage'] > max_lag)
            for idx in df[mask].index:
                issues.append((idx, 'lag_percentage_out_of_range'))

        # 6. 未来时间记录
        if 'remind_time' in cols:
            from datetime import datetime as _dt
            mask = df['remind_time'] > _dt.now()
            for idx in df[mask].index:
                issues.append((idx, 'future_time'))

        # 7. 本地网不一致
        if all(c in cols for c in ['local_network', 'file_local_network']):
            mask = df['local_network'] != df['file_local_network']
            for idx in df[mask].index:
                issues.append((idx, 'network_inconsistency'))

        if not issues:
            return pd.DataFrame()

        # 合并原因
        from collections import defaultdict
        reason_map = defaultdict(list)
        for idx, reason in issues:
            if reason not in reason_map[idx]:
                reason_map[idx].append(reason)

        rows = []
        for idx, rlist in reason_map.items():
            row = df.loc[idx].copy()
            row['filter_reason'] = ';'.join(sorted(rlist))
            rows.append(row)

        invalid_df = pd.DataFrame(rows)
        return invalid_df.reset_index(drop=True)