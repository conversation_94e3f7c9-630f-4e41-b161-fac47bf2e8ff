"""
模型工厂

负责创建和管理不同类型的机器学习模型
"""

import logging
import yaml
import os
from typing import Dict, Any, Optional, Callable
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import GridSearchCV, cross_val_score
import xgboost as xgb
import lightgbm as lgb
from tqdm import tqdm
import threading
import time

# 贝叶斯优化相关导入
try:
    from skopt import BayesSearchCV
    from skopt.space import Real, Categorical, Integer
    from skopt.callbacks import VerboseCallback
    BAYESIAN_AVAILABLE = True
except ImportError:
    BAYESIAN_AVAILABLE = False

class ModelFactory:
    """模型工厂类"""

    def __init__(self, config_path: str = None):
        """
        初始化模型工厂

        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()

    def _create_progress_grid_search(self, base_model, param_grid, cv_folds, scoring):
        """
        创建带进度条的GridSearchCV

        Args:
            base_model: 基础模型
            param_grid: 参数网格
            cv_folds: 交叉验证折数
            scoring: 评分方法

        Returns:
            GridSearchCV with progress wrapper
        """
        # 计算总的训练次数
        total_combinations = 1
        for param_values in param_grid.values():
            total_combinations *= len(param_values)
        total_fits = total_combinations * cv_folds

        # 创建GridSearchCV
        grid_search = GridSearchCV(
            estimator=base_model,
            param_grid=param_grid,
            cv=cv_folds,
            scoring=scoring,
            n_jobs=-1,
            verbose=0  # 设为0避免打印每个CV的详细信息
        )

        return grid_search, total_fits

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/config.yaml')

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"警告: 无法加载配置文件: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'model': {
                'algorithm': 'random_forest',
                'random_forest': {
                    'n_estimators': 100,
                    'max_depth': 10,
                    'min_samples_split': 5,
                    'min_samples_leaf': 2,
                    'random_state': 42,
                    'n_jobs': -1
                }
            }
        }

    def _setup_logger(self) -> logging.Logger:
        """设置统一日志: 仅使用根日志器格式 (TelecomLogFormatter) , 移除本模块自定义的控制台handler"""
        logger = logging.getLogger(__name__)
        # 若之前已经被旧版本代码添加了普通格式的 StreamHandler, 这里清理掉以避免重复/格式不一致
        try:
            from src.utils.logger import TelecomLogFormatter  # 仅用于类型判断
            for h in list(logger.handlers):
                if isinstance(h, logging.StreamHandler) and not isinstance(getattr(h, 'formatter', None), TelecomLogFormatter):
                    logger.removeHandler(h)
        except Exception:
            # 安全兜底: 不抛出异常, 继续使用现有logger
            pass
        logger.propagate = True  # 确保传递到根日志器使用统一格式
        return logger

    def create_random_forest(self, custom_params: Dict[str, Any] = None) -> RandomForestRegressor:
        """
        创建随机森林回归模型

        Args:
            custom_params: 自定义参数

        Returns:
            RandomForestRegressor实例
        """
        # 获取默认参数
        default_params = self.config.get('model', {}).get('random_forest', {})

        # 合并自定义参数
        if custom_params:
            params = {**default_params, **custom_params}
        else:
            params = default_params

        self.logger.info(f"创建随机森林模型，参数: {params}")

        return RandomForestRegressor(**params)

    def create_xgboost(self, custom_params: Dict[str, Any] = None) -> xgb.XGBRegressor:
        """
        创建XGBoost回归模型

        Args:
            custom_params: 自定义参数

        Returns:
            XGBRegressor实例
        """
        # 获取默认参数
        default_params = self.config.get('model', {}).get('xgboost', {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42
        })

        # 合并自定义参数
        if custom_params:
            params = {**default_params, **custom_params}
        else:
            params = default_params

        self.logger.info(f"创建XGBoost模型，参数: {params}")

        return xgb.XGBRegressor(**params)

    def create_lightgbm(self, custom_params: Dict[str, Any] = None) -> lgb.LGBMRegressor:
        """
        创建LightGBM回归模型

        Args:
            custom_params: 自定义参数

        Returns:
            LGBMRegressor实例
        """
        # 获取默认参数
        default_params = self.config.get('model', {}).get('lightgbm', {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'num_leaves': 31,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42,
            'verbose': -1
        })

        # 合并自定义参数
        if custom_params:
            params = {**default_params, **custom_params}
        else:
            params = default_params

        self.logger.info(f"创建LightGBM模型，参数: {params}")

        return lgb.LGBMRegressor(**params)

    def create_model(self, algorithm: str = None, custom_params: Dict[str, Any] = None):
        """
        创建指定算法的模型

        Args:
            algorithm: 算法名称
            custom_params: 自定义参数

        Returns:
            模型实例
        """
        if algorithm is None:
            algorithm = self.config.get('model', {}).get('algorithm', 'random_forest')

        if algorithm == 'random_forest':
            return self.create_random_forest(custom_params)
        elif algorithm == 'xgboost':
            return self.create_xgboost(custom_params)
        elif algorithm == 'lightgbm':
            return self.create_lightgbm(custom_params)
        else:
            self.logger.error(f"不支持的算法: {algorithm}")
            raise ValueError(f"不支持的算法: {algorithm}")

    def get_bayesian_search_space(self, algorithm: str) -> Dict[str, Any]:
        """
        获取贝叶斯优化的搜索空间定义

        Args:
            algorithm: 算法名称

        Returns:
            搜索空间字典
        """
        if not BAYESIAN_AVAILABLE:
            self.logger.warning("scikit-optimize未安装，无法使用贝叶斯优化")
            return {}

        from skopt.space import Real, Categorical, Integer

        if algorithm == 'random_forest':
            return {
                'n_estimators': Integer(50, 300),
                'max_depth': Integer(5, 20),
                'min_samples_split': Integer(2, 20),
                'min_samples_leaf': Integer(1, 10)
            }
        elif algorithm == 'xgboost':
            return {
                'n_estimators': Integer(50, 300),
                'max_depth': Integer(3, 10),
                'learning_rate': Real(0.01, 0.3, prior='log-uniform'),
                'subsample': Real(0.6, 1.0)
            }
        elif algorithm == 'lightgbm':
            return {
                'n_estimators': Integer(50, 300),
                'max_depth': Integer(3, 10),
                'learning_rate': Real(0.01, 0.3, prior='log-uniform'),
                'num_leaves': Integer(15, 100)
            }
        else:
            return {}

    def create_progress_callback(self, network_label: str = "模型", total_calls: int = 50) -> Callable:
        """
        创建贝叶斯优化进度回调函数

        Args:
            network_label: 网络标签用于显示
            total_calls: 总调用次数用于计算进度百分比

        Returns:
            进度回调函数
        """
        def progress_callback(result):
            n_calls = len(result.func_vals)
            best_score = result.fun
            progress_percent = (n_calls / total_calls) * 100

            # 显示进度信息（每10%显示一次避免刷屏）
            if n_calls % max(1, total_calls // 10) == 0 or n_calls == total_calls:
                self.logger.info(f"[{network_label}] 贝叶斯优化进度: {n_calls}/{total_calls} ({progress_percent:.0f}%)")
                self.logger.info(f"[{network_label}] 当前最佳得分: {-best_score:.4f}")  # 转换回正值

            return False  # 继续优化

        return progress_callback

    def create_model_with_bayesian_search(self, algorithm: str = None,
                                        n_calls: int = 50,
                                        cv_folds: int = 5,
                                        scoring: str = 'neg_mean_squared_error',
                                        network_label: str = "模型") -> Any:
        """
        创建带贝叶斯优化的模型

        Args:
            algorithm: 算法名称
            n_calls: 贝叶斯优化调用次数（相当于网格搜索的参数组合数）
            cv_folds: 交叉验证折数
            scoring: 评分方法
            network_label: 网络标签用于显示

        Returns:
            BayesSearchCV实例或普通模型
        """
        if algorithm is None:
            algorithm = self.config.get('model', {}).get('algorithm', 'random_forest')

        if not BAYESIAN_AVAILABLE:
            self.logger.warning("scikit-optimize未安装，回退到网格搜索")
            return self.create_model_with_grid_search(algorithm, cv_folds, scoring)

        # 创建基础模型
        base_model = self.create_model(algorithm)

        # 获取搜索空间
        search_space = self.get_bayesian_search_space(algorithm)

        if not search_space:
            self.logger.warning(f"算法 {algorithm} 没有定义贝叶斯搜索空间")
            return base_model

        # 创建进度回调
        progress_callback = self.create_progress_callback(network_label, n_calls)

        # 创建贝叶斯搜索
        from skopt import BayesSearchCV

        bayes_search = BayesSearchCV(
            estimator=base_model,
            search_spaces=search_space,
            n_iter=n_calls,
            cv=cv_folds,
            scoring=scoring,
            n_jobs=1,  # 在多线程环境中使用单线程避免冲突
            verbose=0,
            random_state=42
        )

        # 将回调函数存储到模型中，以便在训练时使用
        bayes_search._progress_callback = progress_callback
        bayes_search._network_label = network_label
        bayes_search._n_calls = n_calls

        self.logger.info(f"创建贝叶斯优化模型，算法: {algorithm}, CV折数: {cv_folds}")
        self.logger.info(f"贝叶斯优化调用次数: {n_calls}, 总训练次数: {n_calls * cv_folds}")

        return bayes_search
        """
        获取超参数网格搜索参数 - 优化版，减少计算量

        Args:
            algorithm: 算法名称

        Returns:
            参数网格字典
        """
        if algorithm == 'random_forest':
            # 优化参数网格：从108个组合减少到18个组合
            return {
                'n_estimators': [100, 200],  # 2个值 (原3个)
                'max_depth': [10, 15, None],  # 3个值 (原4个)
                'min_samples_split': [2, 5],  # 2个值 (原3个)
                'min_samples_leaf': [1, 2]   # 2个值 (原3个)
                # 总组合: 2×3×2×2 = 24个，比原来的108个减少78%
            }
        elif algorithm == 'xgboost':
            return {
                'n_estimators': [100, 200],
                'max_depth': [6, 9],
                'learning_rate': [0.1, 0.2],
                'subsample': [0.8, 1.0]
            }
        elif algorithm == 'lightgbm':
            return {
                'n_estimators': [100, 200],
                'max_depth': [6, 9],
                'learning_rate': [0.1, 0.2],
                'num_leaves': [31, 63]
            }
        else:
            return {}

    def get_hyperparameter_grid(self, algorithm: str) -> Dict[str, list]:
        """获取超参数网格（精简版，控制计算量）"""
        if algorithm == 'random_forest':
            return {
                'n_estimators': [100, 200],
                'max_depth': [10, 15, None],
                'min_samples_split': [2, 5],
                'min_samples_leaf': [1, 2]
            }
        elif algorithm == 'xgboost':
            return {
                'n_estimators': [100, 200],
                'max_depth': [6, 9],
                'learning_rate': [0.1, 0.2],
                'subsample': [0.8, 1.0]
            }
        elif algorithm == 'lightgbm':
            return {
                'n_estimators': [100, 200],
                'max_depth': [6, 9],
                'learning_rate': [0.1, 0.2],
                'num_leaves': [31, 63]
            }
        else:
            return {}

    def create_model_with_grid_search(self, algorithm: str = None,
                                    cv_folds: int = 5,
                                    scoring: str = 'neg_mean_squared_error') -> GridSearchCV:
        """
        创建带网格搜索的模型

        Args:
            algorithm: 算法名称
            cv_folds: 交叉验证折数
            scoring: 评分方法

        Returns:
            GridSearchCV实例
        """
        if algorithm is None:
            algorithm = self.config.get('model', {}).get('algorithm', 'random_forest')

        # 创建基础模型
        base_model = self.create_model(algorithm)

        # 获取参数网格
        param_grid = self.get_hyperparameter_grid(algorithm)

        if not param_grid:
            self.logger.warning(f"算法 {algorithm} 没有定义参数网格")
            return base_model

        # 创建带进度条的网格搜索
        grid_search, total_fits = self._create_progress_grid_search(
            base_model, param_grid, cv_folds, scoring
        )

        # 计算参数组合数用于日志显示
        total_combinations = 1
        for param_values in param_grid.values():
            total_combinations *= len(param_values)

        self.logger.info(f"创建网格搜索模型，算法: {algorithm}, CV折数: {cv_folds}")
        self.logger.info(f"参数组合数: {total_combinations}, 总训练次数: {total_fits}")

        return grid_search

    def get_supported_algorithms(self) -> list:
        """
        获取支持的算法列表

        Returns:
            算法名称列表
        """
        return ['random_forest', 'xgboost', 'lightgbm']

    def get_model_info(self, model) -> Dict[str, Any]:
        """
        获取模型信息

        Args:
            model: 模型实例

        Returns:
            模型信息字典
        """
        model_info = {
            'model_type': type(model).__name__,
            'model_module': type(model).__module__
        }

        # 获取模型参数
        if hasattr(model, 'get_params'):
            model_info['parameters'] = model.get_params()

        # 获取特征重要性（如果模型已训练且支持）
        if hasattr(model, 'feature_importances_') and model.feature_importances_ is not None:
            model_info['has_feature_importance'] = True
        else:
            model_info['has_feature_importance'] = False

        return model_info