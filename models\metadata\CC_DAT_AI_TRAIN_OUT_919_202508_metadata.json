{"model_name": "lag_prediction_919", "model_type": "LGBMRegressor", "save_timestamp": "202508", "local_network": "919", "file_path": "models/trained\\CC_DAT_AI_TRAIN_OUT_919_202508.pkl", "file_format": "CC_DAT_AI_TRAIN_OUT_本地网_时间(YYYYMM).PKL", "creation_date": "2025-09-16T16:51:43.668674", "model_info": {"model_type": "LGBMRegressor", "model_module": "lightgbm.sklearn", "parameters": {"boosting_type": "gbdt", "class_weight": null, "colsample_bytree": 0.8, "importance_type": "split", "learning_rate": 0.29999999999999993, "max_depth": 3, "min_child_samples": 20, "min_child_weight": 0.001, "min_split_gain": 0.0, "n_estimators": 300, "n_jobs": null, "num_leaves": 15, "objective": null, "random_state": 42, "reg_alpha": 0.0, "reg_lambda": 0.0, "subsample": 0.8, "subsample_for_bin": 200000, "subsample_freq": 0, "verbose": -1}, "has_feature_importance": true}, "target_type": "lag_amount", "target_column": "lag_amount", "feature_names": ["year", "month", "volume_type", "gear_label", "total_volume", "trigger_usage", "prev_1m_total_volume", "prev_1m_trigger_usage", "prev_1m_lag_amount", "prev_2m_total_volume", "prev_2m_trigger_usage", "prev_2m_lag_amount", "h_percentage", "gear_score", "historical_lag_stability", "h_change_1m", "h_change_2m", "h_trend", "business_type_1", "trigger_usage_x_gear_label", "prev_1m_total_volume_x_total_volume", "prev_1m_trigger_usage_x_trigger_usage"]}