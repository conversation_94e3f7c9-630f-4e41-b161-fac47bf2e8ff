"""
模型训练器

负责模型训练、超参数调优和模型保存
"""

import pandas as pd
import numpy as np
import pickle
import joblib
import json
import time
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
import yaml
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from tqdm import tqdm

from .model_factory import ModelFactory

# 业务/训练参数
DEFAULT_TARGET_TYPE = 'lag_percentage'  # 统一使用百分比建模
ENABLE_CALIBRATION = True               # 始终启用校准
CALIBRATION_METHOD = 'isotonic'         # 仅实现 isotonic

class ModelTrainer:
    """模型训练器类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化模型训练器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 初始化模型工厂
        self.model_factory = ModelFactory(config_path)
        
        # 训练状态
        self.trained_models = {}
        self.training_history = []
        
        # 路径配置
        self.models_dir = self.config.get('model', {}).get('trained_models_dir', 'models/trained')
        self.metadata_dir = self.config.get('model', {}).get('metadata_dir', 'models/metadata')
        
        # 创建目录
        os.makedirs(self.models_dir, exist_ok=True)
        os.makedirs(self.metadata_dir, exist_ok=True)
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/config.yaml')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"警告: 无法加载配置文件: {e}")
            return {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        # 不再添加处理器，让日志器使用根日志器的处理器
        logger.setLevel(logging.INFO)
        return logger
    
    def prepare_data(self, df: pd.DataFrame, target_column: str = 'lag_amount',
                    test_size: float = None, random_state: int = 42,
                    return_sample_weight: bool = True) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series, Optional[pd.Series]]:
        """
        准备训练数据
        
        Args:
            df: 特征数据DataFrame
            target_column: 目标列名（默认为lag_amount）
            test_size: 测试集比例
            random_state: 随机种子
            
        Returns:
            (X_train, X_test, y_train, y_test, sample_weight_train)
        """
        if test_size is None:
            # 80%训练集，20%测试集
            test_size = 1 - self.config.get('data', {}).get('train_test_split', 0.8)
        
        # 分离特征和目标变量
        if target_column not in df.columns:
            raise ValueError(f"目标列 {target_column} 不存在于数据中")
        
        # 排除非特征列
        exclude_columns = [
            target_column, 'user', 'local_network',
            'order_id', 'remind_time', 'source_file', 'file_local_network', 'file_time',
            'product_instance_id'  # 保持向后兼容
        ]
        
        feature_columns = [col for col in df.columns if col not in exclude_columns]
        
        X = df[feature_columns]
        y = df[target_column]
        
        # 检查缺失值
        if X.isnull().any().any():
            self.logger.warning("特征数据中存在缺失值")
        
        if y.isnull().any():
            self.logger.warning("目标变量中存在缺失值")
            # 删除目标变量为空的行
            mask = y.notna()
            X = X[mask]
            y = y[mask]
        
        # 分割数据 - 确保使用正确的比例
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )
        
        self.logger.info(f"数据分割完成: 训练集 {len(X_train)} 样本({len(X_train)/(len(X_train)+len(X_test))*100:.1f}%), 测试集 {len(X_test)} 样本({len(X_test)/(len(X_train)+len(X_test))*100:.1f}%)")
        self.logger.info(f"特征数量: {len(feature_columns)}")
        
        sample_weight_series: Optional[pd.Series] = None
        if return_sample_weight and 'sample_weight' in df.columns:
            try:
                # 与 y 对齐
                sw = df.loc[y.index, 'sample_weight'].astype(float)
                sample_weight_series = sw.loc[y_train.index]
            except Exception:
                self.logger.warning("sample_weight 提取失败，忽略权重")
                sample_weight_series = None
        return X_train, X_test, y_train, y_test, sample_weight_series
    
    def _fit_with_progress_monitoring(self, model, X_train, y_train, network_label, sample_weight: Optional[pd.Series] = None):
        """执行贝叶斯优化，使用真实迭代回调打印单行实时进度。

        目标:
        1. 单行刷新 (使用\r 回车覆盖) ，避免多行刷屏。
        2. 使用 BayesSearchCV 的实际迭代信息 (result.func_vals 长度)。
        3. 兼容多线程多本地网：为不同本地网前缀保留标签，不共享同一行。
           (在并行环境中每个线程输出自己的行，不使用tqdm多行管理，减少锁冲突)。
        4. 结束时输出最终结果，并换行锁定。
        """
        import sys, time, threading, os
        from math import sqrt
        n_calls = getattr(model, '_n_calls', 50)
        cv_folds = getattr(model, 'cv', 5)
        start_time = time.time()

        # 判断是否 TTY 或强制测试模式
        force_test = os.environ.get('FORCE_SINGLE_LINE_PROGRESS_TEST') == '1'
        is_tty = (hasattr(sys.stdout, 'isatty') and sys.stdout.isatty()) or force_test

        # tqdm 模式（TTY） vs 简洁日志模式（非TTY）
        try:
            from tqdm import tqdm
            use_tqdm = is_tty
        except ImportError:
            use_tqdm = False

        progress_bar = None
        last_reported = 0
        best_cache = None

        if use_tqdm:
            progress_bar = tqdm(total=n_calls, desc=f"{network_label} Bayes", ncols=0, leave=True, dynamic_ncols=True, file=sys.stdout)
        else:
            # 非TTY输出开始提示一次
            print(f"[{network_label}] 贝叶斯优化开始: {n_calls} 次搜索 × {cv_folds} 折CV = {n_calls * cv_folds} 次训练")
            non_tty_state = {'last_len':0}

        # 线程锁避免并行冲突（仅在非tqdm模式下需要保障原子性输出）
        global _bayes_print_lock
        try:
            _ = _bayes_print_lock
        except NameError:
            _bayes_print_lock = threading.Lock()  # type: ignore

        def _fmt_eta(done):
            elapsed = time.time() - start_time
            if done == 0:
                return "--:--/--:--"
            avg = elapsed / done
            remaining = (n_calls - done) * avg
            def fmt(t):
                m = int(t // 60); s = int(t % 60); return f"{m:02d}:{s:02d}"
            return f"{fmt(elapsed)}/{fmt(remaining)}"

        def callback(res):
            nonlocal last_reported, best_cache
            current = len(res.func_vals)
            if current == last_reported:
                return False
            last_reported = current
            try:
                # res.func_vals 是每次迭代的目标值(neg mse)，取最小值
                best_obj = min(res.func_vals[:current])
                # 转换为RMSE(仅用于展示)
                best_cache = sqrt(max(0.0, -best_obj)) if best_obj < 0 else best_obj
            except Exception:
                pass

            if use_tqdm and progress_bar is not None:
                # 更新至当前
                progress_bar.n = current
                postfix = {}
                if best_cache is not None:
                    postfix['best'] = f"{best_cache:.4f}"
                progress_bar.set_postfix(postfix, refresh=True)
                progress_bar.refresh()
            else:
                # 非TTY：仍使用单行覆盖（某些环境日志系统不支持\r 会显示多行，这是运行环境限制）
                pct = current / n_calls * 100
                line = f"[{network_label}] Bayes {current:3d}/{n_calls:<3d} {pct:5.1f}% ETA:{_fmt_eta(current)}"
                if best_cache is not None:
                    line += f" best={best_cache:.4f}"
                # 终端宽度截断（防止行过长换行）
                try:
                    width = os.get_terminal_size().columns
                    if width > 20 and len(line) >= width:
                        ell = '...'
                        keep = width - len(ell) - 1
                        if keep > 0:
                            line = line[:keep] + ell
                except Exception:
                    pass
                with _bayes_print_lock:
                    pad = ' ' * max(0, non_tty_state['last_len'] - len(line))
                    try:
                        sys.stdout.write('\r' + line + pad)
                        sys.stdout.flush()
                    except Exception:
                        print(line)
                non_tty_state['last_len'] = len(line)
                if current == n_calls:
                    sys.stdout.write('\n')
                    sys.stdout.flush()
            return False

        # 执行 fit
        try:
            try:
                fit_kwargs = {}
                if sample_weight is not None:
                    fit_kwargs['sample_weight'] = sample_weight
                model.fit(X_train, y_train, callback=[callback], **fit_kwargs)
            except TypeError:
                fit_kwargs = {}
                if sample_weight is not None:
                    fit_kwargs['sample_weight'] = sample_weight
                model.fit(X_train, y_train, callbacks=[callback], **fit_kwargs)
        finally:
            if progress_bar is not None:
                progress_bar.close()

        final_best = getattr(model, 'best_score_', None)
        if final_best is not None and final_best < 0:
            display_best = -final_best  # neg_mse -> mse
        else:
            display_best = final_best
        elapsed_min = (time.time() - start_time) / 60
        print(f"[{network_label}] 贝叶斯优化完成，用时 {elapsed_min:.1f} 分钟。最佳得分: {display_best:.4f}" if display_best is not None else f"[{network_label}] 贝叶斯优化完成。")
    
    def train_model(self, X_train: pd.DataFrame, y_train: pd.Series,
                   sample_weight: Optional[pd.Series] = None,
                   algorithm: str = None, hyperparameter_tuning: bool = None,
                   local_network: str = None) -> Any:
        """
        训练模型
        
        Args:
            X_train: 训练特征
            y_train: 训练目标变量
            algorithm: 算法名称
            hyperparameter_tuning: 是否进行超参数调优
            local_network: 本地网标识（用于进度条显示）
            
        Returns:
            训练好的模型
        """
        if algorithm is None:
            algorithm = self.config.get('model', {}).get('algorithm', 'random_forest')
        
        if hyperparameter_tuning is None:
            hyperparameter_tuning = self.config.get('model', {}).get('hyperparameter_tuning', True)
        
        # 构建本地网标识用于日志和进度条
        network_label = f"本地网{local_network}" if local_network else "模型"
        
        self.logger.info(f"开始训练{network_label}模型，算法: {algorithm}, 超参数调优: {hyperparameter_tuning}")
        
        # 记录训练开始时间
        start_time = datetime.now()
        
        try:
            if hyperparameter_tuning:
                # 检查是否使用贝叶斯优化
                optimization_method = self.config.get('model', {}).get('optimization_method', 'grid_search')
                
                if optimization_method == 'bayesian' and hasattr(self.model_factory, 'create_model_with_bayesian_search'):
                    # 使用贝叶斯优化
                    cv_folds = self.config.get('model', {}).get('cross_validation_folds', 5)
                    n_calls = self.config.get('model', {}).get('bayesian_n_calls', 50)
                    
                    self.logger.info(f"{network_label}使用贝叶斯优化进行超参数调优...")
                    self.logger.info(f"{network_label}贝叶斯优化详情: {n_calls}次智能搜索 × {cv_folds}折CV = {n_calls * cv_folds}次训练")
                    
                    # 创建贝叶斯优化模型
                    model = self.model_factory.create_model_with_bayesian_search(
                        algorithm, n_calls, cv_folds, network_label=network_label
                    )
                    
                    # 执行贝叶斯优化（带真实进度回调）
                    start_fit_time = time.time()
                    self.logger.info(f"{network_label}贝叶斯优化开始执行...")
                    
                    try:
                        # 执行贝叶斯优化并监控进度
                        self._fit_with_progress_monitoring(model, X_train, y_train, network_label, sample_weight=sample_weight)
                        
                        actual_time = time.time() - start_fit_time
                        self.logger.info(f"{network_label}贝叶斯优化完成! 实际耗时: {actual_time/60:.1f}分钟")
                        
                    except Exception as e:
                        actual_time = time.time() - start_fit_time
                        self.logger.error(f"{network_label}贝叶斯优化失败! 耗时: {actual_time/60:.1f}分钟, 错误: {e}")
                        raise e
                    
                    self.logger.info(f"{network_label}最佳参数: {model.best_params_}")
                    self.logger.info(f"{network_label}最佳得分: {model.best_score_:.4f}")
                    
                    # 使用最佳模型
                    best_model = model.best_estimator_
                    
                else:
                    # 使用网格搜索（原有逻辑）
                    cv_folds = self.config.get('model', {}).get('cross_validation_folds', 5)
                    model = self.model_factory.create_model_with_grid_search(algorithm, cv_folds)
                    
                    # 计算参数组合数用于进度条
                    param_grid = self.model_factory.get_hyperparameter_grid(algorithm)
                    total_combinations = 1
                    for param_values in param_grid.values():
                        total_combinations *= len(param_values)
                    total_fits = total_combinations * cv_folds
                    
                    # 预估每个参数组合的训练时间（基于数据量和特征数）
                    data_size = len(X_train)
                    feature_count = len(X_train.columns)
                    
                    # 根据数据量和特征数估算时间（单位：秒）
                    if data_size < 100:
                        estimated_time_per_combo = 2.0  # 小数据集
                    elif data_size < 1000:
                        estimated_time_per_combo = 4.0  # 中等数据集
                    else:
                        estimated_time_per_combo = 8.0  # 大数据集
                    
                    # 特征数也会影响训练时间
                    if feature_count > 50:
                        estimated_time_per_combo *= 1.5
                    
                    total_estimated_time = total_combinations * estimated_time_per_combo
                    
                    # 记录训练开始时间和预期信息
                    start_fit_time = time.time()
                    self.logger.info(f"{network_label}网格搜索详情: {total_combinations}个参数组合 × {cv_folds}折CV = {total_fits}次训练")
                    self.logger.info(f"{network_label}预估训练时间: {total_estimated_time/60:.1f}分钟 (数据量: {data_size}, 特征数: {feature_count})")
                    self.logger.info(f"{network_label}GridSearchCV训练中... (sklearn内部进行，无进度显示)")
                    
                    # 设置训练超时（如果预估时间过长）
                    max_training_time = self.config.get('model', {}).get('max_training_time_minutes', 60) * 60  # 转为秒
                    if total_estimated_time > max_training_time:
                        self.logger.warning(f"{network_label}预估训练时间({total_estimated_time/60:.1f}分钟)超过最大限制({max_training_time/60:.1f}分钟)")
                    
                    try:
                        # 执行实际的网格搜索 - sklearn内部处理，无法显示真实进度
                        if sample_weight is not None:
                            try:
                                model.fit(X_train, y_train, sample_weight=sample_weight)
                            except TypeError:
                                self.logger.warning("当前搜索器不支持 sample_weight, 已忽略")
                                model.fit(X_train, y_train)
                        else:
                            model.fit(X_train, y_train)
                        
                        actual_time = time.time() - start_fit_time
                        self.logger.info(f"{network_label}GridSearchCV完成! 实际耗时: {actual_time/60:.1f}分钟")
                        
                    except Exception as e:
                        actual_time = time.time() - start_fit_time
                        self.logger.error(f"{network_label}GridSearchCV失败! 耗时: {actual_time/60:.1f}分钟, 错误: {e}")
                        raise e
                    
                    self.logger.info(f"{network_label}最佳参数: {model.best_params_}")
                    self.logger.info(f"{network_label}最佳得分: {model.best_score_:.4f}")
                    
                    # 使用最佳模型
                    best_model = model.best_estimator_
                
            else:
                # 直接训练模型
                model = self.model_factory.create_model(algorithm)
                if sample_weight is not None:
                    try:
                        model.fit(X_train, y_train, sample_weight=sample_weight)
                    except TypeError:
                        self.logger.warning("基础模型不支持 sample_weight, 已忽略")
                        model.fit(X_train, y_train)
                else:
                    model.fit(X_train, y_train)
                best_model = model
            
            # 记录训练时间
            training_time = (datetime.now() - start_time).total_seconds()
            
            # 存储训练好的模型
            model_key = f"{algorithm}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.trained_models[model_key] = best_model
            
            # 记录训练历史
            training_record = {
                'model_key': model_key,
                'algorithm': algorithm,
                'training_time': training_time,
                'hyperparameter_tuning': hyperparameter_tuning,
                'training_samples': len(X_train),
                'features_count': len(X_train.columns),
                'timestamp': datetime.now().isoformat()
            }
            
            if hyperparameter_tuning:
                training_record['best_params'] = model.best_params_
                training_record['best_score'] = model.best_score_
            
            self.training_history.append(training_record)
            
            self.logger.info(f"模型训练完成，耗时: {training_time:.2f}秒")
            
            return best_model
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            raise
    
    def evaluate_model(self, model: Any, X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            model: 训练好的模型
            X_test: 测试特征
            y_test: 测试目标变量
            
        Returns:
            评估指标字典
        """
        try:
            # 预测
            y_pred = model.predict(X_test)
            
            # 计算评估指标
            metrics = {
                'mse': mean_squared_error(y_test, y_pred),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred)),
                'mae': mean_absolute_error(y_test, y_pred),
                'r2': r2_score(y_test, y_pred)
            }
            
            # 计算MAPE（平均绝对百分比误差）
            mape = np.mean(np.abs((y_test - y_pred) / y_test)) * 100
            metrics['mape'] = mape
            
            self.logger.info("模型评估结果:")
            for metric, value in metrics.items():
                self.logger.info(f"  {metric.upper()}: {value:.4f}")
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            return {}
    
    def cross_validate_model(self, model: Any, X: pd.DataFrame, y: pd.Series, 
                           cv_folds: int = None) -> Dict[str, Any]:
        """
        交叉验证模型
        
        Args:
            model: 模型实例
            X: 特征数据
            y: 目标变量
            cv_folds: 交叉验证折数
            
        Returns:
            交叉验证结果
        """
        if cv_folds is None:
            cv_folds = self.config.get('model', {}).get('cross_validation_folds', 5)
        
        try:
            self.logger.info(f"开始 {cv_folds} 折交叉验证")
            
            # 执行交叉验证
            cv_scores = cross_val_score(model, X, y, cv=cv_folds, 
                                      scoring='neg_mean_squared_error', n_jobs=-1)
            
            rmse_scores = np.sqrt(-cv_scores)
            
            cv_results = {
                'cv_rmse_scores': rmse_scores.tolist(),
                'cv_rmse_mean': rmse_scores.mean(),
                'cv_rmse_std': rmse_scores.std(),
                'cv_folds': cv_folds
            }
            
            self.logger.info(f"交叉验证结果:")
            self.logger.info(f"  RMSE平均值: {cv_results['cv_rmse_mean']:.4f}")
            self.logger.info(f"  RMSE标准差: {cv_results['cv_rmse_std']:.4f}")
            
            return cv_results
            
        except Exception as e:
            self.logger.error(f"交叉验证失败: {e}")
            return {}
    
    def save_model(self, model: Any, model_name: str, 
                  local_network: str = None, timestamp: str = None) -> str:
        """
        保存模型
        
        Args:
            model: 训练好的模型
            model_name: 模型名称
            local_network: 本地网标识
            timestamp: 时间戳 (YYYYMM格式)
            
        Returns:
            保存的文件路径
        """
        if timestamp is None:
            # 生成YYYYMM格式的时间戳
            timestamp = datetime.now().strftime('%Y%m')

        # 构建文件名：CC_DAT_AI_TRAIN_OUT_本地网_时间(YYYYMM).PKL
        if local_network:
            filename = f"CC_DAT_AI_TRAIN_OUT_{local_network}_{timestamp}.pkl"
        else:
            # 如果没有本地网信息，使用通用格式
            filename = f"CC_DAT_AI_TRAIN_OUT_GENERAL_{timestamp}.pkl"
        
        filepath = os.path.join(self.models_dir, filename)
        
        try:
            # 保存模型
            joblib.dump(model, filepath)
            
            # 保存模型元数据 - 保持_metadata后缀
            metadata = {
                'model_name': model_name,
                'model_type': type(model).__name__,
                'save_timestamp': timestamp,
                'local_network': local_network,
                'file_path': filepath,
                'file_format': 'CC_DAT_AI_TRAIN_OUT_本地网_时间(YYYYMM).PKL',
                'creation_date': datetime.now().isoformat(),
                'model_info': self.model_factory.get_model_info(model)
            }
            
            metadata_filename = filename.replace('.pkl', '_metadata.json')
            metadata_filepath = os.path.join(self.metadata_dir, metadata_filename)
            
            with open(metadata_filepath, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"模型已保存到: {filepath}")
            self.logger.info(f"元数据已保存到: {metadata_filepath}")
            self.logger.info(f"文件名格式：CC_DAT_AI_TRAIN_OUT_{local_network}_{timestamp}.PKL")
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存模型失败: {e}")
            raise
    
    def train_and_save_model(self, df: pd.DataFrame, model_name: str,
                           local_network: str = None, algorithm: str = None,
                           training_timestamp: str = None) -> Dict[str, Any]:
        """
        完整的训练和保存流程
        
        Args:
            df: 特征数据DataFrame
            model_name: 模型名称
            local_network: 本地网标识
            algorithm: 算法名称
            
        Returns:
            训练结果字典
        """
        try:
            self.logger.info(f"开始完整训练流程: {model_name}")
            
            # 1. 准备数据
            # 处理目标类型（优先读取配置，回退到默认常量）
            target_type = self.config.get('model', {}).get('target_type', DEFAULT_TARGET_TYPE)
            working_df = df.copy()
            target_column = 'lag_amount'
            if target_type == 'lag_percentage':
                if all(c in working_df.columns for c in ['lag_amount','total_volume']):
                    with np.errstate(divide='ignore', invalid='ignore'):
                        working_df['lag_percentage'] = (working_df['lag_amount'] / working_df['total_volume'].replace(0, np.nan)) * 100
                    # 去除无效
                    valid_mask = working_df['lag_percentage'].notna() & np.isfinite(working_df['lag_percentage'])
                    dropped = (~valid_mask).sum()
                    if dropped:
                        self.logger.info(f"百分比目标: 丢弃无效样本 {dropped} 行")
                    working_df = working_df[valid_mask]
                    target_column = 'lag_percentage'
                else:
                    self.logger.warning("target_type=lag_percentage 但缺少 lag_amount/total_volume 列, 回退到 lag_amount")
                    target_type = 'lag_amount'
            X_train, X_test, y_train, y_test, sample_weight = self.prepare_data(working_df, target_column=target_column)
            
            # 2. 训练模型（传递本地网标识）
            model = self.train_model(X_train, y_train, sample_weight=sample_weight, algorithm=algorithm, local_network=local_network)

            calibrator = None
            calibration_info = {}
            enable_calibration = self.config.get('model', {}).get('enable_calibration', ENABLE_CALIBRATION)
            calibration_method = self.config.get('model', {}).get('calibration_method', CALIBRATION_METHOD)
            if target_type == 'lag_percentage' and enable_calibration:
                # 在验证集上拟合校准器
                try:
                    raw_valid_pred = model.predict(X_test)
                    from sklearn.isotonic import IsotonicRegression
                    if calibration_method == 'isotonic':
                        calibrator = IsotonicRegression(out_of_bounds='clip')
                        calibrator.fit(raw_valid_pred, y_test)
                        calibration_info = {
                            'method': 'isotonic',
                            'train_samples': int(len(raw_valid_pred)),
                            'note': 'raw_pred -> calibrated_percentage'
                        }
                        self.logger.info(f"已训练 IsotonicRegression 校准器 (样本 {len(raw_valid_pred)})")
                    else:
                        self.logger.warning(f"未实现的校准方法: {calibration_method}, 跳过校准")
                except Exception as ce:
                    self.logger.error(f"校准训练失败: {ce}")
                    calibrator = None
            
            # 3. 评估模型
            test_metrics = self.evaluate_model(model, X_test, y_test)
            if target_type == 'lag_percentage':
                min_pct = self.config.get('business_rules', {}).get('min_lag_percentage', 0.5)
                max_pct = self.config.get('business_rules', {}).get('max_lag_percentage', 3.0)
                in_range_ratio = ((y_test >= min_pct) & (y_test <= max_pct)).mean() if len(y_test) else float('nan')
                test_metrics['target_in_business_range_ratio'] = float(in_range_ratio)
            
            # 4. 交叉验证
            cv_results = self.cross_validate_model(model, X_train, y_train)
            
            # 5. 保存模型
            # 保存：若存在校准器则封装字典对象
            if calibrator is not None:
                wrapped = {
                    'base_model': model,
                    'calibrator': calibrator,
                    'target_type': target_type,
                    'calibration': calibration_info
                }
                try:
                    # 临时文件名策略与 save_model 统一, 复用 save_model 逻辑但需绕过其 joblib.dump 重复保存
                    model_path = self.save_model(wrapped, model_name, local_network, timestamp=training_timestamp)
                except Exception as se:
                    self.logger.error(f"保存包含校准器的模型失败, 回退保存基础模型: {se}")
                    model_path = self.save_model(model, model_name, local_network, timestamp=training_timestamp)
            else:
                model_path = self.save_model(model, model_name, local_network, timestamp=training_timestamp)
            # 增补元数据写入 target_type
            try:
                if model_path:
                    model_filename = os.path.basename(model_path)
                    metadata_filename = model_filename.replace('.pkl', '_metadata.json')
                    metadata_path = os.path.join(self.metadata_dir, metadata_filename)
                    if os.path.exists(metadata_path):
                        with open(metadata_path, 'r', encoding='utf-8') as mf:
                            meta = json.load(mf)
                        meta['target_type'] = target_type
                        meta['target_column'] = target_column
                        if target_type == 'lag_percentage':
                            # 追加业务裁剪区间元数据，便于审计
                            meta['clip_range'] = {'min_percentage': 0.5, 'max_percentage': 3.0}
                        with open(metadata_path, 'w', encoding='utf-8') as mf:
                            json.dump(meta, mf, ensure_ascii=False, indent=2)
            except Exception as me:
                self.logger.warning(f"写入 target_type 元数据失败: {me}")
            
            # 6. 整理结果
            training_results = {
                'model_name': model_name,
                'model_path': model_path,
                'algorithm': algorithm or self.config.get('model', {}).get('algorithm', 'random_forest'),
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'features_count': len(X_train.columns),
                'test_metrics': test_metrics,
                'cv_results': cv_results,
                'feature_names': X_train.columns.tolist(),
                'target_type': target_type,
                'target_column': target_column,
                'calibration': calibration_info if calibration_info else None,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info("完整训练流程完成")
            
            return training_results
            
        except Exception as e:
            self.logger.error(f"完整训练流程失败: {e}")
            raise
    
    def batch_train_by_local_network(self, data_dict: Dict[str, pd.DataFrame],
                                   algorithm: str = None, enable_parallel: bool = True,
                                   max_workers: int = None, training_timestamp: str = None) -> Dict[str, Dict[str, Any]]:
        """
        按本地网批量训练模型 - 支持多线程并行训练

        Args:
            data_dict: 本地网为键，DataFrame为值的字典
            algorithm: 算法名称
            enable_parallel: 是否启用并行训练
            max_workers: 最大并行线程数
            
        Returns:
            训练结果字典
        """
        batch_results = {}
        total_networks = len(data_dict)
        
        # 显示总体训练计划
        self.logger.info(f"开始批量训练 {total_networks} 个本地网: {list(data_dict.keys())}")
        
        if not enable_parallel or len(data_dict) == 1:
            # 串行训练 - 简单显示完成状态
            self.logger.info("使用串行训练模式")
            
            for i, (local_network, df) in enumerate(data_dict.items(), 1):
                self.logger.info(f"正在训练本地网 {local_network} ({i}/{total_networks})")
                batch_results[local_network] = self._train_single_local_network(
                    local_network, df, algorithm, training_timestamp=training_timestamp
                )
                
                # 显示完成状态
                success = batch_results[local_network].get('success', False)
                status = "✓ 成功" if success else "✗ 失败"
                self.logger.info(f"本地网 {local_network} 训练完成: {status}")
                    
        else:
            # 并行训练 - 每个本地网都有自己的超参数调优进度条
            if max_workers is None:
                max_workers = min(len(data_dict), self.config.get('processing', {}).get('max_workers', 4))
            
            self.logger.info(f"使用并行训练模式，最大线程数: {max_workers}")
            self.logger.info("每个本地网将显示独立的训练进度条...")
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有训练任务
                future_to_local_network = {
                    executor.submit(self._train_single_local_network, local_network, df, algorithm, training_timestamp): local_network
                    for local_network, df in data_dict.items()
                }
                
                # 收集结果
                completed_count = 0
                for future in as_completed(future_to_local_network):
                    local_network = future_to_local_network[future]
                    try:
                        result = future.result()
                        batch_results[local_network] = result
                        
                        completed_count += 1
                        success = result.get('success', False)
                        status = "✓ 成功" if success else "✗ 失败"
                        
                        self.logger.info(f"本地网 {local_network} 训练完成 ({completed_count}/{total_networks}): {status}")
                        
                    except Exception as e:
                        completed_count += 1
                        self.logger.error(f"本地网 {local_network} 训练异常: {e}")
                        batch_results[local_network] = {'success': False, 'error': str(e)}
                        
                        self.logger.info(f"本地网 {local_network} 训练失败 ({completed_count}/{total_networks}): ✗ 异常")
        
        # 统计结果
        successful_count = sum(1 for result in batch_results.values() if result.get('success', False))
        total_count = len(batch_results)
        
        self.logger.info(f"批量训练完成，成功: {successful_count}/{total_count}")
        
        return batch_results
    
    def _train_single_local_network(self, local_network: str, df: pd.DataFrame, 
                                   algorithm: str = None, training_timestamp: str = None) -> Dict[str, Any]:
        """
        训练单个本地网的模型
        
        Args:
            local_network: 本地网标识
            df: 训练数据
            algorithm: 算法名称
            
        Returns:
            训练结果字典
        """
        try:
            self.logger.info(f"开始训练本地网 {local_network} 的模型，数据量: {len(df)} 行")
            
            model_name = f"lag_prediction_{local_network}"
            result = self.train_and_save_model(df, model_name, local_network, algorithm, training_timestamp=training_timestamp)
            
            # 添加成功标识
            result['success'] = True
            result['local_network'] = local_network
            
            self.logger.info(f"本地网 {local_network} 模型训练完成")
            
            return result
            
        except Exception as e:
            self.logger.error(f"本地网 {local_network} 模型训练失败: {e}")
            return {
                'success': False,
                'local_network': local_network,
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def get_training_summary(self) -> Dict[str, Any]:
        """
        获取训练摘要
        
        Returns:
            训练摘要字典
        """
        summary = {
            'total_trained_models': len(self.trained_models),
            'training_history': self.training_history,
            'available_algorithms': self.model_factory.get_supported_algorithms(),
            'models_directory': self.models_dir,
            'metadata_directory': self.metadata_dir
        }
        
        return summary