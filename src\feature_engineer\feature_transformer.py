"""
特征转换器

负责特征的标准化、归一化、编码等转换操作
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
import yaml
import os
import pickle
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder, OneHotEncoder
from sklearn.compose import ColumnTransformer

class FeatureTransformer:
    """特征转换器类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化特征转换器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 转换器存储
        self.transformers = {}
        self.fitted = False
        
        # 特征列定义
        self.numeric_features = []
        self.categorical_features = []
        self.target_column = 'lag_amount'
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/config.yaml')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"警告: 无法加载配置文件: {e}")
            return {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        # 不再添加处理器，让日志器使用根日志器的处理器
        logger.setLevel(logging.INFO)
        return logger
    
    def identify_feature_types(self, df: pd.DataFrame) -> Tuple[List[str], List[str]]:
        """
        识别特征类型
        
        Args:
            df: 特征DataFrame
            
        Returns:
            (数值特征列表, 分类特征列表)
        """
        numeric_features = []
        categorical_features = []
        
        # 排除目标列和标识列
        exclude_columns = [
            self.target_column, 'product_instance_id', 'local_network',
            'order_id', 'remind_time', 'source_file', 'work_order_id'
        ]
        
        # 获取one-hot编码的最大类别数限制
        max_categories = self.config.get('feature_engineering', {}).get('feature_transformation', {}).get('max_onehot_categories', 50)
        
        # 不允许缩放的列（保持业务原始语义）
        scaling_exclude = {
            'year','month','day','hour','weekday','is_weekend','year_month',
            'gear_label',
            'total_volume','trigger_usage','lag_amount',
            'prev_1m_total_volume','prev_1m_trigger_usage','prev_1m_lag_amount',
            'prev_2m_total_volume','prev_2m_trigger_usage','prev_2m_lag_amount'
        }

        for col in df.columns:
            if col in exclude_columns:
                continue
                
            if pd.api.types.is_numeric_dtype(df[col]):
                # 剔除不允许缩放列
                if col in scaling_exclude:
                    continue
                numeric_features.append(col)
            else:
                # 检查分类特征的基数（唯一值数量）
                unique_count = df[col].nunique()
                if unique_count > max_categories:
                    self.logger.warning(f"分类特征 '{col}' 有 {unique_count} 个唯一值，超过限制 {max_categories}，将被排除")
                    continue
                categorical_features.append(col)
        
        self.numeric_features = numeric_features
        self.categorical_features = categorical_features
        
        self.logger.info(f"识别到 {len(numeric_features)} 个数值特征, {len(categorical_features)} 个分类特征")
        
        return numeric_features, categorical_features
    
    def handle_missing_values(self, df: pd.DataFrame, strategy: str = 'median') -> pd.DataFrame:
        """
        处理缺失值
        
        Args:
            df: 数据DataFrame
            strategy: 处理策略 ('median', 'mean', 'mode', 'forward_fill')
            
        Returns:
            处理后的DataFrame
        """
        df_filled = df.copy()
        
        # 处理数值特征的缺失值
        for col in self.numeric_features:
            if col in df_filled.columns:
                if strategy == 'median':
                    fill_value = df_filled[col].median()
                elif strategy == 'mean':
                    fill_value = df_filled[col].mean()
                elif strategy == 'forward_fill':
                    df_filled[col] = df_filled[col].fillna(method='ffill')
                    continue
                else:
                    fill_value = df_filled[col].median()  # 默认使用中位数
                
                df_filled[col] = df_filled[col].fillna(fill_value)
        
        # 处理分类特征的缺失值
        for col in self.categorical_features:
            if col in df_filled.columns:
                if strategy == 'mode':
                    mode_value = df_filled[col].mode().iloc[0] if not df_filled[col].mode().empty else 'unknown'
                    df_filled[col] = df_filled[col].fillna(mode_value)
                else:
                    df_filled[col] = df_filled[col].fillna('unknown')
        
        missing_count = df_filled.isnull().sum().sum()
        self.logger.info(f"缺失值处理完成，剩余缺失值: {missing_count}")
        
        return df_filled
    
    def scale_numeric_features(self, df: pd.DataFrame, method: str = 'standard') -> pd.DataFrame:
        """
        缩放数值特征
        
        Args:
            df: 数据DataFrame
            method: 缩放方法 ('standard', 'minmax', 'robust')
            
        Returns:
            缩放后的DataFrame
        """
        df_scaled = df.copy()

        if not self.numeric_features:
            self.logger.warning("没有数值特征需要缩放")
            return df_scaled

        # 1) 过滤当前已不存在的列（可能在编码或裁剪过程中被删除）
        existing_numeric = [c for c in self.numeric_features if c in df_scaled.columns]
        missing_numeric = [c for c in self.numeric_features if c not in df_scaled.columns]
        if missing_numeric:
            self.logger.warning(
                f"数值缩放阶段发现 {len(missing_numeric)} 个已登记数值特征缺失(可能被编码/裁剪移除): {missing_numeric[:10]}{'...' if len(missing_numeric)>10 else ''}"
            )
        if not existing_numeric:
            self.logger.warning("登记的数值特征在当前数据集中均不存在，跳过缩放")
            return df_scaled

        # 2) 选择缩放器
        if method == 'standard':
            scaler = StandardScaler()
        elif method == 'minmax':
            scaler = MinMaxScaler()
        else:
            scaler = StandardScaler()  # 默认使用标准缩放

        # 3) 拟合与转换（仅使用存在的列）
        try:
            if not self.fitted or 'numeric_scaler' not in self.transformers:
                scaled_values = scaler.fit_transform(df_scaled[existing_numeric])
                self.transformers['numeric_scaler'] = scaler
            else:
                scaler = self.transformers['numeric_scaler']
                # 如果旧缩放器的特征数与当前不一致，采用降级策略：仅对交集列缩放
                if hasattr(scaler, 'n_features_in_') and scaler.n_features_in_ != len(existing_numeric):
                    self.logger.warning(
                        f"旧缩放器特征数 {getattr(scaler,'n_features_in_', 'NA')} 与当前待缩放数 {len(existing_numeric)} 不一致，按交集列重建并重新拟合"
                    )
                    scaled_values = scaler.fit_transform(df_scaled[existing_numeric])
                    self.transformers['numeric_scaler'] = scaler
                else:
                    scaled_values = scaler.transform(df_scaled[existing_numeric])
        except Exception as e:
            self.logger.error(f"数值特征缩放失败，已跳过缩放阶段: {e}")
            return df_scaled

        # 4) 更新 DataFrame
        df_scaled[existing_numeric] = scaled_values

        self.logger.info(
            f"数值特征缩放完成，使用方法: {method}，缩放列数: {len(existing_numeric)} (缺失跳过: {len(missing_numeric)})"
        )

        return df_scaled
    
    def encode_categorical_features(self, df: pd.DataFrame, method: str = None) -> pd.DataFrame:
        """
        编码分类特征
        
        Args:
            df: 数据DataFrame
            method: 编码方法 ('onehot', 'label', 'target')，如果为None则从配置文件读取
            
        Returns:
            编码后的DataFrame
        """
        df_encoded = df.copy()
        
        if not self.categorical_features:
            self.logger.warning("没有分类特征需要编码")
            return df_encoded
        
        # 从配置文件读取编码方式
        if method is None:
            method = self.config.get('feature_engineering', {}).get('feature_transformation', {}).get('categorical_encoding', 'label')
        
        if method == 'onehot':
            df_encoded = self._onehot_encode(df_encoded)
        elif method == 'label':
            df_encoded = self._label_encode(df_encoded)
        else:
            df_encoded = self._label_encode(df_encoded)  # 默认使用标签编码
        
        self.logger.info(f"分类特征编码完成，使用方法: {method}")
        
        return df_encoded
    
    def _onehot_encode(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        One-hot编码（内存优化版本）
        """
        df_encoded = df.copy()
        max_categories = self.config.get('feature_engineering', {}).get('feature_transformation', {}).get('max_onehot_categories', 20)
        
        # 检查总体内存需求
        total_new_columns = 0
        high_cardinality_features = []
        
        for col in self.categorical_features:
            if col in df_encoded.columns:
                unique_count = df_encoded[col].nunique()
                if unique_count > max_categories:
                    high_cardinality_features.append((col, unique_count))
                    continue
                total_new_columns += unique_count - 1  # drop='first'
        
        # 估算内存需求（假设每个元素 8 字节）
        estimated_memory_gb = (len(df_encoded) * total_new_columns * 8) / (1024**3)
        
        if estimated_memory_gb > 2.0:  # 超过2GB就警告
            self.logger.warning(f"估算One-hot编码将增加 {estimated_memory_gb:.2f}GB 内存使用，建议使用标签编码")
        
        # 处理高基数特征
        if high_cardinality_features:
            self.logger.warning(f"发现高基数分类特征，将使用标签编码:")
            for col, count in high_cardinality_features:
                self.logger.warning(f"  - {col}: {count} 个唯一值（超过限制 {max_categories}）")
                # 从分类特征列表中移除，然后使用标签编码
                temp_categorical_features = self.categorical_features.copy()
                self.categorical_features = [c for c in self.categorical_features if c != col]
                df_encoded = self._label_encode_single(df_encoded, col)
                self.categorical_features = temp_categorical_features
        
        # 对剩余的低基数特征进行one-hot编码
        for col in self.categorical_features:
            if col in df_encoded.columns:
                unique_count = df_encoded[col].nunique()
                if unique_count > max_categories:
                    continue  # 跳过高基数特征
                
                if not self.fitted:
                    # 创建并拟合编码器 - 兼容不同sklearn版本
                    try:
                        # 新版本sklearn (>= 1.2)
                        encoder = OneHotEncoder(drop='first', sparse_output=False, handle_unknown='ignore')
                    except TypeError:
                        # 旧版本sklearn (< 1.2)
                        encoder = OneHotEncoder(drop='first', sparse=False, handle_unknown='ignore')
                    
                    # 逐步处理避免大矩阵的内存问题
                    encoded_values = encoder.fit_transform(df_encoded[[col]])
                    self.transformers[f'{col}_onehot'] = encoder
                else:
                    # 使用已拟合的编码器
                    encoder = self.transformers[f'{col}_onehot']
                    encoded_values = encoder.transform(df_encoded[[col]])
                
                # 创建新列名
                feature_names = [f"{col}_{cat}" for cat in encoder.categories_[0][1:]]  # drop first
                
                # 使用更内存友好的方式添加编码后的列
                try:
                    encoded_df = pd.DataFrame(encoded_values, columns=feature_names, index=df_encoded.index)
                    # 先删除原始列，再添加新列，减少内存峰值
                    df_encoded = df_encoded.drop(columns=[col])
                    df_encoded = pd.concat([df_encoded, encoded_df], axis=1)
                except MemoryError as e:
                    self.logger.error(f"列 '{col}' 的One-hot编码导致内存不足，改为使用标签编码: {e}")
                    df_encoded = self._label_encode_single(df_encoded, col)
        
        return df_encoded
    
    def _label_encode(self, df: pd.DataFrame) -> pd.DataFrame:
        """标签编码"""
        df_encoded = df.copy()
        
        for col in self.categorical_features:
            if col in df_encoded.columns:
                if not self.fitted:
                    # 创建并拟合编码器
                    encoder = LabelEncoder()
                    df_encoded[col] = encoder.fit_transform(df_encoded[col].astype(str))
                    self.transformers[f'{col}_label'] = encoder
                else:
                    # 使用已拟合的编码器
                    encoder = self.transformers[f'{col}_label']
                    # 处理未见过的类别
                    unique_values = set(df_encoded[col].astype(str))
                    known_values = set(encoder.classes_)
                    unknown_values = unique_values - known_values
                    
                    if unknown_values:
                        self.logger.warning(f"列 {col} 发现未知类别: {unknown_values}")
                        # 将未知类别替换为第一个已知类别
                        df_encoded[col] = df_encoded[col].astype(str).replace(
                            list(unknown_values), encoder.classes_[0]
                        )
                    
                    df_encoded[col] = encoder.transform(df_encoded[col].astype(str))
        
        return df_encoded
    
    def _label_encode_single(self, df: pd.DataFrame, col: str) -> pd.DataFrame:
        """
        对单个分类特征进行标签编码
        
        Args:
            df: 数据DataFrame
            col: 要编码的列名
            
        Returns:
            编码后的DataFrame
        """
        df_encoded = df.copy()
        
        if col in df_encoded.columns:
            if not self.fitted:
                # 创建并拟合编码器
                encoder = LabelEncoder()
                df_encoded[col] = encoder.fit_transform(df_encoded[col].astype(str))
                self.transformers[f'{col}_label'] = encoder
            else:
                # 使用已拟合的编码器
                encoder = self.transformers[f'{col}_label']
                # 处理未见过的类别
                unique_values = set(df_encoded[col].astype(str))
                known_values = set(encoder.classes_)
                unknown_values = unique_values - known_values
                
                if unknown_values:
                    self.logger.warning(f"列 {col} 发现未知类别: {unknown_values}")
                    # 将未知类别替换为第一个已知类别
                    df_encoded[col] = df_encoded[col].astype(str).replace(
                        list(unknown_values), encoder.classes_[0]
                    )
                
                df_encoded[col] = encoder.transform(df_encoded[col].astype(str))
        
        return df_encoded
    
    def create_interaction_features(self, df: pd.DataFrame, 
                                  feature_pairs: List[Tuple[str, str]] = None) -> pd.DataFrame:
        """
        创建交互特征
        
        Args:
            df: 数据DataFrame
            feature_pairs: 特征对列表，如果为None则自动选择
            
        Returns:
            添加交互特征的DataFrame
        """
        df_with_interactions = df.copy()
        
        if feature_pairs is None:
            # 自动选择一些有意义的特征对
            feature_pairs = [
                ('gear_label', 'business_type'),
                ('trigger_usage', 'gear_label'),  # 改为trigger_usage
                ('prev_1m_total_volume', 'total_volume'),  # 改为更合适的字段名
                ('prev_1m_trigger_usage', 'trigger_usage'),  # 改为更合适的字段名
                # 若存在第三个月历史，增加交互
                ('prev_3m_total_volume', 'total_volume'),
                ('prev_3m_trigger_usage', 'trigger_usage')
            ]
        
        interaction_count = 0
        for feature1, feature2 in feature_pairs:
            if feature1 in df_with_interactions.columns and feature2 in df_with_interactions.columns:
                # 数值特征相乘
                if (pd.api.types.is_numeric_dtype(df_with_interactions[feature1]) and 
                    pd.api.types.is_numeric_dtype(df_with_interactions[feature2])):
                    interaction_name = f"{feature1}_x_{feature2}"
                    df_with_interactions[interaction_name] = (
                        df_with_interactions[feature1] * df_with_interactions[feature2]
                    )
                    interaction_count += 1
        
        self.logger.info(f"创建了 {interaction_count} 个交互特征")
        
        return df_with_interactions
    
    def apply_business_transformations(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        应用业务相关的转换
        
        Args:
            df: 数据DataFrame
            
        Returns:
            转换后的DataFrame
        """
        df_transformed = df.copy()
        
        # 滞后量百分比转换 -> 重命名为 h_percentage (去除 lag_ 前缀)
        if all(col in df_transformed.columns for col in ['lag_amount', 'total_volume']):
            df_transformed['h_percentage'] = (
                df_transformed['lag_amount'] / df_transformed['total_volume'] * 100
            ).fillna(0)
        
        # 档位档次评分
        if 'gear_label' in df_transformed.columns:
            gear_score_map = {0.2: 1, 0.4: 2, 0.6: 3, 0.8: 4, 1.0: 5}
            df_transformed['gear_score'] = df_transformed['gear_label'].map(gear_score_map).fillna(0)
        
        # 历史稳定性指标
        lag_cols = [col for col in df_transformed.columns if 'lag_' in col and 'lag_amount' in col]
        if len(lag_cols) >= 2:
            # 计算历史滞后量的方差作为稳定性指标
            df_transformed['historical_lag_stability'] = df_transformed[lag_cols].var(axis=1, skipna=True)
        
        # 趋势特征 (重命名)
        if all(col in df_transformed.columns for col in ['prev_1m_lag_amount', 'prev_2m_lag_amount']):
            # 计算滞后量变化趋势 -> h_change_*
            df_transformed['h_change_1m'] = df_transformed['lag_amount'] - df_transformed['prev_1m_lag_amount']
            df_transformed['h_change_2m'] = df_transformed['prev_1m_lag_amount'] - df_transformed['prev_2m_lag_amount']
            
            # 简单趋势 -> h_trend
            df_transformed['h_trend'] = np.where(
                (df_transformed['h_change_1m'] > 0) & (df_transformed['h_change_2m'] > 0),
                1,  # 上升趋势
                np.where(
                    (df_transformed['h_change_1m'] < 0) & (df_transformed['h_change_2m'] < 0),
                    -1,  # 下降趋势
                    0   # 稳定
                )
            )

        # 三个月扩展特征（可降级，不强制）
        if 'prev_3m_lag_amount' in df_transformed.columns:
            if 'lag_amount' in df_transformed.columns:
                df_transformed['h_change_3m'] = df_transformed['lag_amount'] - df_transformed['prev_3m_lag_amount']
            if 'h_change_1m' in df_transformed.columns and 'h_change_2m' in df_transformed.columns:
                df_transformed['h_trend_accel'] = df_transformed['h_change_1m'] - df_transformed['h_change_2m']
            hist_cols = [c for c in ['prev_3m_lag_amount','prev_2m_lag_amount','prev_1m_lag_amount','lag_amount'] if c in df_transformed.columns]
            if len(hist_cols) >= 3:
                df_transformed['historical_lag_stability'] = df_transformed[hist_cols].var(axis=1, skipna=True)
        
        self.logger.info("业务转换完成")
        
        return df_transformed
    
    def fit_transform(self, df: pd.DataFrame, 
                     scale_method: str = 'standard',
                     encode_method: str = 'onehot',
                     create_interactions: bool = True) -> pd.DataFrame:
        """
        拟合并转换特征
        
        Args:
            df: 训练数据DataFrame
            scale_method: 缩放方法
            encode_method: 编码方法
            create_interactions: 是否创建交互特征
            
        Returns:
            转换后的DataFrame
        """
        self.logger.info("开始拟合并转换特征")
        
        # 1. 识别特征类型
        self.identify_feature_types(df)
        
        # 2. 处理缺失值
        df_processed = self.handle_missing_values(df)
        
        # 3. 应用业务转换
        df_processed = self.apply_business_transformations(df_processed)
        
        # 4. 重新识别特征类型（业务转换后可能有新特征）
        self.identify_feature_types(df_processed)
        
        # 5. 编码分类特征
        df_processed = self.encode_categorical_features(df_processed, encode_method)
        
        # 6. 缩放数值特征
        df_processed = self.scale_numeric_features(df_processed, scale_method)
        
        # 7. 创建交互特征
        if create_interactions:
            df_processed = self.create_interaction_features(df_processed)
        
        # 标记为已拟合
        self.fitted = True
        
        # 输出最终特征数量及名称列表
        try:
            feature_list_str = ", ".join([str(c) for c in df_processed.columns])
        except Exception:
            feature_list_str = str(list(df_processed.columns))
        self.logger.info(
            f"特征转换完成，最终特征数: {len(df_processed.columns)}，特征列表: {feature_list_str}"
        )
        
        return df_processed
    
    def transform(self, df: pd.DataFrame, create_interactions: bool = True) -> pd.DataFrame:
        """
        转换特征（使用已拟合的转换器）
        
        Args:
            df: 数据DataFrame
            create_interactions: 是否创建交互特征
            
        Returns:
            转换后的DataFrame
        """
        if not self.fitted:
            # 自动降级：首次调用 transform 等同 fit_transform（仅适用于已弃用持久化模式）
            self.logger.warning("转换器未拟合，自动执行 fit_transform (持久化已移除模式)")
            return self.fit_transform(df, create_interactions=create_interactions)
        
        self.logger.info("开始转换特征")
        self.logger.debug(
            f"进入 transform 时登记的数值特征: {len(self.numeric_features)} 个; 分类特征: {len(self.categorical_features)} 个"
        )
        
        # 加载后旧转换器兼容：确保 numeric_features / categorical_features 中的列若缺失则先补列
        df_processed = df.copy()
        missing_numeric = [c for c in self.numeric_features if c not in df_processed.columns]
        missing_categorical = [c for c in self.categorical_features if c not in df_processed.columns]
        if missing_numeric:
            for c in missing_numeric:
                df_processed[c] = 0
        if missing_categorical:
            for c in missing_categorical:
                df_processed[c] = 'unknown'
        if missing_numeric or missing_categorical:
            self.logger.warning(
                f"转换阶段发现缺失特征列: 数值 {len(missing_numeric)} 个, 分类 {len(missing_categorical)} 个; 已补齐"
            )

        # 1. 处理缺失值（在补列之后）
        df_processed = self.handle_missing_values(df_processed)
        
        # 2. 应用业务转换
        df_processed = self.apply_business_transformations(df_processed)
        
        # 3. 仅在尚未加载旧转换器字段列表的情况下重新识别（避免覆盖已保存结构）
        if not self.numeric_features and not self.categorical_features:
            try:
                self.identify_feature_types(df_processed)
            except Exception as e:
                self.logger.warning(f"重新识别特征类型失败，将沿用原有列表: {e}")

        # 4. 编码分类特征
        self.logger.debug(f"编码前列数 {len(df_processed.columns)}")
        df_processed = self.encode_categorical_features(df_processed)
        self.logger.debug(f"编码后列数 {len(df_processed.columns)}")
        
        # 5. 缩放数值特征（已增强容错）
        self.logger.debug(f"缩放前列数 {len(df_processed.columns)}")
        before_cols = set(df_processed.columns)
        df_processed = self.scale_numeric_features(df_processed)
        after_cols = set(df_processed.columns)
        dropped = before_cols - after_cols
        if dropped:
            self.logger.warning(f"缩放后检测到列缺失(异常): {list(dropped)[:10]}{'...' if len(dropped)>10 else ''}")
        self.logger.debug(f"缩放后列数 {len(df_processed.columns)}")
        
        # 6. 创建交互特征
        if create_interactions:
            df_processed = self.create_interaction_features(df_processed)
        
        self.logger.info("特征转换完成")
        
        return df_processed
    
    def save_transformers(self, filepath: str):
        """已弃用: 兼容旧调用，当前不再执行持久化。"""
        self.logger.info("save_transformers 调用已忽略 (持久化功能已移除)")
    
    def load_transformers(self, filepath: str):
        """已弃用: 兼容旧调用，当前不再从文件加载。"""
        self.logger.info("load_transformers 调用已忽略 (持久化功能已移除)")
    
    def get_feature_names(self) -> List[str]:
        """
        获取转换后的特征名称
        
        Returns:
            特征名称列表
        """
        if not self.fitted:
            return []
        
        feature_names = self.numeric_features.copy()
        
        # 添加编码后的分类特征名称
        for col in self.categorical_features:
            if f'{col}_onehot' in self.transformers:
                encoder = self.transformers[f'{col}_onehot']
                onehot_names = [f"{col}_{cat}" for cat in encoder.categories_[0][1:]]
                feature_names.extend(onehot_names)
            elif f'{col}_label' in self.transformers:
                feature_names.append(col)
        
        return feature_names