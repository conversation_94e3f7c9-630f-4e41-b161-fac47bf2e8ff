# 陕西本地网代码对照表

本文档记录了陕西电信系统中使用的本地网代码，用于滞后量预测模型的数据处理和模型训练。

## 本地网代码列表

| 本地网代码 | 地区名称 | 备注 |
|----------|---------|------|
| 912      | 西安    | 省会城市，主要数据来源 |
| 888      | 咸阳    | 重要地级市 |
| 919      | 宝鸡    | 重要地级市 |
| 910      | 渭南    | 地级市 |
| 911      | 铜川    | 地级市 |
| 913      | 延安    | 地级市 |
| 914      | 榆林    | 地级市 |
| 915      | 汉中    | 地级市 |
| 916      | 安康    | 地级市 |
| 917      | 商洛    | 地级市 |
| 290      | 杨凌    | 示范区 |

## 文件命名规范

### 训练数据文件
- 格式：`CC_DAT_AI_TRAIN_{本地网代码}_{YYYYMM}_{序号}.txt`
- 示例：`CC_DAT_AI_TRAIN_912_202411_001.txt`

### 处理结果文件
- 格式：`CC_DAT_AI_TRAIN_OUT_{本地网代码}_{时间戳}.txt`
- 示例：`CC_DAT_AI_TRAIN_OUT_912_20241201_120000.txt`

### 预测结果文件
- 格式：`CC_DAT_AI_PRE_OUT_{本地网代码}_{时间戳}.txt`
- 示例：`CC_DAT_AI_PRE_OUT_912_20241201_120000.txt`

### 模型文件
- 格式：`CC_DAT_AI_TRAIN_OUT_{本地网代码}_{时间戳}.pkl`
- 示例：`CC_DAT_AI_TRAIN_OUT_912_20241201_120000.pkl`

## 代码使用示例

### Python代码示例
```python
# 加载特定本地网的数据
data_dict = data_loader.load_data_by_local_network(['912', '888'])

# 训练特定本地网的模型
result = trainer.train_model(data, local_network="912")

# 批量处理多个本地网
local_networks = ['912', '888', '919', '910', '911']
results = model_trainer.batch_train_by_local_network({list(data_dict.keys())[0]: list(data_dict.values())[0]})  # 精简版：仅单本地网
```

### 命令行使用示例
```bash
# 训练指定本地网的模型
python scripts/train_model.py --latnid "912" --model-type "XGBoost"

# 预测指定本地网的数据
python scripts/predict_model.py --latnid "912" --input-file data/raw/predict_data.txt

# 评估指定本地网的模型
python scripts/evaluate_model.py --model-path models/trained/xgb_912_20241201.pkl
```

## 数据处理注意事项

1. **数据分区**: 每个本地网的数据独立处理，避免跨地区数据混合
2. **模型独立性**: 每个本地网训练独立的模型，提高预测精度
3. **文件管理**: 严格按照本地网代码命名文件，便于数据管理和追溯
4. **并行处理**: 支持多本地网数据的并行处理，提高系统效率

## 配置文件示例

在测试和配置文件中使用本地网代码：

```yaml
# tests/test_config.yaml
test_data:
  local_networks: ["912", "888", "919"]  # 使用实际本地网代码

# 示例数据生成
sample_data = {
    'local_network': ['912', '912', '888', '888', '919'],
    # 其他字段...
}
```

## 更新历史

- 2024-12-09: 将项目中所有示例从城市名称（西安、北京等）更新为实际本地网代码
- 2024-12-09: 统一文档和代码中的本地网代码使用规范