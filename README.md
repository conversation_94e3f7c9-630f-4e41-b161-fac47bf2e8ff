# 滞后量预测模型 (PredicH_model)

基于陕西电信智能提醒系统的滞后量预测模型，采用机器学习算法精准预测用户流量/语音使用的滞后量，通过"规则+AI智能调整"提高提醒过滤率，在保证提醒不遗漏的同时减少误报。

## 项目概述

本项目是天源迪科为陕西电信开发的核心AI组件，实现滞后量(h)预测模型。通过分析用户历史使用数据和行为模式，预测下一个计费周期的滞后量，为智能提醒系统提供关键的预测参数，优化提醒策略。

### 核心业务价值
- **提高过滤精度**: 通过精准的滞后量预测，显著提升提醒过滤率
- **减少误报率**: 智能识别用户使用规律，避免不必要的提醒
- **保证提醒覆盖**: 确保重要提醒不遗漏，维护用户体验
- **支持多场景**: 覆盖语音和数据业务，支持全省11个本地网

### 技术特性
- **智能数据处理**: 自动化数据加载、清洗、验证和特征工程流程
- **多算法支持**: 集成随机森林、XGBoost、LightGBM等先进机器学习算法
- **分布式处理**: 支持多线程并行处理不同本地网数据，提高处理效率
- **实时预测**: 提供批量预测和实时预测接口，满足不同业务场景
- **全面监控**: 完整的模型性能评估、可视化分析和业务指标监控

### 核心业务逻辑
```
滞后量预测公式: h = 触发时使用量 - (总量 × 档位标签)

档位标签计算: 基于 使用量/总量 比例分档
├── 档位0.8: 0.8 ≤ 比例 < 1.0
├── 档位0.6: 0.6 ≤ 比例 < 0.8  
├── 档位0.4: 0.4 ≤ 比例 < 0.6
├── 档位0.2: 0.2 ≤ 比例 < 0.4
└── 过滤规则: 比例 = 0 或 ≥ 1.0 的数据

预测约束边界:
├── 上限: h > 3% 时，取 3% 
├── 下限: h < 0.5% 时，取 0.5% (避免漏发风险)
└── 范围: 0.5% ≤ h ≤ 3%
```

## 项目架构

### 整体架构图
```
数据输入层 → 数据处理层 → 特征工程层 → 模型训练层 → 预测服务层 → 结果输出层
     ↓           ↓           ↓           ↓           ↓           ↓
原始数据文件   数据加载清洗   时序特征构建   随机森林训练   滞后量预测   预测结果文件
(*.txt)       异常处理      历史特征融合   模型保存      业务约束     (*.txt)
```

### 分层架构设计
```
├── 应用层 (Application Layer)
│   ├── 训练脚本 (train_model.py)
│   ├── 预测脚本 (predict_model.py) 
│   ├── 评估脚本 (evaluate_model.py)
│   └── 批处理脚本 (batch_process.py)
├── 业务层 (Business Layer)  
│   ├── 特征工程 (feature_engineer/)
│   ├── 模型训练 (model/trainer.py)
│   ├── 模型预测 (model/predictor.py)
│   └── 模型评估 (model/evaluator.py)
├── 数据层 (Data Layer)
│   ├── 数据加载 (data_processor/data_loader.py)
│   ├── 数据清洗 (data_processor/data_cleaner.py)
│   └── 数据验证 (data_processor/data_validator.py)
└── 基础层 (Infrastructure Layer)
    ├── 日志管理 (utils/logger.py)
    ├── 文件工具 (utils/file_utils.py)
    ├── 指标计算 (utils/metrics.py)
    └── 配置管理 (config/config.yaml)
```

### 目录结构详述
```
PredicH_model/                          # 项目根目录
├── README.md                           # 项目说明文档（本文件）
├── requirements.txt                    # Python依赖包清单
├── .github/                            # GitHub配置
│   └── copilot-instructions.md         # AI编程指南
├── config/                             # 配置文件目录
│   └── config.yaml                     # 主配置文件（业务参数、模型配置等）
├── data/                               # 数据目录
│   ├── raw/                            # 原始训练数据
│   │   ├── CC_DAT_AI_TRAIN_912_202411_001.txt    # 西安本地网数据
│   │   ├── CC_DAT_AI_TRAIN_888_202411_001.txt    # 咸阳本地网数据
│   │   └── CC_DAT_AI_TRAIN_919_202411_001.txt    # 宝鸡本地网数据
│   ├── processed/                      # 特征工程后数据
│   ├── predictions/                    # 预测结果输出
│   └── sample/                         # 样本和测试数据
├── models/                             # 模型文件目录
│   ├── trained/                        # 训练完成的模型(.pkl文件)
│   ├── metadata/                       # 模型元数据(性能指标、参数等)
│   └── backup/                         # 模型版本备份
├── src/                                # 核心源代码
│   ├── __init__.py                     # 包初始化文件
│   ├── data_processor/                 # 数据处理模块
│   │   ├── __init__.py
│   │   ├── data_loader.py              # 数据加载器(支持多线程、多格式)
│   │   ├── data_cleaner.py             # 数据清洗器(异常值处理、缺失值填充)
│   │   └── data_validator.py           # 数据验证器(格式检查、完整性验证)
│   ├── feature_engineer/               # 特征工程模块
│   │   ├── __init__.py
│   │   ├── feature_extractor.py        # 特征提取器(时序特征、统计特征)
│   │   ├── feature_transformer.py      # 特征转换器(标准化、编码)
│   │   └── feature_selector.py         # 特征选择器(重要性筛选)
│   ├── model/                          # 机器学习模型模块
│   │   ├── __init__.py
│   │   ├── trainer.py                  # 模型训练器(训练、调优、保存)
│   │   ├── predictor.py                # 模型预测器(加载、预测、后处理)
│   │   ├── evaluator.py                # 模型评估器(指标计算、可视化)
│   │   └── model_factory.py            # 模型工厂(多算法支持)
│   └── utils/                          # 工具模块
│       ├── __init__.py
│       ├── logger.py                   # 日志管理器(分级日志、性能监控)
│       ├── file_utils.py               # 文件工具(IO操作、路径管理)
│       ├── date_utils.py               # 日期工具(时间处理、特征提取)
│       └── metrics.py                  # 指标工具(回归指标、业务指标)
├── scripts/                            # 可执行脚本
│   ├── __init__.py
│   ├── train_model.py                  # 模型训练脚本(支持参数化配置)
│   ├── predict_model.py                # 模型预测脚本(批量预测、约束处理)
│   ├── evaluate_model.py               # 模型评估脚本(性能分析、报告生成)
│   └── batch_process.py                # 批处理脚本(全流程自动化)
├── tests/                              # 测试模块
│   ├── __init__.py
│   ├── test_config.yaml                # 测试配置文件
│   ├── test_data_processor.py          # 数据处理模块测试
│   ├── test_feature_engineer.py        # 特征工程模块测试
│   ├── test_model.py                   # 模型模块测试
│   ├── test_scripts.py                 # 脚本功能测试
│   ├── test_utils.py                   # 测试工具函数
│   └── test_utils_tests.py             # 工具模块测试
├── logs/                               # 日志文件目录
│   ├── data_processor.log              # 数据处理日志
│   ├── model_trainer.log               # 模型训练日志
│   └── *.log                          # 其他组件日志
├── notebooks/                          # Jupyter笔记本
│   └── example_usage.ipynb             # 使用示例和数据分析
└── docs/                               # 项目文档
    ├── 概要设计文档.md                   # 架构设计文档
    ├── 设计文档.txt                     # 详细实现方案
    └── 陕西本地网代码对照表.md           # 本地网编码规范
```

## 快速开始

### 环境要求

| 组件 | 版本要求 | 推荐版本 | 备注 |
|------|---------|---------|------|
| Python | ≥ 3.8 | 3.12.4 | 项目主要开发环境 |
| 操作系统 | Windows 10+, Linux, macOS | Windows 11 | 支持跨平台 |
| 内存 | ≥ 4GB | 8GB+ | 大数据处理建议更高配置 |
| 磁盘空间 | ≥ 2GB | 5GB+ | 包含数据、模型、日志空间 |
| CPU | 多核心 | 8核+ | 支持并行处理加速 |

### 核心依赖包
```python
# 科学计算和数据处理
numpy>=1.21.0              # 数值计算基础库
pandas>=1.3.0              # 数据分析和处理
scipy>=1.7.0               # 科学计算工具

# 机器学习框架  
scikit-learn>=1.0.0        # 主要ML算法库
xgboost>=1.5.0             # 梯度提升算法
lightgbm>=3.3.0            # 轻量级梯度提升

# 数据可视化
matplotlib>=3.5.0          # 基础绘图库
seaborn>=0.11.0            # 统计可视化
plotly>=5.0.0              # 交互式图表

# 配置和工具
pyyaml>=6.0                # YAML配置文件处理
joblib>=1.1.0              # 模型序列化和并行计算
tqdm>=4.62.0               # 进度条显示
python-dateutil>=2.8.0    # 日期时间处理
```

### 安装部署

#### 1. 获取项目代码
```bash
# 克隆项目仓库
git clone <repository-url>
cd PredicH_model

# 或解压项目包
# unzip PredicH_model.zip
# cd PredicH_model
```

#### 2. Python环境配置
```bash
# 检查Python版本（确保 ≥ 3.8）
python --version

# 推荐使用pyenv管理Python版本
# 安装指定版本
pyenv install 3.12.4
pyenv local 3.12.4

# 验证版本
python --version  # 应显示 Python 3.12.4
```

#### 3. 虚拟环境创建
```bash
# 创建虚拟环境（推荐方式）
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS  
source venv/bin/activate

# 验证虚拟环境
which python  # 应指向虚拟环境中的python
```

#### 4. 依赖包安装
```bash
# 升级pip到最新版本
python -m pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 验证关键包安装
python -c "import sklearn, xgboost, lightgbm, pandas; print('✓ 核心依赖安装成功')"
```

#### 5. 环境验证
```bash
# 验证项目结构
ls -la  # 检查目录结构

# 验证配置文件
python -c "import yaml; print('✓ 配置文件格式正确')" && \
python -c "with open('config/config.yaml', 'r', encoding='utf-8') as f: yaml.safe_load(f); print('✓ 配置文件加载成功')"

# 验证模块导入
python -c "from src.data_processor import DataLoader; print('✓ 数据处理模块正常')"
python -c "from src.feature_engineer import FeatureExtractor; print('✓ 特征工程模块正常')"
python -c "from src.model import ModelTrainer; print('✓ 模型训练模块正常')"
```

### 数据准备

#### 数据文件格式规范
结合当前真实数据分析情况（单用户单月平均≈1条记录），本项目对“原始输入”与“特征输出”采用严格且可复现的结构与业务规则。

##### 1. 原始训练数据（输入层）
```
文件命名: CC_DAT_AI_TRAIN_{本地网}_{YYYYMM}_{序号}.txt
示例:     CC_DAT_AI_TRAIN_912_202411_01.txt

编码: UTF-8   分隔符: |   字段数: 10
字段顺序:
工单ID|提醒时间|产品实例ID|本地网|业务类型|量本类型|档位标签|总量|触发时使用量|滞后量
```

字段与规则要点:
1) 采集周期: 建议每三个月批量采集一次，覆盖最近连续3个自然月（训练/预测统一使用近3个月窗口）。
2) 时间粒度: `提醒时间` 精确到分/秒；同一 (用户+本地网+业务类型+量本类型+档位标签+年月) 可多条，聚合时取最大时间戳记录。
3) 档位标签计算: R = 触发时使用量 / 总量 ：
   - 0.8 档: 0.8 ≤ R < 1.0
   - 0.6 档: 0.6 ≤ R < 0.8
   - 0.4 档: 0.4 ≤ R < 0.6
   - 0.2 档: 0.2 ≤ R < 0.4
   - 其余: R < 0.2 => 0；R ≥ 1 => 1 (标记为无效后过滤)
4) 过滤策略（进入建模前执行）：
   - 丢弃 档位标签 = 0 或 1 的记录。
   - 丢弃 总量 <= 0 或 触发时使用量 < 0。
5) 滞后量(第10列) 计算/校验 (保持原始符号，不做负值归零):
  h = 触发时使用量 - (总量 × 档位标签)
  - 允许 h 为负值（表示实际使用量尚未达到“档位理论阈值”）。
  - 9列旧文件: 动态新增该列 (按新公式计算)。
  - 10列文件: 重新计算与文件值核对，不一致写日志并以重新计算值为准（保留负号）。
6) 业务边界（仅在 预测输出 阶段对模型预测值应用，不回写训练标签）:
  - 下限: 预测 h_pred < 0.5% 总量 → 截断为 0.5% * 总量。
  - 上限: 预测 h_pred > 3%   总量 → 截断为 3% * 总量。
  - 训练阶段: 保留真实 h（可正可负），供模型学习真实偏差分布。

示例原始数据行(10列)（示例数值仅用于说明公式位置，非真实业务意义）:
```
202411290001|2024-11-29 23:58:12|13800001234|912|1|1|0.6|2048|1234| 1234 - (2048*0.6)= -? (允许为负, 不做归零)
```
含义: 2024年11月 本地网912 数据业务 用户总量2048MB, 使用量1234MB, 档位0.6。按档位阈值(0.6*2048) 与实际使用量差计算滞后量；若结果为负，表示尚未“滞后”，模型仍会学习该状态。


##### 2. 聚合逻辑（形成月级样本）
对过滤后的数据：
```
按 用户+本地网+业务类型+量本类型+档位标签+年月 分组
→ 每组选取 提醒时间 最大 的记录 → 形成该用户该月特征基线
```
这样得到的月级数据（单用户单月 ≈1条）与三个月窗口结合，可稳定构造“当前月 + 前1月 + 前2月”时序特征。

##### 3. 特征工程输出（训练/评估用 16 字段）
```
文件命名: CC_DAT_AI_TRAIN_OUT_{本地网}_{YYYYMM}_{序号}.txt
字段(16):
用户|年|月|本地网|业务类型|量本类型|档位标签|总量|触发时使用量|滞后量|
前一个月总量|前一个月触发时使用量|前一个月滞后量|
前二个月总量|前二个月触发时使用量|前二个月滞后量
```
说明:
- 滞后量字段对应 h_raw（仍保留原始值，评估环节可统计截断影响）。
- 前1/前2个月缺失时采用占位（可配置：默认用0并在模型侧由树模型自动处理）。
- 文件行数>500,000 时自动分割序号递增。

示例特征行(说明性质，值仅示例):
```
13800001234|2024|12|912|1|1|0.6|2048|1234| 1234-(0.6*2048)= -? |
2048|1100|-56.2|1024|600|12.5
```
（注：示例中出现的负滞后量保留原样；模型训练时直接使用该值。预测阶段再进行 0.5%-3% 范围截断处理。）

##### 4. 关键差异总结
| 维度 | 原始文件 | 特征文件 |
|------|----------|----------|
| 粒度 | 事件级/提醒级 | 用户-月级 |
| 去重方式 | 无 | 取最大时间戳 |
| 档位过滤 | 含 0/1 | 已剔除 0/1 |
| 时间窗口 | 任意历史（月内多条） | 连续近3个月 |
| 目标列 | 不含显式 h 字段（可计算） | 含滞后量 & 历史滞后量 |
| 行数 | 可能>用户数 | 约=用户月数 |

##### 5. 常见数据问题及指引
- 档位=0/1 占比异常升高 → 检查源端比例计算或总量为0情况。
- 大量同一用户同一月重复 → 核查提醒触发策略是否重复发送；如需可在聚合前添加二次筛选（例如优先选择触发时使用量更接近档位理论值的记录）。
- 触发时使用量>总量 → 视为异常，记录日志并剔除。
- 近3个月缺失2个月以上 → 仍保留（树模型可容忍），但可在评估报告中单列“稀疏用户”统计。

> 简化说明：本章节重点聚焦“原始数据内容与生成逻辑”，更复杂的特征扩展（如比例衍生、稳定性指标等）将在后续版本迭代中补充。

#### 支持的本地网编码
```
陕西电信本地网代码对照:
├── 912 - 西安市 (省会，主要数据来源)
├── 888 - 咸阳市  
├── 919 - 宝鸡市
├── 910 - 渭南市
├── 911 - 铜川市
├── 913 - 延安市  
├── 914 - 榆林市
├── 915 - 汉中市
├── 916 - 安康市
├── 917 - 商洛市
└── 290 - 杨凌示范区
```

#### 数据放置说明
```bash
# 创建数据目录结构
mkdir -p data/{raw,processed,predictions,sample}

# 放置原始训练数据到 data/raw/ 目录
cp CC_DAT_AI_TRAIN_*.txt data/raw/

# 检查数据文件
ls data/raw/
# 预期输出类似：
# CC_DAT_AI_TRAIN_912_202411_001.txt
# CC_DAT_AI_TRAIN_888_202411_001.txt  
# CC_DAT_AI_TRAIN_919_202411_001.txt

# 验证数据格式
head -5 data/raw/CC_DAT_AI_TRAIN_912_202411_001.txt
# 应显示管道分隔的数据记录
```

## 使用指南

### 核心工作流程

滞后量预测模型遵循标准的机器学习流程，分为训练、预测、评估三个主要阶段：

```
数据准备 → 特征工程 → 模型训练 → 模型验证 → 预测服务 → 性能监控
   ↓           ↓           ↓           ↓           ↓           ↓
原始数据加载  时序特征构建  随机森林训练  交叉验证评估  滞后量预测  业务指标分析
数据清洗验证  历史特征融合  超参数调优   残差分析     约束处理    模型优化
```

### 1. 模型训练（train_model.py 最新规范）

当前源码中 `train_model.py` 仅支持“单本地网”训练，必须提供 `--latnid`。训练过程中：
1) 传入的 `--bill-cycle` (可选) 代表“未来预测目标月 (prediction_month)”；若未提供则取系统当前月。
2) 实际训练样本所属月份 = `training_month = prediction_month - 1`。
3) 训练所需历史窗口 = `{training_month, training_month-1, training_month-2}` 三个月原始文件（不加载 prediction_month 本身）。
4) 仅当三个月文件均存在且含有效数据才继续；缺失即终止。

#### 1.1 可用命令行参数（真实存在）
| 参数 | 必填 | 说明 | 默认 |
|------|------|------|------|
| `--latnid` / `-n` | 是 | 本地网编码 (如 912 / 888 / 919)，一次仅训练该本地网 | 无 |
| `--config` / `-c` | 否 | 配置文件路径 | `config/config.yaml` |
| `--bill-cycle` / `-m` | 否 | 预测目标月 YYYYMM；用于反推出训练月 (预测月-1) | 系统当前月 |

不再支持：`--enable-tuning`、`--model-type`、`--log-level` 等命令行覆盖；所有算法、调优、折数、日志级别均由配置文件驱动。

#### 1.2 核心训练时间逻辑示例
假设执行：
```bash
python scripts/train_model.py --latnid 912 --bill-cycle 202509
```
则：
```
prediction_month = 202509
training_month   = 202508
历史窗口         = 202508 / 202507 / 202506 (需全部存在)
```
仅这三个月的原始文件被加载，模型标签来自 `training_month` 的真实滞后量；前1/前2月构造时序特征列。

#### 1.3 训练内部标准步骤
```
步骤 1 数据加载: 精确挑选三个月文件 → 合并 → 校验月份覆盖
步骤 2 数据清洗: 过滤档位0/1、异常值、无效时间、总量<=0等
步骤 3 数据验证: 结构/字段/范围/完整性校验，收集无效行写过滤文件(配置开启时)
步骤 4 特征工程: 提取用户月级特征 (当前月+前1/2月), 生成业务特征文件 (使用 training_month 作为时间戳)
步骤 5 模型训练: 读取配置指定算法与调优策略；单本地网独立训练
步骤 6 结果汇总: 输出日志、模型文件、元数据补充 feature_names
```

#### 1.4 配置主导的关键项（与命令行分离）
| 配置键 | 作用 | 备注 |
|--------|------|------|
| `model.algorithm` / 或 `model.default_algorithm` | 选择算法 | 仅在配置中设定 |
| `model.hyperparameter_tuning` | 是否启用调优 | 命令行不再控制 |
| `model.optimization_method` | `bayesian` / `grid_search` | 若实现保留则由配置决定 |
| `model.cross_validation_folds` | 交叉验证折数 | |
| `data.train_test_split` | 训练/测试划分 | 默认 0.8 |
| `data.filtered_output.enabled` | 是否写出过滤记录文件 | 生成目录 `data/processed/filtered/` |
| `model.metadata_dir` | 模型元数据目录 | 写入 `_metadata.json` |

#### 1.5 输出文件与命名
| 类型 | 路径 | 命名模式 | 时间戳来源 |
|------|------|----------|------------|
| 特征文件 | `data/processed/` | `CC_DAT_AI_TRAIN_OUT_{LATNID}_{training_month}_{SEQ}.txt` | training_month |
| 过滤记录(可选) | `data/processed/filtered/` | `CC_DAT_AI_TRAIN_FILTER_{LATNID}_{training_month}_01.txt` | training_month 或回退最大 file_time |
| 模型 | `models/trained/` | `CC_DAT_AI_TRAIN_OUT_{LATNID}_{training_month}.pkl` | training_month |
| 模型元数据 | `models/metadata/` | 同名 `_metadata.json` | 同上 |

#### 1.6 使用示例
```bash
# 基本训练（预测目标月默认=当前月；训练月=当前月-1）
python scripts/train_model.py --latnid 912

# 指定未来预测目标月（从而固定训练月窗口）
python scripts.train_model.py --latnid 912 --bill-cycle 202509

# 外部循环多本地网（顺序串行，避免资源竞争）
for net in 912 888 919; do
  python scripts/train_model.py --latnid $net || echo "本地网 $net 训练失败";
done
```

#### 1.7 退出码判定
| 退出码 | 含义 |
|--------|------|
| 0 | 训练成功（模型与元数据写出） |
| 1 | 失败/异常（详见 `logs/model_training.log`） |
| 130 | 用户中断 (Ctrl+C) |

#### 1.8 重要差异说明（相对旧文档）
| 旧说明 | 当前真实行为 |
|--------|--------------|
| 命令行可 `--enable-tuning` | 已移除，改为配置驱动 |
| 可一次训练多个本地网 | 强制单本地网；批量请用外层循环或 `batch_process.py` |
| 直接使用“预测月”原始文件 | 不加载未来预测月，只用训练月及其前两月 |
| 训练阶段可通过参数改日志级别 | 日志级别由配置 `logging.level` 控制 |

---

### 2. 模型预测（predict_model.py 最新规范）

当前脚本仅支持“单本地网未来月预测”，参数极简：`--config`、`--latnid`、`--prediction-month`。所有旧参数（`--data-dir`、`--output-dir`、`--apply-constraints`、`--no-constraints`、`--generate-summary`、`--save-intermediate`、`--model-timestamp`、`--input-file` 等）已从源码移除。

#### 2.1 可用命令行参数
| 参数 | 必填 | 说明 | 默认 |
|------|------|------|------|
| `--latnid` / `-n` | 是 | 需预测的本地网编码 | 无 |
| `--config` / `-c` | 否 | 配置文件路径 | `config/config.yaml` |
| `--prediction-month` / `-p` | 否 | 未来预测目标月 YYYYMM | 系统当前月 |

#### 2.2 历史数据依赖与校验
预测目标月 = P；脚本严格检查存在 `P-1`、`P-2` 两个月原始文件：
```
required: CC_DAT_AI_TRAIN_{LATNID}_{P-1}_01[1].txt  与  CC_DAT_AI_TRAIN_{LATNID}_{P-2}_01[1].txt
缺任意 → 抛 FileNotFoundError → 终止
```
预测样本构造仅使用前1/前2月历史特征；当前未来月本身的总量/使用量/滞后量列在构造时置 0 或缺省占位，模型仅依赖历史窗口进行推断。

#### 2.3 特征工程（未来模式）关键点
| 步骤 | 说明 |
|------|------|
| 清洗 | 同训练阶段：过滤档位0/1、异常记录 |
| 验证 | 结构与字段检查；不阻断但写日志 |
| 提取 | `mode='future'`：生成 prev_1m_* / prev_2m_* 特征；当前月字段可为占位 |
| 过滤 | 再次移除残留档位0/1 |
| 转换 | 每本地网临时 `fit_transform`；不持久化转换器；失败即跳过该本地网 |

#### 2.4 业务约束与预测后处理
| 项 | 说明 |
|----|------|
| 约束开关 | `apply_constraints=True` 在脚本中固定启用；如需关闭需通过后续代码修改或配置扩展（当前无命令行） |
| 下限 | 预测值 / 总量 < 0.5% → 调整为 0.5% * 总量 |
| 上限 | 预测值 / 总量 > 3%   → 调整为 3% * 总量 |
| 缺总量 | 找不到 `current_total_volume`/`total_volume` 时记录警告并跳过约束 |
| 百分比 | 若存在总量列则产出 `predicted_lag_percentage` |

#### 2.5 输出文件
| 路径 | 命名 | 含义 |
|------|------|------|
| `data/predictions/` | `CC_DAT_AI_PRE_OUT_{LATNID}_{prediction_month}.txt` | 单本地网预测结果 |
| `data/predictions/` | `prediction_summary_时间戳.json` | 摘要 (当配置 `prediction.generate_summary=true`) |

字段结构（按当前保存逻辑，若缺失会以0填补并记录日志）：
```
user|year|month|local_network|business_type|volume_type|gear_label|
prev_1m_total_volume|prev_1m_trigger_usage|prev_1m_lag_amount|
prev_2m_total_volume|prev_2m_trigger_usage|prev_2m_lag_amount|
predicted_lag_amount
```
（可选：若配置或后处理补充，会出现 predicted_lag_percentage 不同统计字段，不影响主文件格式。）

#### 2.6 使用示例
```bash
# 使用当前月作为预测目标月
python scripts/predict_model.py --latnid 912

# 指定预测目标月（要求存在前两个月原始数据）
python scripts/predict_model.py --latnid 912 --prediction-month 202509
```

#### 2.7 常见失败原因
| 类型 | 典型日志 | 处理建议 |
|------|----------|----------|
| 缺历史文件 | FileNotFoundError: 预测 202509 需要前两个月文件缺失 | 补齐 `202507/202508` 原始文件 |
| 全部清洗为空 | 所有数据集清洗后都为空 | 检查档位分布、总量/使用量为0异常 |
| 无可用模型 | 未找到任何可用的模型 | 确认已先完成对应本地网训练 |
| 特征转换失败 | 在线特征转换失败 | 检查列缺失/类型异常；回看清洗日志 |

#### 2.8 已弃用命令行参数对照
| 旧参数 | 状态 | 替代 |
|--------|------|------|
| `--data-dir` / `--output-dir` | 移除 | 使用配置 `data.raw_data_dir` / `data.predictions_dir` |
| `--apply-constraints` / `--no-constraints` | 移除 | 固定启用；未来可在配置新增开关 |
| `--generate-summary` / `--save-intermediate` | 移除 | 摘要由配置 `prediction.generate_summary` 控制；中间结果由 `feature_engineering.save_intermediate_prediction` 控制 |
| `--model-timestamp` | 移除 | 自动选择本地网最新模型 |
| `--input-file` | 移除 | 统一基于原始目录遍历与过滤 |

#### 2.9 退出码
| 退出码 | 含义 |
|--------|------|
| 0 | 预测成功（目标本地网结果文件生成） |
| 1 | 失败/异常（查看 `logs/model_prediction.log`） |
| 130 | 用户中断 (Ctrl+C) |

---

### 贝叶斯优化与单行进度条
当配置 `model.optimization_method=bayesian` 且命令行添加 `--enable-tuning` 时，训练阶段会启动贝叶斯超参数搜索，并显示单行实时进度条。进度条使用回车覆盖刷新，不会产生多行刷屏。

示例（截断展示）：
```
[本地网912] Bayes  15/ 50  30.0% [███████░░░░░░░░░░░░░░░░░] ⏱ 01:25/03:10 best=0.0213
```

字段说明：
| 字段 | 含义 |
|------|------|
| 本地网912 | 当前训练数据所属本地网 |
| 15/50 | 已完成迭代 / 总迭代次数 (n_calls) |
| 30.0% | 完成百分比 |
| 条形 | 长度30的可视化进度条 |
| ⏱ 01:25/03:10 | 已用时间 / 预计剩余时间 |
| best=0.0213 | 当前最佳分数（已将 neg 指标转换为正向可读值） |

启用方式：
1. 配置文件 `config/config.yaml`：
```yaml
model:
  optimization_method: bayesian
  bayesian_n_calls: 50
  cross_validation_folds: 5
```
2. 运行命令：
```bash
python scripts/train_model.py --latnid 912 --enable-tuning
```
3. 并行多本地网：
```bash
python scripts/batch_process.py --mode train --latnids 912 919 888 --enable-tuning --parallel-networks 3
```

测试保障：`tests/test_bayesian_progress_single_line.py` 会捕获标准输出，验证：
- 存在回车 `\r` 以实现单行覆盖
- 可见换行中包含 `Bayes` 的行数受控（不刷屏）
- 最终 100% 行出现

FAQ：
| 问题 | 原因 | 处理 |
|------|------|------|
| 仍出现多行 | 终端自动换行/窗口太窄/并行线程交错 | 放大窗口；减少并行；确认终端宽度 >= 90 |
| 没显示进度条 | 未启用 `--enable-tuning` 或 optimization_method 不是 bayesian | 添加参数并检查配置 |
| best 显示负数 | 使用自定义 scoring 未被识别 | 检查 scoring，若为 neg_ 前缀指标需在显示时取反 |
| 字符乱码 | 终端不支持 UTF-8 | 切换到 UTF-8 终端或移除特殊字符 |

后续可扩展：JSON 事件输出、静默模式(--no-progress)、WebSocket 实时流。

#### 预测结果文件格式
```
输出文件: CC_DAT_AI_PRE_OUT_912_202412_001.txt

字段结构:
用户|年|月|本地网|业务类型|量本类型|档位标签|
前一个月总量|前一个月触发时使用量|前一个月滞后量|
前二个月总量|前二个月触发时使用量|前二个月滞后量|
预测的滞后量

示例数据行:
138****1234|2024|12|912|1|1|0.6|1024|600|12.5|2048|1200|25.6|15.2
```

### 3. 模型评估

本章节依据当前源码（`scripts/evaluate_model.py` 最新入口 + `src/model/evaluator.py`）进行“窗口评估”说明，并剥离/标注已过时或重复的旧版 CLI 选项，确保与真实实现一致，避免误用。

#### 3.1 评估核心语义（单月窗口）
| 项 | 说明 |
|----|------|
| 评估目标月 (evaluation_month) | 已经真实发生且具备真实 `lag_amount` 的自然月 M（若未显式给出，默认=系统当前月-1） |
| 窗口加载 | 严格读取 {M, M-1, M-2} 三个月原始文件；缺任意文件且开启严格模式即终止 |
| 特征构造 | 复用训练模式：`FeatureExtractor.extract_all_features(mode='training', training_month=M)` 先生成“当前+历史”窗口，再过滤出“年=Y, 月=M”样本 |
| 样本筛选 | 仅对 M 当月样本计算指标（窗口内历史月只用于构造 prev_* 特征） |
| 档位过滤 | 评估前后统一剔除 `gear_label in {0,1}` |
| 目标列 | 使用原始（未裁剪）`lag_amount`；评估阶段不做 0.5%-3% 业务裁剪（避免与训练一致性破坏） |
| 预测裁剪 | 在评估逻辑中不会主动再次裁剪（预测时若模型为百分比目标已在 predictor 中 clip） |
| 特征对齐 | 优先使用模型元数据 `feature_names`，缺失列补 0，多余列丢弃，顺序严格对齐 |
| 回退策略 | 若元数据缺 `feature_names`：自动推导（排除订单/文件/ID等非特征列）并警告 |
| 输出 | JSON 报告（单/多数据集列表封装）、可选图表、日志摘要 |

#### 3.2 当前生效 CLI（新入口）
执行文件：`python scripts/evaluate_model.py`（文件首段“单月评估”实现）

| 参数 | 必填 | 默认 | 说明 |
|------|------|------|------|
| `--config / -c` | 否 | `config/config.yaml` | 配置文件路径 |
| `--latnid / -n` | 是 | - | 本地网编码，单本地网评估 |
| `--evaluation-month / -m` | 否 | 系统当前月-1 | 评估目标账期 YYYYMM |
| `--model-timestamp / -t` | 否 | 最新模型 | 指定模型保存时间戳（精准回溯） |
| `--generate-plots` | 否 | 关闭 | 打开后生成散点与残差图 |
| `--percentage-plots` | 否 | 关闭 | 需与 `--generate-plots` 同时使用，生成滞后量百分比四象限/分布图 |
| `--output-prefix` | 否 | 自动 | 自定义报告文件前缀 |
| `--strict-month-check` | 否 | True | 是否强制三个月原始文件全部存在 |
| `--log-level` | 否 | 配置级别 | 临时覆盖控制台日志级别（INFO / DEBUG ...） |
| `--backtest` | 否 | 未实现 | 传入 >1 仅提示“尚未实现”并返回码 2 |

> 说明：同一脚本后半段仍保留一个“旧版评估入口”（包含 `--compare-models`, `--business-metrics`, `--segment-analysis` 等）；当前推荐只使用上表“新入口”参数。若确需批量/比较功能，建议改造为独立脚本或调用 `ModelEvaluator.compare_models` API。README 不再鼓励使用旧混合参数集，避免歧义。

#### 3.3 最常用命令示例
```bash
# (1) 评估最新模型在上一个已完成账期的表现（最常用）
python scripts/evaluate_model.py -n 912 --generate-plots

# (2) 指定历史账期与特定模型时间戳（回溯验证）
python scripts/evaluate_model.py -n 912 -m 202508 -t 20250815123045 --generate-plots --percentage-plots

# (3) 禁用严格窗口（允许缺一月继续，适合排障）
python scripts/evaluate_model.py -n 912 -m 202508 --strict-month-check False

# (4) 调高日志粒度 & 自定义输出前缀
python scripts/evaluate_model.py -n 912 --log-level DEBUG --output-prefix eval_debug_912
```

#### 3.4 评估处理流程（代码对应函数）
1. 参数解析（`parse_arguments`）→ 账期与本地网校验
2. 构建数据：`EvaluationDataBuilder.build` 内部顺序：
  - `load_window_raw`：收集 {M, M-1, M-2} 原始文件（缺失策略受 `--strict-month-check` 控制）
  - `clean_and_validate`：调用 `DataCleaner.clean_data` → `DataValidator.validate_all` （非致命异常仅告警）→ 档位过滤
  - `extract_features_for_eval_month`：生成窗口特征后截取 evaluation_month 样本
3. 特征转换与对齐：`FeatureTransformer.fit_transform` → `transform_and_align`（元数据列补零/裁剪）
4. 模型加载：`ModelPredictor.load_model_by_local_network`
5. 预测：`ModelPredictor.predict`（若目标是 `lag_percentage` 会内置校准与 clip）
6. 指标计算：`ModelEvaluator.evaluate_predictions` 聚合三类指标 + 额外越界率统计
7. 报告输出：`generate_evaluation_report`（JSON）+ 可选图表生成（散点 / 残差 / 百分比）
8. 日志摘要：打印 RMSE / MAE / R2 / MAPE 与百分比越界率

#### 3.5 输出文件与结构
| 类型 | 位置 | 命名模式 | 说明 |
|------|------|---------|------|
| 评估报告(JSON) | `models/evaluations/` | `evaluation_{LATNID}_{YYYYMM}_<timestamp>.json` 或自定义前缀 | `individual_evaluations` 列表内含 dataset_name、basic/business/distribution_metrics |
| 图表-散点 | `models/evaluations/figures/` | `scatter_{dataset}_{timestamp}.png` | 真实 vs 预测 + 理想线 + R² |
| 图表-残差 | 同上 | `residuals_{dataset}_{timestamp}.png` | 四宫格：残差-预测 / 残差-真实 / 直方图 / Q-Q |
| 图表-百分比 | 同上 | `percentage_{dataset}_{timestamp}.png` | 仅在 `--percentage-plots` 下生成 |

报告主要字段（单数据集示例 `evaluation_912_202508_xxx.json`）：
```json
{
  "report_name": "evaluation_912_202508_20250915_142355",
  "generation_timestamp": "2025-09-15T14:23:55.123456",
  "total_datasets": 1,
  "individual_evaluations": [
   {
    "dataset_name": "912_202508",
    "sample_count": 18342,
    "basic_metrics": {"rmse": 12.34, "mae": 7.89, "r2": 0.8421, "mape": 6.52, ...},
    "business_metrics": {"percentage_rmse": 0.41, "pred_in_range_rate": 0.976, ...},
    "distribution_metrics": {"q25_true": -5.1, "q25_pred": -4.9, ...},
    "prediction_percentage_out_of_range_low": 0.0071,
    "prediction_percentage_out_of_range_high": 0.0114
   }
  ],
  "summary_statistics": {"basic_metrics_summary": {"rmse_mean": 12.34, ...}}
}
```
（示例数值仅示意；真实结果由运行期计算。）

#### 3.6 指标体系
1) 基础回归指标（`calculate_basic_metrics`）：
  - MSE / RMSE: 预测误差平方均值及其平方根
  - MAE: 平均绝对误差
  - R2: 决定系数
  - MAPE (%): 过滤 `y_true==0` 后按 |(y_pred - y_true)/y_true| 汇总
  - mean_error / std_error: 误差均值与标准差（检视系统性偏移）
2) 业务指标（`calculate_business_metrics`，需 `total_volume`）：
  - percentage_mae / percentage_rmse: 以 (lag_amount / total_volume *100) 计算的百分比误差
  - pred_in_range_rate: 预测百分比落在 [min_lag_percentage, max_lag_percentage] 的比例
  - pred_high_risk_rate: 预测百分比 > max_lag_percentage 的占比（潜在过度过滤）
  - pred_miss_risk_rate: 预测百分比 < risk_threshold 的占比（潜在漏发风险）
  - 对应 true_* 指标用于与真实行为对比衡量模型策略一致性
3) 分布指标（`calculate_distribution_metrics`）：
  - 分位数: q25/q50/q75/q90/q95_true 与对应预测及差值
  - 均值/标准差: mean_true / mean_pred / std_true / std_pred（检测分布漂移）
4) 越界率附加（评估主流程中计算）：
  - prediction_percentage_out_of_range_low / high：预测百分比低于最小、高于最大阈值的比例

#### 3.7 典型解读指引
| 现象 | 可能原因 | 排查建议 |
|------|----------|---------|
| RMSE 明显低但 MAPE 高 | 存在大量小幅度真实值接近 0，导致相对误差放大 | 聚焦低总量/低真实滞后量分布；可引入加权指标 |
| pred_in_range_rate 远高于 true_in_range_rate | 模型过度集中在阈值区间（过拟合裁剪策略） | 检查训练标签是否被错误裁剪；核对 predictor 是否重复 clip |
| high_risk_pred 明显高 | 模型在高量用户上偏高估 | 分档（gear_label）+ 总量分层查看残差；考虑特征归一或加权 |
| R2 良好但残差图呈漏斗形 | 异方差：高总量用户误差方差增大 | 引入对数变换或分段建模策略（后续迭代项） |
| q95_pred >> q95_true | 尾部风险偏高（潜在策略性过预测） | 分析最大值样本，核对是否缺失历史特征被补零 |

#### 3.8 与训练 & 预测阶段的对比
| 维度 | 训练 | 预测 | 评估 |
|------|------|------|------|
| 标签处理 | 使用原始 lag_amount（可负） | 模型输出经业务裁剪（百分比模式 clip） | 使用原始真实 lag_amount 与“已裁剪或未裁剪”的模型输出进行对照（不再额外裁剪） |
| 时间窗口 | (T, T-1, T-2) → 标签=T | (P-1, P-2) → 推断 P | (M, M-1, M-2) → 指标仅 M |
| 特征对齐 | 训练生成并记录 feature_names | 依据模型 `feature_names_in_` / 元数据补齐 | 使用元数据 `feature_names` 优先；缺失自动推导 |
| 业务指标 | 不评估百分比覆盖 | 预测后应用裁剪 | 统计预测百分比越界率（不再修改预测值） |

#### 3.9 已过时 / 不推荐参数 & 原因
| 参数 (旧文档/旧入口) | 状态 | 说明 |
|----------------------|------|------|
| `--compare-models` | 旧脚本存在 | 新推荐：调用 `ModelEvaluator.compare_models` 自行组装列表；或后续拆分独立脚本 |
| `--business-metrics` / `--segment-analysis` | 旧脚本存在 | 新入口始终计算业务指标（若有 total_volume）；细分分析未在新入口暴露 |
| `--data-dir` / `--model-dir` / `--output-dir` | 旧脚本存在 | 路径统一走配置，减少 CLI 分歧 |
| `--no-plots` / `--detailed-analysis` | 旧脚本存在 | 新入口用显式 `--generate-plots` + `--percentage-plots` 组合表达 |

#### 3.10 质量核查清单（提交或扩展评估功能前）
1. 是否仍保证窗口加载与训练语义对齐（M, M-1, M-2）
2. 是否未在评估阶段对真实标签或预测值做业务裁剪（除百分比模型内部 clip）
3. 新增指标是否写入 JSON 报告并命名清晰（避免覆盖现有键）
4. 若新增特征：元数据 `feature_names` 是否随模型保存更新
5. 图表新增是否保持单文件内 2x2 或适合布局，防止目录膨胀
6. 日志是否避免循环大量打印（评估通常单月）

#### 3.11 后续可扩展路线（建议）
| 需求 | 描述 | 技术要点 |
|------|------|---------|
| 多月回测 | `--backtest N` 同步输出 N 个月曲线 | 循环 evaluation_month 递减；合并 summary_statistics |
| 漂移监控 | 特征/滞后量分布月度漂移 | 计算 PSI / KL / KS 指标；追加 `drift_metrics` 字段 |
| 分档残差分解 | gear_label & total_volume 分层箱线 | 统一绘制栅格图；输出 JSON 分层统计 |
| 业务阈值敏感性 | 测试不同 0.4%-0.8% / 2%-4% 范围影响 | 参数化重算 in_range / risk 指标曲线 |
| 比较报告统一化 | 多模型 / 多月对比矩阵 | 复用 `compare_models` + pivot 高级封装 |

#### 3.12 快速 FAQ
| 问题 | 现象 | 处理建议 |
|------|------|---------|
| 报错缺失窗口文件 | FileNotFoundError | 确认 `--evaluation-month` 前两月文件是否放入 `data/raw` 且命名规范 |
| 评估数据为空 | “清洗后无有效数据” | 检查档位 0/1 占比；确认 total_volume>0 与使用量>=0 逻辑 |
| 元数据无 feature_names | 日志 WARN 自动推导 | 需重训模型以写回元数据；否则生产一致性风险 |
| 百分比指标缺失 | business_metrics 为空 | 特征中缺 `total_volume`；排查特征提取链路是否被裁剪列 |
| MAPE=inf | y_true 全为 0（或过滤后无非零） | 业务上该月极端；关注 mean_error/分布指标替代 |
| 越界率异常高 | out_of_range_high / low > 0.2 | 排查预测是否被重复 clip 或总量列异常（0 / 缺失导致放大） |

> 若需脚本级“模型集合横向比较”或“多月批量评估”，请勿复制旧 CLI；应新增独立 `evaluate_batch.py` 保持单一职责，避免再次出现双入口拼接问题。

---


### 4. 批量处理 (自动化流程)

#### 完整流程自动化
```bash
# 全流程自动化（训练→预测→评估）
python scripts/batch_process.py --mode full_pipeline

# 仅批量训练多个本地网
python scripts/batch_process.py --mode train --latnids 912 888 919

# 仅批量预测
python scripts/batch_process.py --mode predict --parallel-networks 3
```

#### 生产环境部署
```bash
# 启用超参数调优的批量训练（适合定期重训）
python scripts/batch_process.py --mode train --enable-tuning --generate-reports

# 强制重训所有模型（适合数据更新后）
python scripts/batch_process.py --mode full_pipeline --config config/config.yaml

# 干运行模式（仅显示执行计划，不实际运行）
python scripts/batch_process.py --mode full_pipeline --dry-run
```

### 5. API编程接口

#### 数据处理API
```python
from src.data_processor import DataLoader, DataCleaner, DataValidator

# 数据加载
loader = DataLoader('config/config.yaml')
data_dict = loader.load_data_by_local_network(['912', '888'])  # 按本地网加载
recent_data = loader.load_recent_months_data(months=3)         # 加载近3个月数据

# 数据清洗
cleaner = DataCleaner('config/config.yaml')
clean_data = cleaner.clean_data(data_dict['912'])              # 清洗单个数据集
batch_clean = cleaner.clean_batch_data(data_dict)             # 批量清洗

# 数据验证
validator = DataValidator('config/config.yaml')
is_valid, report = validator.validate_data(clean_data)        # 验证数据质量
```

#### 特征工程API
```python
from src.feature_engineer import FeatureExtractor, FeatureTransformer

# 特征提取
extractor = FeatureExtractor('config/config.yaml')
features = extractor.extract_all_features(clean_data)         # 提取所有特征
time_features = extractor.create_time_series_features(data)   # 创建时序特征

# 特征保存（如果需要手动再次导出，可调用）
extractor.save_features_to_file(features, local_network="912", timestamp="202412")

# 特征转换
transformer = FeatureTransformer('config/config.yaml')
transformed = transformer.transform_features(features)        # 标准化转换
```

#### 模型训练API
```python
from src.model import ModelTrainer, ModelPredictor

# 模型训练
trainer = ModelTrainer('config/config.yaml')

# 数据准备和分割
X_train, X_test, y_train, y_test = trainer.prepare_data(features, 
                                                        target_column='lag_amount')

# 训练模型
model = trainer.train_model(X_train, y_train, algorithm='random_forest')

# 模型评估
metrics = trainer.evaluate_model(model, X_test, y_test)
print(f"模型性能: R² = {metrics['r2_score']:.4f}, RMSE = {metrics['rmse']:.4f}")

# 模型保存（按设计文档格式）
trainer.save_model(model, model_name="912", timestamp="202412")
```

#### 模型预测API
```python
# 模型预测
predictor = ModelPredictor('config/config.yaml')

# 加载模型
model_path = "models/trained/CC_DAT_AI_TRAIN_OUT_912_202412.pkl"
model = predictor.load_model(model_path)

# 单次预测
predictions = predictor.predict(model, features)

# 批量预测（应用业务约束）
batch_pred = predictor.predict_with_constraints(model, features, 
                                               min_pct=0.5, max_pct=3.0)

# 按本地网批量预测
network_pred = predictor.predict_by_local_network(data_dict)
```

### 6. 配置管理

#### 核心配置文件结构
```yaml
# config/config.yaml
data:
  raw_data_dir: "data/raw"
  processed_data_dir: "data/processed"
  train_file_pattern: "CC_DAT_AI_TRAIN_*_*.txt"
  train_test_split: 0.8

model:
  algorithm: "random_forest"               # 主要算法
  trained_models_dir: "models/trained"
  random_forest:
    n_estimators: 100
    max_depth: 10
    min_samples_split: 5
    random_state: 42

feature_engineering:
  gear_labels: [0.2, 0.4, 0.6, 0.8, 1.0]  # 档位标签
  time_window_months: 3                    # 时间窗口

business_rules:
  min_lag_percentage: 0.5                  # 最小滞后量边界
  max_lag_percentage: 3.0                  # 最大滞后量边界
  filter_gear_labels: [0, 1]              # 过滤档位

processing:
  max_workers: 4                           # 最大并行数
  chunk_size: 10000                        # 数据块大小

logging:
  level: 4                                 # 日志级别(4=INFO)
  log_dir: "logs"
```

#### 运行时配置覆盖
```bash
# 通过命令行参数覆盖配置
python scripts/train_model.py \
  --model-type "XGBoost" \                 # 覆盖算法类型
  --validation-split 0.25 \               # 覆盖验证集比例
  --log-level DEBUG                       # 覆盖日志级别
```

## 技术规范

### 设计文档合规性

本项目严格按照《滞后量模型实现方案》设计文档实现，确保生产环境完全兼容：

#### ✅ 数据处理流程合规
```
合规要点:
├── 多线程处理: 不同线程处理不同本地网数据，提高处理效率
├── 时间窗口: 使用近3个月的历史数据进行建模  
├── 用户聚合: 按用户+本地网+业务类型+量本类型+档位标签分组
├── 时间戳处理: 取每个月内时间戳最大的数据记录
└── 数据过滤: 自动过滤档位标签为0和1的数据
```

#### ✅ 特征工程格式合规
```
输出字段完全符合设计文档要求(16个字段):
用户|年|月|本地网|业务类型|量本类型|档位标签|总量|触发时使用量|滞后量|
前一个月总量|前一个月触发时使用量|前一个月滞后量|
前二个月总量|前二个月触发时使用量|前二个月滞后量

关键特点:
├── 目标变量: 字段名统一为 lag_amount（滞后量）
├── 历史特征: 前1个月和前2个月的完整数据
├── 字段顺序: 严格按照设计文档规范排列
└── 数据类型: 所有数值字段保持正确的数据类型
```

#### ✅ 文件命名格式合规
```
严格按照设计文档要求:
├── 特征文件: CC_DAT_AI_TRAIN_OUT_本地网_时间(YYYYMM)_序号.txt
├── 模型文件: CC_DAT_AI_TRAIN_OUT_本地网_时间(YYYYMM).PKL
├── 时间格式: 统一使用YYYYMM格式（如202412）
├── 序号格式: 三位数递增（001, 002, 003...）
└── 分割规则: 每文件最多50万行，超过自动分割
```

#### ✅ 模型训练规范合规
```
训练规范:
├── 算法选择: 使用随机森林回归算法（符合设计文档要求）
├── 数据分割: 严格按照80%训练集，20%测试集分割
├── 超参数调优: 支持网格搜索优化模型参数
├── 模型保存: 按照规范格式保存模型和元数据
└── 性能验证: 内置交叉验证和性能指标计算
```

#### ✅ 业务规则约束合规
```
业务约束:
├── 档位标签: 支持 [0.2, 0.4, 0.6, 0.8, 1.0] 五个标准档位
├── 预测边界: 滞后量预测值严格控制在0.5%-3%范围内
├── 风险控制: h < 0.5%时自动调整为0.5%（避免漏发）
├── 上限保护: h > 3%时自动调整为3%（避免过度过滤）
└── 质量保证: 内置数据验证和业务规则检查机制
```

### 算法技术规范

#### 主要算法：随机森林回归
```python
RandomForestRegressor参数配置:
├── n_estimators: 100        # 决策树数量
├── max_depth: 10           # 最大树深度
├── min_samples_split: 5    # 分割所需最小样本数
├── min_samples_leaf: 2     # 叶节点最小样本数
├── random_state: 42        # 随机种子，确保结果可重复
└── n_jobs: -1             # 并行作业数，-1表示使用全部CPU核心

算法优势:
├── 鲁棒性强: 对异常值和噪声不敏感
├── 可解释性: 提供特征重要性排序
├── 泛化能力: 有效避免过拟合问题
└── 稳定性好: 适合电信业务场景的稳定预测需求
```

#### 备选算法支持
```python
XGBoost配置:
├── n_estimators: 100
├── max_depth: 6
├── learning_rate: 0.1
├── subsample: 0.8
└── colsample_bytree: 0.8

LightGBM配置:
├── n_estimators: 100
├── max_depth: 6
├── learning_rate: 0.1
├── num_leaves: 31
└── subsample: 0.8
```

### 性能规范

#### 系统性能指标
```
处理能力:
├── 数据吞吐: 支持百万级数据记录处理
├── 并行处理: 最多支持4个本地网同时训练
├── 内存占用: 单次训练<4GB内存
├── 训练时间: 单本地网模型训练<5分钟
└── 预测速度: 万级数据预测<30秒

存储需求:
├── 原始数据: 每月每本地网~100MB
├── 特征数据: 每月每本地网~50MB  
├── 模型文件: 每个模型<10MB
├── 日志文件: 每天<50MB
└── 总空间: 建议预留5GB+
```

#### 模型性能标准
```
预测精度要求:
├── R² Score: ≥ 0.8         # 模型解释度
├── RMSE: ≤ 0.05           # 均方根误差
├── MAE: ≤ 0.03            # 平均绝对误差
├── 约束内比例: ≥ 95%      # 预测值在0.5%-3%范围内的比例
└── 业务准确性: ≥ 90%      # 业务场景下的预测准确性
```

### 数据规范

#### 输入数据质量标准
```
数据完整性:
├── 必需字段: 9个核心字段不能缺失
├── 数据类型: 数值字段必须为有效数值
├── 取值范围: 档位标签在[0.2, 0.4, 0.6, 0.8, 1.0]内
├── 时间格式: 精确到分/秒的标准时间戳
└── 编码格式: 严格使用UTF-8编码

数据一致性:
├── 本地网编码: 使用标准三位数编码（如912, 888）
├── 业务类型: 0=语音, 1=数据
├── 量本类型: 通用量本固定为1
├── 单位换算: 流量MB, 语音分钟
└── 字段分隔: 统一使用竖线(|)分隔
```

#### 输出数据格式标准
```
特征文件标准:
├── 字段数量: 固定16个字段
├── 字段顺序: 严格按照设计文档排列
├── 文件大小: 单文件≤50万行记录
├── 命名规范: CC_DAT_AI_TRAIN_OUT_本地网_时间_序号.txt
└── 编码格式: UTF-8编码，竖线分隔

模型文件标准:
├── 格式类型: pickle格式(.pkl)
├── 命名规范: CC_DAT_AI_TRAIN_OUT_本地网_时间.pkl
├── 版本管理: 包含模型训练时间戳
├── 元数据: 同步生成JSON格式元数据文件
└── 向后兼容: 支持模型版本升级和回退
```

## 常见问题与故障排除

### 安装部署问题

#### Q: Python版本不兼容怎么办？
```bash
# 检查当前Python版本
python --version

# 如果版本 < 3.8，需要升级
# 推荐使用pyenv管理Python版本
pyenv install 3.12.4
pyenv global 3.12.4

# 重新创建虚拟环境
rm -rf venv
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/macOS
```

#### Q: 依赖包安装失败？
```bash
# 升级pip和setuptools
pip install --upgrade pip setuptools wheel

# 清理缓存后重新安装
pip cache purge
pip install -r requirements.txt

# 如果仍有问题，逐个安装核心包
pip install numpy pandas scikit-learn xgboost lightgbm
```

#### Q: Windows环境编码问题？
```bash
# 设置环境变量
set PYTHONIOENCODING=utf-8

# 或在代码中指定编码
python -c "import sys; print(sys.getdefaultencoding())"  # 应显示utf-8
```

### 数据处理问题

#### Q: 数据文件格式不正确？
```bash
# 检查文件编码
file -i data/raw/CC_DAT_AI_TRAIN_912_202411_001.txt

# 应显示类似: text/plain; charset=utf-8

# 检查字段分隔符
head -1 data/raw/CC_DAT_AI_TRAIN_912_202411_001.txt | tr '|' '\n' | wc -l
# 应显示 9 (表示9个字段)

# 转换编码（如果需要）
iconv -f GBK -t UTF-8 input.txt -o output.txt
```

#### Q: 数据量太大导致内存不足？
```yaml
# 修改配置文件 config/config.yaml
processing:
  chunk_size: 5000        # 减小块大小
  max_workers: 2          # 减少并行数

data:
  use_chunked_loading: true  # 启用分块加载
```

#### Q: 特定本地网数据缺失？
```python
# 检查可用的本地网数据
from src.data_processor import DataLoader
loader = DataLoader()
files = loader.find_data_files()
print("可用数据文件:", files)

# 检查数据分布
import pandas as pd
for file in files:
    df = pd.read_csv(file, sep='|', encoding='utf-8')
    print(f"{file}: {df['local_network'].unique()}")
```

### 模型训练问题

#### Q: 模型训练精度过低？
```bash
# 检查数据质量
python -c "
from src.data_processor import DataValidator
validator = DataValidator()
data = pd.read_csv('data/raw/CC_DAT_AI_TRAIN_912_202411_001.txt', sep='|')
is_valid, report = validator.validate_data(data)
print('数据质量报告:', report)
"

# 尝试启用超参数调优
python scripts/train_model.py --latnid "912" --enable-tuning --cv-folds 5

# 尝试不同算法
python scripts/train_model.py --latnid "912" --model-type "XGBoost"
```

#### Q: 训练速度太慢？
```yaml
# 优化配置参数
model:
  random_forest:
    n_estimators: 50      # 减少树的数量
    max_depth: 8          # 降低树深度
    n_jobs: -1           # 使用全部CPU核心

processing:
  max_workers: 4          # 增加并行处理数
  chunk_size: 10000       # 适当增大块大小
```

#### Q: 模型文件保存失败？
```bash
# 检查磁盘空间
df -h  # Linux/macOS
dir   # Windows

# 检查目录权限
ls -la models/trained/

# 手动创建必要目录
mkdir -p models/{trained,metadata,backup}
chmod 755 models/trained
```

### 预测服务问题

#### Q: 预测结果全为边界值？
```python
# 检查原始预测值
from src.model import ModelPredictor
predictor = ModelPredictor()

# 禁用约束查看原始预测
predictions = predictor.predict(model, X_test, apply_constraints=False)
print("原始预测值范围:", predictions.min(), "-", predictions.max())

# 如果范围异常，可能需要重新训练模型
```

#### Q: 预测速度太慢？
```bash
# 减少预测批次大小
python scripts/predict_model.py --batch-size 1000 --latnid "912"

# 关闭不必要的功能
python scripts/predict_model.py --no-constraints --latnid "912"
```

#### Q: 预测结果文件格式不对？
```python
# 验证输出格式
import pandas as pd
df = pd.read_csv('data/predictions/CC_DAT_AI_PRE_OUT_912_202412_001.txt', sep='|')
print("字段数量:", len(df.columns))
print("字段名称:", df.columns.tolist())
print("数据类型:", df.dtypes)

# 应该有13个字段，最后一个是预测的滞后量
```

### 性能监控问题

#### Q: 如何监控模型性能变化？
```bash
# 定期评估模型
python scripts/evaluate_model.py --latnid "912" --generate-plots

# 比较不同时间训练的模型
python scripts/evaluate_model.py --compare-models --models-dir models/trained/

# 生成性能趋势报告
python scripts/evaluate_model.py --detailed-analysis --business-metrics
```

#### Q: 系统资源占用过高？
```bash
# 监控资源使用
python -c "
import psutil
print('CPU使用率:', psutil.cpu_percent())
print('内存使用率:', psutil.virtual_memory().percent)
print('磁盘使用率:', psutil.disk_usage('.').percent)
"

# 优化资源配置
# 减少并行处理数
# 使用分块处理
# 及时清理临时文件
```

### 日志分析技巧

#### 查看关键日志信息
```bash
# 查看训练日志
tail -f logs/model_trainer.log

# 查看错误日志
grep "ERROR" logs/*.log

# 查看性能日志
grep "耗时\|完成" logs/model_trainer.log

# 查看数据处理日志
tail -f logs/data_processor.log
```

#### 日志级别调整
```bash
# 启用调试模式
python scripts/train_model.py --log-level DEBUG --latnid "912"

# 查看详细执行过程
python scripts/train_model.py --log-level DEBUG 2>&1 | tee debug.log
```

## 生产部署指南

### 定时任务配置

#### Linux Cron作业
```bash
# 编辑crontab
crontab -e

# 每月1日凌晨2点重新训练模型
0 2 1 * * cd /path/to/PredicH_model && python scripts/batch_process.py --mode train >> logs/cron.log 2>&1

# 每天凌晨3点执行预测
0 3 * * * cd /path/to/PredicH_model && python scripts/predict_model.py --data-dir data/raw >> logs/predict_cron.log 2>&1

# 每周日凌晨4点执行模型评估
0 4 * * 0 cd /path/to/PredicH_model && python scripts/evaluate_model.py --generate-reports >> logs/evaluate_cron.log 2>&1
```

#### Windows任务计划
```powershell
# 创建训练任务
schtasks /create /tn "AI模型训练" /tr "python scripts/train_model.py" /sc monthly /d 1 /st 02:00

# 创建预测任务
schtasks /create /tn "AI模型预测" /tr "python scripts/predict_model.py --data-dir data/raw" /sc daily /st 03:00
```

### 监控告警

#### 系统监控脚本
```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

LOG_DIR="logs"
ALERT_EMAIL="<EMAIL>"

# 检查磁盘空间
DISK_USAGE=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "警告: 磁盘使用率 ${DISK_USAGE}%" | mail -s "磁盘空间不足" $ALERT_EMAIL
fi

# 检查错误日志
ERROR_COUNT=$(grep -c "ERROR" $LOG_DIR/*.log)
if [ $ERROR_COUNT -gt 10 ]; then
    echo "警告: 发现 ${ERROR_COUNT} 个错误" | mail -s "系统错误告警" $ALERT_EMAIL
fi

# 检查模型文件
MODEL_COUNT=$(find models/trained -name "*.pkl" -mtime -7 | wc -l)
if [ $MODEL_COUNT -eq 0 ]; then
    echo "警告: 一周内无新模型文件生成" | mail -s "模型更新告警" $ALERT_EMAIL
fi
```

### 备份恢复

#### 自动备份脚本
```bash
#!/bin/bash
# backup.sh - 自动备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/predicH_model_$DATE"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份关键文件
cp -r config/ $BACKUP_DIR/
cp -r models/ $BACKUP_DIR/
cp -r data/processed/ $BACKUP_DIR/
cp -r logs/ $BACKUP_DIR/

# 压缩备份
tar -czf "${BACKUP_DIR}.tar.gz" $BACKUP_DIR
rm -rf $BACKUP_DIR

echo "备份完成: ${BACKUP_DIR}.tar.gz"
```

#### 恢复操作
```bash
# 解压备份文件
tar -xzf predicH_model_20241209_140000.tar.gz

# 恢复配置文件
cp -r predicH_model_20241209_140000/config/ ./

# 恢复模型文件
cp -r predicH_model_20241209_140000/models/ ./

# 验证恢复结果
python -c "
from src.model import ModelPredictor
predictor = ModelPredictor()
models = predictor.list_available_models()
print('可用模型:', models)
"
```

## 版本更新日志

### v1.2.0 (2024-12-09) - 完整文档重构
#### 重大更新
- 📚 **README文档全面重构**: 基于深入的源码分析，重写了完整、专业、严谨的文档
- 🎯 **业务逻辑明确**: 详细说明滞后量预测的核心业务价值和技术实现
- 🏗️ **架构图完善**: 提供清晰的分层架构图和数据流图
- 📋 **使用指南详尽**: 包含完整的安装、配置、使用和故障排除指南
- 🔧 **API文档补充**: 提供详细的编程接口使用示例
- 📊 **技术规范明确**: 明确性能指标、数据格式和合规性要求

#### 文档改进内容
- ✅ 项目概述更加专业，突出业务价值
- ✅ 架构设计图表化，便于理解技术实现
- ✅ 快速开始指南详细，降低上手门槛
- ✅ 使用示例完整，覆盖所有主要功能
- ✅ 故障排除全面，提供实用解决方案
- ✅ 生产部署指导，支持企业级应用

### v1.1.0 (2024-12-XX) - 设计文档合规性修复
#### 重大更新
- 🔧 **特征工程格式修复**: 输出字段完全符合设计文档要求
- 🔧 **文件命名规范修复**: 严格按照设计文档要求
- 🔧 **大文件分割功能**: 新增按设计文档要求的文件分割
- 🔧 **多线程训练优化**: 实现真正的并行训练
- 🔧 **训练流程验证**: 确保80%训练集，20%测试集分割比例

### v1.0.0 (2024-01-XX) - 初始版本
#### 核心功能
- ✅ 完整的滞后量预测模型实现
- ✅ 支持多种机器学习算法
- ✅ 完善的数据处理和特征工程流程
- ✅ 灵活的配置管理系统
- ✅ 全面的测试覆盖

## 技术支持

### 项目信息
- **项目名称**: 滞后量预测模型 (PredicH Model)
- **开发团队**: 天源迪科·陕西电信项目组
- **技术栈**: Python 3.8+, scikit-learn, XGBoost, pandas
- **部署环境**: Windows/Linux生产环境
- **维护状态**: 积极维护中

### 联系方式
- **项目负责人**: 开发团队负责人
- **技术支持**: 天源迪科技术支持团队
- **业务咨询**: 陕西电信项目组
- **问题反馈**: 请通过项目Issue系统提交

### 许可与合规
- **版权声明**: 本项目版权归天源迪科科技有限公司所有
- **使用许可**: 仅供陕西电信内部使用，未经授权不得复制或分发
- **合规保证**: 严格遵循电信行业数据安全和隐私保护规范
- **技术支持**: 提供完整的技术文档和专业技术支持服务

---

**最后更新**: 2024年12月9日  
**文档版本**: v1.2.0  
**适用环境**: 陕西电信生产环境  
**维护团队**: 天源迪科开发团队