2025-09-16 10:33:49:885173|model_training|9224|1892|INFO|train_model.py:101|==================================================
2025-09-16 10:33:49:885173|model_training|9224|1892|INFO|train_model.py:102|开始模型训练流程 (单本地网强制模式)
2025-09-16 10:33:49:885173|model_training|9224|1892|INFO|train_model.py:103|配置文件: config/config.yaml
2025-09-16 10:33:49:885173|model_training|9224|1892|INFO|train_model.py:104|数据目录: data/raw
2025-09-16 10:33:49:885173|model_training|9224|1892|INFO|train_model.py:105|输出目录: models/trained
2025-09-16 10:33:49:885173|model_training|9224|1892|INFO|train_model.py:106|==================================================
2025-09-16 10:33:49:899102|model_training|9224|1892|INFO|train_model.py:109|步骤 1: 数据加载
2025-09-16 10:33:49:899102|model_training|9224|1892|INFO|train_model.py:132|预测目标月: 202509; 训练月(当前样本月): 202508; 加载历史窗口: ['202506', '202507', '202508']
2025-09-16 10:33:49:899102|model_training|9224|1892|INFO|train_model.py:137|数据加载子步骤: 按需选择历史月份文件并加载
2025-09-16 10:33:51:357101|model_training|9224|1892|INFO|train_model.py:160|按需加载完成: 行数 519327; 覆盖月份 ['202506', '202507', '202508'] (不含预测月 202509)
2025-09-16 10:33:51:357101|model_training|9224|1892|INFO|train_model.py:163|步骤 2: 数据清洗
2025-09-16 10:34:00:611918|model_training|9224|1892|INFO|train_model.py:174|数据集 919: 清洗前 519327 行，清洗后 71338 行
2025-09-16 10:34:00:611918|model_training|9224|1892|INFO|train_model.py:179|数据集 919: 记录过滤行 447855 行 (含原因)
2025-09-16 10:34:00:611918|model_training|9224|1892|INFO|train_model.py:186|步骤 3: 数据验证
2025-09-16 10:34:21:039215|model_training|9224|1892|INFO|train_model.py:205|数据集 919: 验证阶段新增无效标记 23296 行
2025-09-16 10:35:33:243879|model_training|9224|1892|INFO|train_model.py:243|过滤数据文件已生成: data/processed/filtered\CC_DAT_AI_TRAIN_FILTER_919_202508_01.txt (行数: 471151)
2025-09-16 10:35:33:243879|model_training|9224|1892|INFO|train_model.py:248|步骤 4: 特征工程
2025-09-16 10:37:29:705314|model_training|9224|1892|INFO|train_model.py:259|数据集 919: 提取了 16 个特征
2025-09-16 10:37:29:938821|model_training|9224|1892|INFO|train_model.py:272|数据集 919: 特征转换完成 (列数: 25) — 已禁用转换器持久化
2025-09-16 10:37:29:938821|model_training|9224|1892|INFO|train_model.py:278|数据集 919: 准备了 25 个特征用于训练
2025-09-16 10:37:29:938821|model_training|9224|1892|INFO|train_model.py:293|调用 export_business_features 生成业务标准特征文件
2025-09-16 10:37:30:697430|model_training|9224|1892|INFO|train_model.py:299|本地网 919 的业务标准特征数据已保存 -> 目录: data/processed
2025-09-16 10:37:30:697430|model_training|9224|1892|INFO|train_model.py:304|步骤 5: 模型训练
2025-09-16 10:42:14:273109|model_training|9224|1892|INFO|train_model.py:329|步骤 6: 训练结果汇总
2025-09-16 10:42:14:273109|model_training|9224|1892|INFO|train_model.py:337|本地网 919: 训练成功
2025-09-16 10:42:14:273109|model_training|9224|1892|INFO|train_model.py:338|  - 模型文件: models/trained\CC_DAT_AI_TRAIN_OUT_919_202508.pkl
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:357|  - 已更新元数据: 写入 feature_names(23)
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:366|  - 测试RMSE: 0.0102
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:368|  - R²得分: 0.9999
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:373|  - CV RMSE均值: 0.0111
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:379|==================================================
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:380|训练完成汇总:
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:381|成功训练模型: 1
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:382|训练失败模型: 0
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:383|总模型数: 1
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:386|模型保存目录: models/trained
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:387|元数据保存目录: models/metadata
2025-09-16 10:42:14:288738|model_training|9224|1892|INFO|train_model.py:389|==================================================
