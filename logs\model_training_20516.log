2025-09-16 16:44:02:715933|model_training|20516|21660|INFO|train_model.py:173|==================================================
2025-09-16 16:44:02:715933|model_training|20516|21660|INFO|train_model.py:174|开始模型训练流程 (单本地网强制模式)
2025-09-16 16:44:02:715933|model_training|20516|21660|INFO|train_model.py:175|配置文件: config/config.yaml
2025-09-16 16:44:02:716933|model_training|20516|21660|INFO|train_model.py:176|数据目录: data/raw
2025-09-16 16:44:02:716933|model_training|20516|21660|INFO|train_model.py:177|输出目录: models/trained
2025-09-16 16:44:02:716933|model_training|20516|21660|INFO|train_model.py:178|==================================================
2025-09-16 16:44:02:717933|model_training|20516|21660|INFO|train_model.py:181|步骤 1: 数据加载
2025-09-16 16:44:02:717933|model_training|20516|21660|INFO|train_model.py:197|预测目标月: 202509; 训练月(当前样本月): 202508; 加载历史窗口: ['202506', '202507', '202508']
2025-09-16 16:44:02:717933|model_training|20516|21660|INFO|train_model.py:202|数据加载子步骤: 按需选择历史月份文件并加载
2025-09-16 16:44:05:303387|model_training|20516|21660|INFO|train_model.py:225|按需加载完成: 行数 519327; 覆盖月份 ['202506', '202507', '202508'] (不含预测月 202509)
2025-09-16 16:44:05:303387|model_training|20516|21660|INFO|train_model.py:228|步骤 2: 数据清洗
2025-09-16 16:44:11:559497|model_training|20516|21660|INFO|train_model.py:239|数据集 919: 清洗前 519327 行，清洗后 48042 行
2025-09-16 16:44:11:560080|model_training|20516|21660|INFO|train_model.py:244|数据集 919: 记录过滤行 471151 行 (含原因)
2025-09-16 16:44:11:560609|model_training|20516|21660|INFO|train_model.py:251|步骤 3: 数据验证
2025-09-16 16:45:09:338197|model_training|20516|21660|INFO|train_model.py:308|过滤数据文件已生成: data/processed/filtered\CC_DAT_AI_TRAIN_FILTER_919_202508_01.txt (行数: 471151)
2025-09-16 16:45:09:339162|model_training|20516|21660|INFO|train_model.py:313|步骤 4: 特征工程
2025-09-16 16:47:26:897945|model_training|20516|21660|INFO|train_model.py:324|数据集 919: 提取了 16 个特征
2025-09-16 16:47:27:079917|model_training|20516|21660|INFO|train_model.py:337|数据集 919: 特征转换完成 (列数: 25) — 已禁用转换器持久化
2025-09-16 16:47:27:080914|model_training|20516|21660|INFO|train_model.py:343|数据集 919: 准备了 25 个特征用于训练
2025-09-16 16:47:27:081911|model_training|20516|21660|INFO|train_model.py:358|调用 export_business_features 生成业务标准特征文件
2025-09-16 16:47:27:662824|model_training|20516|21660|INFO|train_model.py:364|本地网 919 的业务标准特征数据已保存 -> 目录: data/processed
2025-09-16 16:47:27:662824|model_training|20516|21660|INFO|train_model.py:369|步骤 5: 模型训练
2025-09-16 16:50:27:595999|model_training|20516|21660|INFO|train_model.py:394|步骤 6: 训练结果汇总
2025-09-16 16:50:27:597000|model_training|20516|21660|INFO|train_model.py:402|本地网 919: 训练成功
2025-09-16 16:50:27:598000|model_training|20516|21660|INFO|train_model.py:403|  - 模型文件: models/trained\CC_DAT_AI_TRAIN_OUT_919_202508.pkl
2025-09-16 16:50:27:614000|model_training|20516|21660|INFO|train_model.py:422|  - 已更新元数据: 写入 feature_names(22)
2025-09-16 16:50:27:614000|model_training|20516|21660|INFO|train_model.py:431|  - 测试RMSE: 62.8391
2025-09-16 16:50:27:615000|model_training|20516|21660|INFO|train_model.py:433|  - R²得分: 0.9977
2025-09-16 16:50:27:615000|model_training|20516|21660|INFO|train_model.py:438|  - CV RMSE均值: 44.8894
2025-09-16 16:50:27:616001|model_training|20516|21660|INFO|train_model.py:444|==================================================
2025-09-16 16:50:27:616001|model_training|20516|21660|INFO|train_model.py:445|训练完成汇总:
2025-09-16 16:50:27:616998|model_training|20516|21660|INFO|train_model.py:446|成功训练模型: 1
2025-09-16 16:50:27:616998|model_training|20516|21660|INFO|train_model.py:447|训练失败模型: 0
2025-09-16 16:50:27:618001|model_training|20516|21660|INFO|train_model.py:448|总模型数: 1
2025-09-16 16:50:27:618001|model_training|20516|21660|INFO|train_model.py:451|模型保存目录: models/trained
2025-09-16 16:50:27:618001|model_training|20516|21660|INFO|train_model.py:452|元数据保存目录: models/metadata
2025-09-16 16:50:27:618999|model_training|20516|21660|INFO|train_model.py:455|步骤 7: 特征重要性评估
2025-09-16 16:50:27:640999|model_training|20516|21660|INFO|train_model.py:143|--------------------------------------------------
2025-09-16 16:50:27:640999|model_training|20516|21660|INFO|train_model.py:144|本地网 919 特征重要性排行榜（共22列）:
2025-09-16 16:50:27:642001|model_training|20516|21660|INFO|train_model.py:150|   1. historical_lag_stability                 重要性: 333.000000 (19.33%)
2025-09-16 16:50:27:642001|model_training|20516|21660|INFO|train_model.py:150|   2. h_change_1m                              重要性: 323.000000 (18.75%)
2025-09-16 16:50:27:644000|model_training|20516|21660|INFO|train_model.py:150|   3. h_percentage                             重要性: 251.000000 (14.57%)
2025-09-16 16:50:27:644000|model_training|20516|21660|INFO|train_model.py:150|   4. total_volume                             重要性: 134.000000 (7.78%)
2025-09-16 16:50:27:646000|model_training|20516|21660|INFO|train_model.py:150|   5. trigger_usage                            重要性: 115.000000 (6.67%)
2025-09-16 16:50:27:646000|model_training|20516|21660|INFO|train_model.py:150|   6. h_change_2m                              重要性: 105.000000 (6.09%)
2025-09-16 16:50:27:647001|model_training|20516|21660|INFO|train_model.py:150|   7. prev_1m_lag_amount                       重要性: 88.000000 (5.11%)
2025-09-16 16:50:27:647001|model_training|20516|21660|INFO|train_model.py:150|   8. prev_1m_total_volume_x_total_volume      重要性: 58.000000 (3.37%)
2025-09-16 16:50:27:647999|model_training|20516|21660|INFO|train_model.py:150|   9. prev_2m_total_volume                     重要性: 58.000000 (3.37%)
2025-09-16 16:50:27:647999|model_training|20516|21660|INFO|train_model.py:150|  10. prev_2m_lag_amount                       重要性: 58.000000 (3.37%)
2025-09-16 16:50:27:649000|model_training|20516|21660|INFO|train_model.py:150|  11. prev_1m_total_volume                     重要性: 46.000000 (2.67%)
2025-09-16 16:50:27:649000|model_training|20516|21660|INFO|train_model.py:150|  12. prev_1m_trigger_usage_x_trigger_usage    重要性: 42.000000 (2.44%)
2025-09-16 16:50:27:650000|model_training|20516|21660|INFO|train_model.py:150|  13. prev_2m_trigger_usage                    重要性: 36.000000 (2.09%)
2025-09-16 16:50:27:650000|model_training|20516|21660|INFO|train_model.py:150|  14. trigger_usage_x_gear_label               重要性: 30.000000 (1.74%)
2025-09-16 16:50:27:651001|model_training|20516|21660|INFO|train_model.py:150|  15. prev_1m_trigger_usage                    重要性: 21.000000 (1.22%)
2025-09-16 16:50:27:651001|model_training|20516|21660|INFO|train_model.py:150|  16. month                                    重要性: 18.000000 (1.04%)
2025-09-16 16:50:27:652000|model_training|20516|21660|INFO|train_model.py:150|  17. h_trend                                  重要性: 7.000000 (0.41%)
2025-09-16 16:50:27:652000|model_training|20516|21660|INFO|train_model.py:150|  18. business_type_1                          重要性: 0.000000 (0.00%)
2025-09-16 16:50:27:653000|model_training|20516|21660|INFO|train_model.py:150|  19. gear_score                               重要性: 0.000000 (0.00%)
2025-09-16 16:50:27:653000|model_training|20516|21660|INFO|train_model.py:150|  20. gear_label                               重要性: 0.000000 (0.00%)
2025-09-16 16:50:27:653999|model_training|20516|21660|INFO|train_model.py:150|  21. volume_type                              重要性: 0.000000 (0.00%)
2025-09-16 16:50:27:655000|model_training|20516|21660|INFO|train_model.py:150|  22. year                                     重要性: 0.000000 (0.00%)
2025-09-16 16:50:27:655000|model_training|20516|21660|INFO|train_model.py:153|--------------------------------------------------
2025-09-16 16:50:27:656001|model_training|20516|21660|INFO|train_model.py:461|==================================================
