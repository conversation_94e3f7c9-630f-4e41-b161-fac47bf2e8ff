# 滞后量预测模型依赖包

# 核心科学计算库
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# 机器学习框架
scikit-learn>=1.0.0
xgboost>=1.5.0
lightgbm>=3.3.0
scikit-optimize>=0.9.0  # 贝叶斯优化库，支持真实进度显示

# 数据处理和可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# 配置和日志管理
pyyaml>=6.0
python-dotenv>=0.19.0

# 文件处理和并行计算
joblib>=1.1.0
tqdm>=4.62.0

# 日期时间处理
python-dateutil>=2.8.0

# 数据验证
jsonschema>=4.0.0

# 开发和测试工具
pytest>=6.0.0
pytest-cov>=3.0.0
black>=22.0.0
flake8>=4.0.0

# Jupyter支持
jupyter>=1.0.0
ipykernel>=6.0.0
jupyterlab>=3.0.0

# 性能分析
memory-profiler>=0.60.0
psutil>=5.8.0

# 可选：深度学习支持（如果需要）
# tensorflow>=2.8.0
# torch>=1.10.0