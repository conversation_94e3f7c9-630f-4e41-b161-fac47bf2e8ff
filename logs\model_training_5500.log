2025-09-16 16:05:26:992371|model_training|5500|3900|INFO|train_model.py:101|==================================================
2025-09-16 16:05:26:992371|model_training|5500|3900|INFO|train_model.py:102|开始模型训练流程 (单本地网强制模式)
2025-09-16 16:05:26:993370|model_training|5500|3900|INFO|train_model.py:103|配置文件: config/config.yaml
2025-09-16 16:05:26:993370|model_training|5500|3900|INFO|train_model.py:104|数据目录: data/raw
2025-09-16 16:05:26:993370|model_training|5500|3900|INFO|train_model.py:105|输出目录: models/trained
2025-09-16 16:05:26:993370|model_training|5500|3900|INFO|train_model.py:106|==================================================
2025-09-16 16:05:26:994372|model_training|5500|3900|INFO|train_model.py:109|步骤 1: 数据加载
2025-09-16 16:05:26:994372|model_training|5500|3900|INFO|train_model.py:125|预测目标月: 202509; 训练月(当前样本月): 202508; 加载历史窗口: ['202506', '202507', '202508']
2025-09-16 16:05:26:994372|model_training|5500|3900|INFO|train_model.py:130|数据加载子步骤: 按需选择历史月份文件并加载
2025-09-16 16:05:29:477854|model_training|5500|3900|INFO|train_model.py:153|按需加载完成: 行数 519327; 覆盖月份 ['202506', '202507', '202508'] (不含预测月 202509)
2025-09-16 16:05:29:478853|model_training|5500|3900|INFO|train_model.py:156|步骤 2: 数据清洗
2025-09-16 16:05:35:142869|model_training|5500|3900|INFO|train_model.py:167|数据集 919: 清洗前 519327 行，清洗后 48042 行
2025-09-16 16:05:35:142869|model_training|5500|3900|INFO|train_model.py:172|数据集 919: 记录过滤行 471151 行 (含原因)
2025-09-16 16:05:35:142869|model_training|5500|3900|INFO|train_model.py:179|步骤 3: 数据验证
2025-09-16 16:06:27:283921|model_training|5500|3900|INFO|train_model.py:236|过滤数据文件已生成: data/processed/filtered\CC_DAT_AI_TRAIN_FILTER_919_202508_01.txt (行数: 471151)
2025-09-16 16:06:27:284884|model_training|5500|3900|INFO|train_model.py:241|步骤 4: 特征工程
2025-09-16 16:08:19:307180|model_training|5500|3900|INFO|train_model.py:252|数据集 919: 提取了 16 个特征
2025-09-16 16:08:19:458885|model_training|5500|3900|INFO|train_model.py:265|数据集 919: 特征转换完成 (列数: 25) — 已禁用转换器持久化
2025-09-16 16:08:19:459555|model_training|5500|3900|INFO|train_model.py:271|数据集 919: 准备了 25 个特征用于训练
2025-09-16 16:08:19:459555|model_training|5500|3900|INFO|train_model.py:286|调用 export_business_features 生成业务标准特征文件
2025-09-16 16:08:20:253710|model_training|5500|3900|INFO|train_model.py:292|本地网 919 的业务标准特征数据已保存 -> 目录: data/processed
2025-09-16 16:08:20:253710|model_training|5500|3900|INFO|train_model.py:297|步骤 5: 模型训练
2025-09-16 16:11:58:984829|model_training|5500|3900|INFO|train_model.py:322|步骤 6: 训练结果汇总
2025-09-16 16:11:58:984829|model_training|5500|3900|INFO|train_model.py:330|本地网 919: 训练成功
2025-09-16 16:11:58:984829|model_training|5500|3900|INFO|train_model.py:331|  - 模型文件: models/trained\CC_DAT_AI_TRAIN_OUT_919_202508.pkl
2025-09-16 16:11:59:001481|model_training|5500|3900|INFO|train_model.py:350|  - 已更新元数据: 写入 feature_names(22)
2025-09-16 16:11:59:003059|model_training|5500|3900|INFO|train_model.py:359|  - 测试RMSE: 62.8391
2025-09-16 16:11:59:004166|model_training|5500|3900|INFO|train_model.py:361|  - R²得分: 0.9977
2025-09-16 16:11:59:007153|model_training|5500|3900|INFO|train_model.py:366|  - CV RMSE均值: 44.8894
2025-09-16 16:11:59:007742|model_training|5500|3900|INFO|train_model.py:372|==================================================
2025-09-16 16:11:59:008383|model_training|5500|3900|INFO|train_model.py:373|训练完成汇总:
2025-09-16 16:11:59:009897|model_training|5500|3900|INFO|train_model.py:374|成功训练模型: 1
2025-09-16 16:11:59:009897|model_training|5500|3900|INFO|train_model.py:375|训练失败模型: 0
2025-09-16 16:11:59:010756|model_training|5500|3900|INFO|train_model.py:376|总模型数: 1
2025-09-16 16:11:59:011301|model_training|5500|3900|INFO|train_model.py:379|模型保存目录: models/trained
2025-09-16 16:11:59:012272|model_training|5500|3900|INFO|train_model.py:380|元数据保存目录: models/metadata
2025-09-16 16:11:59:013270|model_training|5500|3900|INFO|train_model.py:382|==================================================
