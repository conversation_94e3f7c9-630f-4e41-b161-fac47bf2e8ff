"""
基础组件类

提供所有业务组件的通用功能，包括配置管理、日志设置、错误处理等
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from .logger import get_logging_level


class BaseComponent:
    """
    基础组件类
    
    所有业务组件的基类，提供统一的配置管理、日志设置和错误处理功能
    """
    
    def __init__(self, config_path: Optional[str] = None, component_name: Optional[str] = None):
        """
        初始化基础组件
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
            component_name: 组件名称，用于日志标识
        """
        self.component_name = component_name or self.__class__.__name__
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 初始化完成后的钩子方法
        self._post_init()
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        if config_path is None:
            # 计算默认配置文件路径
            config_path = self._get_default_config_path()
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 存储配置文件路径供其他组件使用
            config['_config_path'] = config_path
            
            return config
            
        except Exception as e:
            # 使用print而不是logger，因为logger还未初始化
            print(f"警告: 无法加载配置文件 {config_path}: {e}")
            return self._get_default_config()
    
    def _get_default_config_path(self) -> str:
        """
        获取默认配置文件路径
        
        Returns:
            默认配置文件路径
        """
        # 从当前文件位置计算相对路径
        current_file = Path(__file__)
        project_root = current_file.parent.parent.parent
        return str(project_root / 'config' / 'config.yaml')
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        子类可以重写此方法提供特定的默认配置
        
        Returns:
            默认配置字典
        """
        return {
            'logging': {
                'level': 4,  # INFO级别
                'log_dir': 'logs'
            }
        }
    
    def _setup_logger(self) -> logging.Logger:
        """
        设置日志器
        
        Returns:
            配置好的日志器
        """
        logger = logging.getLogger(f"{self.__module__}.{self.component_name}")
        
        # 设置日志级别
        config_level = self.config.get('logging', {}).get('level', 4)  # 默认INFO级别
        logger.setLevel(get_logging_level(config_level))
        
        # 确保日志器使用根日志器的处理器
        logger.propagate = True
        
        return logger
    
    def _post_init(self):
        """
        初始化完成后的钩子方法
        
        子类可以重写此方法进行额外的初始化工作
        """
        pass
    
    def ensure_directory(self, directory_path: str) -> str:
        """
        确保目录存在
        
        Args:
            directory_path: 目录路径
            
        Returns:
            目录路径
        """
        try:
            os.makedirs(directory_path, exist_ok=True)
            return directory_path
        except Exception as e:
            self.logger.error(f"创建目录失败 {directory_path}: {e}")
            raise
    
    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值，支持点分隔的键路径
        
        Args:
            key_path: 配置键路径，如 'model.algorithm'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def validate_config(self, required_sections: list = None) -> bool:
        """
        验证配置文件
        
        Args:
            required_sections: 必需的配置节列表
            
        Returns:
            是否验证通过
        """
        if required_sections is None:
            required_sections = []
        
        for section in required_sections:
            if section not in self.config:
                self.logger.error(f"配置文件缺少必需的部分: {section}")
                return False
        
        return True
    
    def handle_error(self, error: Exception, context: str = "", raise_error: bool = True):
        """
        统一的错误处理
        
        Args:
            error: 异常对象
            context: 错误上下文描述
            raise_error: 是否重新抛出异常
        """
        error_msg = f"{context}: {str(error)}" if context else str(error)
        self.logger.error(error_msg, exc_info=True)
        
        if raise_error:
            raise error
