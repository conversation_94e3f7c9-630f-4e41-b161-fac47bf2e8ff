"""
配置管理器

提供统一的配置管理功能，支持单例模式、配置验证和热重载
"""

import os
import yaml
import threading
from typing import Dict, Any, Optional, List
from pathlib import Path


class ConfigManager:
    """
    配置管理器（单例模式）
    
    提供全局统一的配置管理功能
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, config_path: Optional[str] = None):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ConfigManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        if self._initialized:
            return
        
        self.config_path = config_path or self._get_default_config_path()
        self.config = {}
        self._load_config()
        self._initialized = True
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        current_file = Path(__file__)
        project_root = current_file.parent.parent.parent
        return str(project_root / 'config' / 'config.yaml')
    
    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f) or {}
            
            # 存储配置文件路径
            self.config['_config_path'] = self.config_path
            
        except Exception as e:
            print(f"警告: 无法加载配置文件 {self.config_path}: {e}")
            self.config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'data': {
                'raw_data_dir': 'data/raw',
                'processed_data_dir': 'data/processed',
                'predictions_dir': 'data/predictions',
                'train_file_pattern': 'CC_DAT_AI_TRAIN_*_*.txt',
                'train_test_split': 0.8
            },
            'model': {
                'trained_models_dir': 'models/trained',
                'metadata_dir': 'models/metadata',
                'algorithm': 'lightgbm',
                'hyperparameter_tuning': True
            },
            'feature_engineering': {
                'time_window_months': 3,
                'gear_labels': [0.2, 0.4, 0.6, 0.8, 1.0]
            },
            'business_rules': {
                'min_lag_percentage': 0.5,
                'max_lag_percentage': 3.0,
                'filter_gear_labels': [0, 1],
                'risk_threshold': 0.5
            },
            'processing': {
                'max_workers': 4,
                'chunk_size': 10000
            },
            'logging': {
                'level': 4,
                'log_dir': 'logs'
            }
        }
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值，支持点分隔的键路径
        
        Args:
            key_path: 配置键路径，如 'model.algorithm'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 配置值
        """
        keys = key_path.split('.')
        config = self.config
        
        # 导航到最后一级的父节点
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置最终值
        config[keys[-1]] = value
    
    def get_all(self) -> Dict[str, Any]:
        """
        获取所有配置
        
        Returns:
            完整的配置字典
        """
        return self.config.copy()
    
    def validate(self, required_sections: List[str] = None) -> bool:
        """
        验证配置
        
        Args:
            required_sections: 必需的配置节列表
            
        Returns:
            是否验证通过
        """
        if required_sections is None:
            required_sections = ['data', 'model', 'feature_engineering', 'business_rules']
        
        for section in required_sections:
            if section not in self.config:
                print(f"配置验证失败: 缺少必需的部分 {section}")
                return False
        
        return True
    
    def reload(self):
        """重新加载配置文件"""
        self._load_config()
    
    def save(self, config_path: Optional[str] = None):
        """
        保存配置到文件
        
        Args:
            config_path: 保存路径，如果为None则使用当前配置文件路径
        """
        save_path = config_path or self.config_path
        
        try:
            # 移除内部使用的键
            config_to_save = {k: v for k, v in self.config.items() if not k.startswith('_')}
            
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_to_save, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
                         
        except Exception as e:
            print(f"保存配置文件失败 {save_path}: {e}")
            raise
    
    def update(self, updates: Dict[str, Any]):
        """
        批量更新配置
        
        Args:
            updates: 更新的配置字典
        """
        def deep_update(base_dict, update_dict):
            """深度更新字典"""
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self.config, updates)


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config() -> ConfigManager:
    """
    获取全局配置管理器实例
    
    Returns:
        配置管理器实例
    """
    return config_manager
