"""
数据加载器

负责加载原始训练数据文件，支持多线程并行处理
"""

import os
import glob
import pandas as pd
import numpy as np
from pathlib import Path
from typing import List, Dict, Union, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入基础组件
from ..utils.base_component import BaseComponent

class DataLoader(BaseComponent):
    """数据加载器类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化数据加载器

        Args:
            config_path: 配置文件路径
        """
        super().__init__(config_path, "DataLoader")

        # 数据字段定义
        self.data_columns = [
            'order_id',           # 工单ID
            'remind_time',        # 提醒时间
            'product_instance_id',# 产品实例ID
            'local_network',      # 本地网
            'business_type',      # 业务类型(0语音/1数据)
            'volume_type',        # 量本类型(通用量本为1)
            'gear_label',         # 档位标签
            'total_volume',       # 总量
            'trigger_usage',      # 触发时使用量
            'lag_amount'          # 滞后量
        ]

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        base_config = super()._get_default_config()
        base_config.update({
            'data': {
                'raw_data_dir': 'data/raw',
                'train_file_pattern': 'CC_DAT_AI_TRAIN_*_*.txt',
                'max_file_rows': 500000
            },
            'processing': {
                'max_workers': 4,
                'chunk_size': 10000
            }
        })
        return base_config
    
    def find_data_files(self, data_dir: str = None, pattern: str = None) -> List[str]:
        """
        查找数据文件
        
        Args:
            data_dir: 数据目录
            pattern: 文件名模式
            
        Returns:
            文件路径列表
        """
        if data_dir is None:
            data_dir = self.config['data']['raw_data_dir']
        if pattern is None:
            pattern = self.config['data']['train_file_pattern']
            
        search_pattern = os.path.join(data_dir, pattern)
        files = glob.glob(search_pattern)
        
        self.logger.info(f"在目录 {data_dir} 中找到 {len(files)} 个数据文件")
        return sorted(files)
    
    def parse_filename(self, filepath: str) -> Dict[str, str]:
        """
        解析文件名信息
        
        Args:
            filepath: 文件路径
            
        Returns:
            包含本地网、时间、序号的字典
        """
        filename = Path(filepath).stem
        # 格式: CC_DAT_AI_TRAIN_本地网_时间(YYYYMM)_序号
        parts = filename.split('_')
        
        if len(parts) >= 6:
            return {
                'local_network': parts[4],
                'time': parts[5],
                'sequence': parts[6] if len(parts) > 6 else '1'
            }
        else:
            self.logger.warning(f"文件名格式不符合预期: {filename}")
            return {'local_network': 'unknown', 'time': 'unknown', 'sequence': '1'}
    
    def load_single_file(self, filepath: str) -> pd.DataFrame:
        """
        加载单个数据文件
        
        Args:
            filepath: 文件路径
            
        Returns:
            数据DataFrame
        """
        try:
            # 使用|作为分隔符；先以字符串载入，随后对数值列做安全降维转换，降低内存占用
            df = pd.read_csv(
                filepath,
                sep='|',
                names=self.data_columns,
                encoding='utf-8',
                dtype=str,
                na_values=['', 'NULL', 'null'],
                low_memory=False
            )

            # 转换数据类型（避免大规模 object 占用内存）
            numeric_cols = ['total_volume','trigger_usage','lag_amount','business_type','volume_type','gear_label']
            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            if 'local_network' in df.columns:
                try:
                    df['local_network'] = df['local_network'].astype('category')
                except Exception:
                    pass
            if 'product_instance_id' in df.columns:
                # 保证主键/ID 始终为字符串
                df['product_instance_id'] = df['product_instance_id'].astype(str)

            # 添加文件信息
            file_info = self.parse_filename(filepath)
            df['source_file'] = Path(filepath).name
            df['file_local_network'] = file_info['local_network']
            df['file_time'] = file_info['time']

            self.logger.info(f"成功加载文件 {Path(filepath).name}, 共 {len(df)} 行数据")
            return df

        except Exception as e:
            self.logger.error(f"加载文件 {filepath} 失败: {e}")
            return pd.DataFrame()
    
    def load_multiple_files(self, filepaths: List[str], max_workers: int = None) -> pd.DataFrame:
        """
        并行加载多个数据文件
        
        Args:
            filepaths: 文件路径列表
            max_workers: 最大工作线程数
            
        Returns:
            合并后的数据DataFrame
        """
        if max_workers is None:
            max_workers = self.config['processing']['max_workers']
        
        all_data = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {
                executor.submit(self.load_single_file, filepath): filepath 
                for filepath in filepaths
            }
            
            # 收集结果
            for future in as_completed(future_to_file):
                filepath = future_to_file[future]
                try:
                    df = future.result()
                    if not df.empty:
                        all_data.append(df)
                except Exception as e:
                    self.logger.error(f"处理文件 {filepath} 时出错: {e}")
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            self.logger.info(f"成功加载并合并 {len(all_data)} 个文件, 总计 {len(combined_df)} 行数据")
            return combined_df
        else:
            self.logger.warning("未成功加载任何数据文件")
            return pd.DataFrame()
    
    def load_data_by_local_network(self, local_networks: List[str] = None, 
                                  data_dir: str = None) -> Dict[str, pd.DataFrame]:
        """
        按本地网分组加载数据
        
        Args:
            local_networks: 本地网列表，如果为None则加载所有
            data_dir: 数据目录
            
        Returns:
            本地网为键，DataFrame为值的字典
        """
        files = self.find_data_files(data_dir)
        grouped_files = {}
        
        # 按本地网分组文件
        for filepath in files:
            file_info = self.parse_filename(filepath)
            local_net = file_info['local_network']
            
            if local_networks is None or local_net in local_networks:
                if local_net not in grouped_files:
                    grouped_files[local_net] = []
                grouped_files[local_net].append(filepath)
        
        # 为每个本地网加载数据
        result = {}
        for local_net, filepaths in grouped_files.items():
            self.logger.info(f"开始加载本地网 {local_net} 的数据，共 {len(filepaths)} 个文件")
            result[local_net] = self.load_multiple_files(filepaths)
        
        return result

    # ================= 新增: 文件元数据与按需选择加载 =================
    def list_files_metadata(self, data_dir: str = None, pattern: str = None) -> List[Dict[str, str]]:
        """列出数据目录下所有符合模式的文件及解析元数据

        Returns:
            [{'filepath': str, 'local_network': str, 'time': 'YYYYMM', 'sequence': str}, ...]
        """
        files = self.find_data_files(data_dir=data_dir, pattern=pattern)
        metadata = []
        for fp in files:
            info = self.parse_filename(fp)
            metadata.append({
                'filepath': fp,
                'local_network': info.get('local_network', 'unknown'),
                'time': info.get('time', 'unknown'),
                'sequence': info.get('sequence', '1')
            })
        return metadata

    def select_files_for_months(self, local_network: str, months: set, data_dir: str = None) -> Tuple[List[str], List[str]]:
        """为指定本地网与月份集合选择需要加载的文件

        Args:
            local_network: 目标本地网
            months: 需要的月份集合 (set[str]) 例如 {'202507','202508'}
            data_dir: 数据目录

        Returns:
            (selected_files, missing_months)
        """
        meta = self.list_files_metadata(data_dir=data_dir)
        # 当前本地网所有文件
        ln_files = [m for m in meta if m['local_network'] == local_network]
        month_to_files = {}
        for m in ln_files:
            month_to_files.setdefault(m['time'], []).append(m['filepath'])
        selected = []
        for month in sorted(months):
            fps = month_to_files.get(month, [])
            if fps:
                # 同一账期可能有多个序号文件，全部加载
                selected.extend(fps)
        existing_months = set(month_to_files.keys())
        missing = sorted(list(months - existing_months))
        self.logger.info(
            f"本地网 {local_network} 可用月份: {sorted(existing_months)}; 需要月份: {sorted(months)}; 缺失: {missing if missing else '无'}"
        )
        if selected:
            self.logger.info(f"选择加载 {len(selected)} 个文件: {[Path(f).name for f in selected]}")
        else:
            self.logger.warning(f"本地网 {local_network} 未找到任何匹配所需月份 {sorted(months)} 的文件")
        return selected, missing

    def load_specific_files_grouped(self, local_network: str, filepaths: List[str]) -> Dict[str, pd.DataFrame]:
        """加载指定文件列表并返回 {local_network: DataFrame} 结构，便于与现有接口兼容"""
        if not filepaths:
            return {}
        df = self.load_multiple_files(filepaths)
        return {local_network: df} if not df.empty else {}
    
    def load_recent_months_data(self, months: int = 3, data_dir: str = None) -> pd.DataFrame:
        """
        加载最近几个月的数据
        
        Args:
            months: 月份数
            data_dir: 数据目录
            
        Returns:
            数据DataFrame
        """
        files = self.find_data_files(data_dir)
        
        # 按时间排序文件
        files_with_time = []
        for filepath in files:
            file_info = self.parse_filename(filepath)
            try:
                # 将YYYYMM格式转换为可排序的格式
                time_str = file_info['time']
                if len(time_str) == 6:  # YYYYMM
                    files_with_time.append((filepath, time_str))
            except:
                continue
        
        # 按时间降序排序，取最近的文件
        files_with_time.sort(key=lambda x: x[1], reverse=True)
        
        # 获取最近months个月的唯一时间
        unique_times = list(set([t[1] for t in files_with_time]))[:months]
        
        # 筛选对应时间的文件
        recent_files = [f[0] for f in files_with_time if f[1] in unique_times]
        
        self.logger.info(f"加载最近 {months} 个月的数据，共 {len(recent_files)} 个文件")
        return self.load_multiple_files(recent_files)
    
    def get_data_summary(self, df: pd.DataFrame) -> Dict:
        """
        获取数据摘要信息
        
        Args:
            df: 数据DataFrame
            
        Returns:
            数据摘要字典
        """
        if df.empty:
            return {'总行数': 0, '错误': '数据为空'}
        
        summary = {
            '总行数': len(df),
            '列数': len(df.columns),
            '本地网数量': df['local_network'].nunique() if 'local_network' in df.columns else 0,
            '业务类型分布': df['business_type'].value_counts().to_dict() if 'business_type' in df.columns else {},
            '缺失值统计': df.isnull().sum().to_dict(),
            '内存使用': f"{df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB"
        }
        
        return summary