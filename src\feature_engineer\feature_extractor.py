"""
特征提取器

负责从原始数据中提取和构建特征，特别是时序特征
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
import yaml
import os
from datetime import datetime, timedelta

class FeatureExtractor:
    """特征提取器类

    重构说明:
    - 训练阶段: 需要窗口内“每个月”一行 (当前月 + 前1/2月历史)，当前月 lag_amount 作为标签
    - 预测阶段: 未来目标月 (尚未发生) 仅生成前1/2月历史特征行，不含当前月真实值
    - 原 snapshot 行为被拆分: create_future_prediction_sample
    """
    
    def __init__(self, config_path: str = None):
        """
        初始化特征提取器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 时序特征配置
        self.time_window_months = self.config.get('feature_engineering', {}).get('time_window_months', 3)
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/config.yaml')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"警告: 无法加载配置文件: {e}")
            return {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        return logger
    
    def extract_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取时间特征
        
        Args:
            df: 数据DataFrame
            
        Returns:
            添加时间特征的DataFrame
        """
        df_with_time = df.copy()
        
        if 'remind_time' in df_with_time.columns:
            # 确保remind_time是datetime类型
            df_with_time['remind_time'] = pd.to_datetime(df_with_time['remind_time'], errors='coerce')
            
            # 提取基础时间特征
            df_with_time['year'] = df_with_time['remind_time'].dt.year
            df_with_time['month'] = df_with_time['remind_time'].dt.month
            df_with_time['day'] = df_with_time['remind_time'].dt.day
            df_with_time['hour'] = df_with_time['remind_time'].dt.hour
            df_with_time['weekday'] = df_with_time['remind_time'].dt.weekday
            df_with_time['is_weekend'] = df_with_time['weekday'].isin([5, 6]).astype(int)
            
            # 提取月份字符串(YYYYMM格式)
            df_with_time['year_month'] = df_with_time['remind_time'].dt.strftime('%Y%m')
            
            self.logger.info("时间特征提取完成")
        
        return df_with_time
    
    def extract_user_aggregated_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取用户聚合特征
        
        Args:
            df: 数据DataFrame
            
        Returns:
            用户聚合特征DataFrame
        """
        self.logger.info("开始提取用户聚合特征")
        
        # 确保有必要的列
        required_cols = ['product_instance_id', 'local_network', 'business_type', 
                        'volume_type', 'gear_label', 'year_month']
        
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            self.logger.error(f"缺少必需列: {missing_cols}")
            return pd.DataFrame()
        
        # 新修正：按档位维度分别保留该月最大时间戳记录。
        # 分组键恢复: (user, local_network, business_type, volume_type, gear_label, year_month)
        sort_df = df.sort_values('remind_time', ascending=False).copy()
        grp_cols = ['product_instance_id','local_network','business_type','volume_type','gear_label','year_month']
        monthly_top_per_gear = sort_df.groupby(grp_cols, as_index=False).first()
        self.logger.info(
            f"月度按档位最大时间戳聚合: 原始 {len(df)} 行 -> 聚合后 {len(monthly_top_per_gear)} 行 (含档位维度)"
        )
        return monthly_top_per_gear
    
    def create_time_series_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建时序特征
        
        Args:
            df: 用户聚合后的数据DataFrame
            
        Returns:
            包含时序特征的DataFrame：
            用户|年|月|本地网|业务类型|量本类型|档位标签|总量|触发时使用量|滞后量|前一个月总量|前一个月触发时使用量|前一个月滞后量|前二个月总量|前二个月触发时使用量|前二个月滞后量
        """
        self.logger.info("开始创建时序特征")
        # 分组键包含 gear_label：保持每个档位独立历史链
        base_group_cols = ['product_instance_id', 'local_network', 'business_type', 'volume_type', 'gear_label']

        records = []

        def ym_to_int(ym: str) -> int:
            return int(ym[:4]) * 12 + int(ym[4:6])

        for _, group_df in df.groupby(base_group_cols):
            g_sorted = group_df.sort_values('year_month')
            month_map = {row.year_month: row for row in g_sorted.itertuples()}
            month_list = sorted(month_map.keys(), key=lambda x: ym_to_int(x))

            for ym in month_list:
                row = month_map[ym]
                feature_record = {
                    'user': row.product_instance_id,
                    'year': row.year,
                    'month': row.month,
                    'year_month': row.year_month,
                    'local_network': row.local_network,
                    'business_type': row.business_type,
                    'volume_type': row.volume_type,
                    'gear_label': row.gear_label,
                    'total_volume': row.total_volume,
                    'trigger_usage': row.trigger_usage,
                    'lag_amount': row.lag_amount
                }
                y = int(ym[:4]); m = int(ym[4:6])
                def shift(y, m, delta):
                    total = y * 12 + m - 1 + delta
                    ny = total // 12
                    nm = total % 12 + 1
                    return f"{ny}{nm:02d}"
                prev1 = shift(y, m, -1)
                prev2 = shift(y, m, -2)
                for i, prev in zip([1,2],[prev1, prev2]):
                    prev_row = month_map.get(prev)
                    feature_record[f'prev_{i}m_total_volume'] = getattr(prev_row, 'total_volume', 0) if prev_row else 0
                    feature_record[f'prev_{i}m_trigger_usage'] = getattr(prev_row, 'trigger_usage', 0) if prev_row else 0
                    feature_record[f'prev_{i}m_lag_amount'] = getattr(prev_row, 'lag_amount', 0) if prev_row else 0
                if self.time_window_months >= 3:
                    prev3 = shift(y, m, -3)
                    prev_row3 = month_map.get(prev3)
                    feature_record['prev_3m_total_volume'] = getattr(prev_row3, 'total_volume', 0) if prev_row3 else 0
                    feature_record['prev_3m_trigger_usage'] = getattr(prev_row3, 'trigger_usage', 0) if prev_row3 else 0
                    feature_record['prev_3m_lag_amount'] = getattr(prev_row3, 'lag_amount', 0) if prev_row3 else 0
                records.append(feature_record)

        if not records:
            self.logger.warning("create_time_series_features: 未生成记录")
            return pd.DataFrame()
        ts_df = pd.DataFrame(records)
            
        self.logger.info(f"时序特征创建完成，共 {len(ts_df)} 条 (缺失历史统一填 0)")
        return ts_df
    
    def _get_lag_month_data(self, group_data: pd.DataFrame, current_index: int, 
                           lag_months: int) -> Dict:
        """
        获取滞后月份的数据
        
        Args:
            group_data: 分组数据
            current_index: 当前记录索引
            lag_months: 滞后月份数
            
        Returns:
            滞后月份的数据字典
        """
        # 计算目标索引
        target_index = current_index - lag_months
        
        if target_index >= 0:
            lag_row = group_data.iloc[target_index]
            return {
                'total_volume': lag_row['total_volume'],
                'trigger_usage': lag_row['trigger_usage'],
                'lag_amount': lag_row['lag_amount']
            }
        else:
            return {
                'total_volume': np.nan,
                'trigger_usage': np.nan,
                'lag_amount': np.nan
            }
    
    def extract_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取统计特征 - 适配新的字段名格式
        
        Args:
            df: 时序特征数据DataFrame
            
        Returns:
            添加统计特征的DataFrame
        """
        df_with_stats = df.copy()
        
        # 使用率特征
        if all(col in df_with_stats.columns for col in ['total_volume', 'trigger_usage']):
            df_with_stats['usage_rate'] = (
                df_with_stats['trigger_usage'] / df_with_stats['total_volume']
            ).fillna(0)
        
        # 滞后量率特征
        if all(col in df_with_stats.columns for col in ['total_volume', 'lag_amount']):
            df_with_stats['lag_rate'] = (
                df_with_stats['lag_amount'] / df_with_stats['total_volume']
            ).fillna(0)
        
        # 历史变化特征 - 动态支持到配置的窗口（月）
        for lag_months in range(1, self.time_window_months + 1):
            prefix = f'prev_{lag_months}m'
            
            # 总量变化
            if all(col in df_with_stats.columns for col in ['total_volume', f'{prefix}_total_volume']):
                df_with_stats[f'{prefix}_volume_change'] = (
                    df_with_stats['total_volume'] - df_with_stats[f'{prefix}_total_volume']
                )
                df_with_stats[f'{prefix}_volume_change_rate'] = (
                    df_with_stats[f'{prefix}_volume_change'] / df_with_stats[f'{prefix}_total_volume']
                ).fillna(0)
            
            # 使用量变化
            if all(col in df_with_stats.columns for col in ['trigger_usage', f'{prefix}_trigger_usage']):
                df_with_stats[f'{prefix}_usage_change'] = (
                    df_with_stats['trigger_usage'] - df_with_stats[f'{prefix}_trigger_usage']
                )
                df_with_stats[f'{prefix}_usage_change_rate'] = (
                    df_with_stats[f'{prefix}_usage_change'] / df_with_stats[f'{prefix}_trigger_usage']
                ).fillna(0)
            
            # 滞后量变化
            if all(col in df_with_stats.columns for col in ['lag_amount', f'{prefix}_lag_amount']):
                df_with_stats[f'{prefix}_lag_change'] = (
                    df_with_stats['lag_amount'] - df_with_stats[f'{prefix}_lag_amount']
                )
        
        self.logger.info("统计特征提取完成")
        return df_with_stats
    
    def extract_categorical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取分类特征
        
        Args:
            df: 数据DataFrame
            
        Returns:
            添加分类特征的DataFrame
        """
        df_with_categorical = df.copy()
        
        # 档位标签分类
        if 'gear_label' in df_with_categorical.columns:
            df_with_categorical['gear_category'] = df_with_categorical['gear_label'].map({
                0.2: 'low',
                0.4: 'medium_low',
                0.6: 'medium',
                0.8: 'medium_high',
                1.0: 'high'
            }).fillna('unknown')
        
        # 业务类型分类
        if 'business_type' in df_with_categorical.columns:
            df_with_categorical['business_category'] = df_with_categorical['business_type'].map({
                0: 'voice',
                1: 'data'
            }).fillna('unknown')
        
        # 月份季节分类
        if 'month' in df_with_categorical.columns:
            def get_season(month):
                if month in [12, 1, 2]:
                    return 'winter'
                elif month in [3, 4, 5]:
                    return 'spring'
                elif month in [6, 7, 8]:
                    return 'summer'
                else:
                    return 'autumn'
            
            df_with_categorical['season'] = df_with_categorical['month'].apply(get_season)
        
        self.logger.info("分类特征提取完成")
        return df_with_categorical
    
    # 训练数据集构造
    def create_training_dataset(self, df: pd.DataFrame) -> pd.DataFrame:
        """基于聚合后的月度代表记录，生成训练样本集 (逐月展开)。

        每一行: 当前月 (有真实 lag_amount) + 前1/2月历史特征（缺失填0）。
        """
        df_with_time = self.extract_time_features(df)
        aggregated_df = self.extract_user_aggregated_features(df_with_time)
        if aggregated_df.empty:
            return pd.DataFrame()
        ts = self.create_time_series_features(aggregated_df)
        if ts.empty:
            return pd.DataFrame()
        base_cols = [
            'user','year','month','local_network','business_type','volume_type','gear_label',
            'total_volume','trigger_usage','lag_amount',
            'prev_1m_total_volume','prev_1m_trigger_usage','prev_1m_lag_amount',
            'prev_2m_total_volume','prev_2m_trigger_usage','prev_2m_lag_amount'
        ]
        for c in base_cols:
            if c not in ts.columns:
                ts[c] = 0
        out = ts[base_cols].copy()
        history_cols = [c for c in out.columns if c.startswith('prev_')]
        out[history_cols] = out[history_cols].fillna(0)
        return out

    # 未来预测样本构造
    def create_future_prediction_sample(self, df: pd.DataFrame, prediction_month: str) -> pd.DataFrame:
        """构造未来月份预测样本 (prediction_month 未发生). 只输出一行/每用户档位组合：
        列: user, year, month, local_network, business_type, volume_type, gear_label,
            prev_1m_*, prev_2m_* 六列 (当前月真实值列 total/trigger/lag 置 0)。
        """
        if not prediction_month or len(prediction_month) != 6 or not prediction_month.isdigit():
            self.logger.error(f"prediction_month 格式非法: {prediction_month}")
            return pd.DataFrame()
        df_with_time = self.extract_time_features(df)
        aggregated_df = self.extract_user_aggregated_features(df_with_time)
        if aggregated_df.empty:
            return pd.DataFrame()
        # 构建索引映射
        ag_map = {}
        for r in aggregated_df.itertuples():
            ag_map[(r.product_instance_id, r.local_network, r.business_type, r.volume_type, r.gear_label, r.year_month)] = r
        def shift_month(ym: str, delta: int) -> str:
            y = int(ym[:4]); m = int(ym[4:6])
            total = y * 12 + m - 1 + delta
            ny = total // 12; nm = total % 12 + 1
            return f"{ny}{nm:02d}"
        prev1 = shift_month(prediction_month, -1)
        prev2 = shift_month(prediction_month, -2)
        p_year = int(prediction_month[:4]); p_month = int(prediction_month[4:6])
        records = []
        base_keys = ['product_instance_id','local_network','business_type','volume_type','gear_label']
        for gkey, _ in aggregated_df.groupby(base_keys):
            user, ln, biz, vol_type, gear = gkey
            row_prev1 = ag_map.get((user, ln, biz, vol_type, gear, prev1))
            row_prev2 = ag_map.get((user, ln, biz, vol_type, gear, prev2))
            rec = {
                'user': user,
                'year': p_year,
                'month': p_month,
                'local_network': ln,
                'business_type': biz,
                'volume_type': vol_type,
                'gear_label': gear,
                # 未来月当前列置 0 (无真实值)
                'total_volume': 0,
                'trigger_usage': 0,
                'lag_amount': 0,
                'prev_1m_total_volume': getattr(row_prev1,'total_volume',0) if row_prev1 else 0,
                'prev_1m_trigger_usage': getattr(row_prev1,'trigger_usage',0) if row_prev1 else 0,
                'prev_1m_lag_amount': getattr(row_prev1,'lag_amount',0) if row_prev1 else 0,
                'prev_2m_total_volume': getattr(row_prev2,'total_volume',0) if row_prev2 else 0,
                'prev_2m_trigger_usage': getattr(row_prev2,'trigger_usage',0) if row_prev2 else 0,
                'prev_2m_lag_amount': getattr(row_prev2,'lag_amount',0) if row_prev2 else 0,
            }
            records.append(rec)
        if not records:
            self.logger.warning("未来预测样本为空 (可能缺少足够历史)")
            return pd.DataFrame()
        return pd.DataFrame(records)

    # extract_all_features -> 仍用于训练阶段 (逐月展开或仅返回窗口最新月?)
    def extract_all_features(self, df: pd.DataFrame, training_month: str = None, mode: str = 'training') -> pd.DataFrame:
        """统一入口:
        mode='training': 返回窗口内所有月度样本 (逐月展开)；若提供 training_month，可过滤只保留 <= training_month 且与其同窗口的月份
        mode='future':   返回未来预测月样本 (使用 prediction_month=training_month 参数)
        """
        if mode == 'future':
            return self.create_future_prediction_sample(df, training_month)
        # training 模式
        full = self.create_training_dataset(df)
        if full.empty:
            return full
        if training_month and len(training_month) == 6 and training_month.isdigit():
            # 仅保留小于等于 training_month 的月份
            ym_int = int(training_month)
            if 'year' in full.columns and 'month' in full.columns:
                full['__ym'] = full['year'] * 100 + full['month']
                # 只取窗口内 (training_month, training_month-1, training_month-2)
                def shift(ym: str, delta: int) -> int:
                    y = int(ym[:4]); m = int(ym[4:6])
                    total = y * 12 + m - 1 + delta
                    ny = total // 12; nm = total % 12 + 1
                    return ny * 100 + nm
                valid_set = {shift(training_month, 0), shift(training_month, -1), shift(training_month, -2)}
                full = full[full['__ym'].isin(valid_set)].copy()
                full.drop(columns=['__ym'], inplace=True)
        return full

    def export_business_features(self, df: pd.DataFrame, ensure_raw: bool = True) -> pd.DataFrame:
        """生成业务特征文件，剥离衍生/统计/交互列。
        保留列:
        user, year, month, local_network, business_type, volume_type, gear_label,
        total_volume, trigger_usage, lag_amount,
        prev_1m_total_volume, prev_1m_trigger_usage, prev_1m_lag_amount,
        prev_2m_total_volume, prev_2m_trigger_usage, prev_2m_lag_amount

        Args:
            df: extract_all_features 输出或上游的原始特征 DataFrame
            ensure_raw: 若为 True，尝试恢复被错误缩放的 year/month/prev_* 列（旧历史文件兼容）
        Returns:
            DataFrame: 业务标准列顺序的特征数据
        """
        required_cols = [
            'user','year','month','local_network','business_type','volume_type','gear_label',
            'total_volume','trigger_usage','lag_amount',
            'prev_1m_total_volume','prev_1m_trigger_usage','prev_1m_lag_amount',
            'prev_2m_total_volume','prev_2m_trigger_usage','prev_2m_lag_amount'
        ]

        df_in = df.copy()

        # 1. 只保留必须列，不存在的先补列
        for c in required_cols:
            if c not in df_in.columns:
                df_in[c] = np.nan

        biz_df = df_in[required_cols].copy()

        # 2. 双重过滤档位标签：移除 0 和 1
        if 'gear_label' in biz_df.columns:
            mask_valid = ~biz_df['gear_label'].isin([0, 1]) & biz_df['gear_label'].notna()
            removed = len(biz_df) - int(mask_valid.sum())
            if removed > 0:
                self.logger.info(f"export_business_features: 过滤 gear_label 为0/1 行 {removed} 条")
            biz_df = biz_df[mask_valid]

        # 3. 如果旧数据 year / month 被标准化为近似 0 均值的浮点，需要恢复或直接丢弃
        if ensure_raw:
            # heuristic: 年应该是 >= 2000, 若 year 列全在 [-5,5] 之间视为被缩放
            if 'year' in biz_df.columns and biz_df['year'].notna().any():
                yr_series = biz_df['year'].dropna()
                if (yr_series.abs() < 10).all():
                    self.logger.warning("检测到 year 列疑似被缩放，无法还原具体年份，后续需重新跑特征提取")
                    # 无法恢复具体年份 -> 暂置空，由调用者决定是否丢弃
                    biz_df['year'] = np.nan
            if 'month' in biz_df.columns and biz_df['month'].notna().any():
                mo_series = biz_df['month'].dropna()
                # month 正常 1-12；若出现大量非整数或绝对值<5 的小数，视为被缩放
                if (~mo_series.isin(range(1,13))).mean() > 0.8:
                    self.logger.warning("检测到 month 列疑似被缩放，无法还原具体月份，暂置空")
                    biz_df['month'] = np.nan

        # 4. 校验与修正滞后量范围 (0.5% - 3%) 仅日志提示，不自动裁剪训练集；输出文件可选择保留
        if all(c in biz_df.columns for c in ['lag_amount','total_volume']):
            with np.errstate(divide='ignore', invalid='ignore'):
                pct = (biz_df['lag_amount'] / biz_df['total_volume']) * 100
            if pct.notna().any():
                below = int((pct < 0.5).sum())
                above = int((pct > 3.0).sum())
                if below or above:
                    self.logger.warning(f"业务输出中发现滞后量百分比越界: <0.5% {below} 条, >3% {above} 条; 建议上游清洗环节拦截")

        # 5. 排序：按 user, year, month
        sort_cols = [c for c in ['user','year','month'] if c in biz_df.columns]
        if sort_cols:
            biz_df = biz_df.sort_values(sort_cols)

        biz_df.reset_index(drop=True, inplace=True)
        self.logger.info(f"业务特征导出完成，行数 {len(biz_df)}，列 {len(biz_df.columns)}")
        return biz_df

    # ---------------------------------------------------------
    
    def save_features_to_file(self, df: pd.DataFrame, local_network: str, 
                             output_dir: str = None, timestamp: str = None) -> str:
        """
        保存特征工程结果到文件
        
        Args:
            df: 特征数据DataFrame
            local_network: 本地网标识
            output_dir: 输出目录
            timestamp: 时间戳 (YYYYMM格式)
            
        Returns:
            保存的文件路径
        """
        if output_dir is None:
            output_dir = 'data/processed'
        
        if timestamp is None:
            timestamp = datetime.now().strftime('%Y%m')

        # 要生成的文件名: CC_DAT_AI_TRAIN_OUT_本地网_时间(YYYYMM)_序号.txt
        sequence_num = '001'  # 序号，后续可以根据文件大小调整
        filename = f"CC_DAT_AI_TRAIN_OUT_{local_network}_{timestamp}_{sequence_num}.txt"
        filepath = os.path.join(output_dir, filename)
        
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            # 使用|分隔符
            df.to_csv(filepath, sep='|', index=False, encoding='utf-8')
            
            self.logger.info(f"特征工程结果已保存: {filepath}")
            self.logger.info(f"文件格式: CC_DAT_AI_TRAIN_OUT_{local_network}_{timestamp}_{sequence_num}.txt")
            self.logger.info(f"数据行数: {len(df)}")
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存特征文件失败: {e}")
            raise
    
    def split_large_file(self, df: pd.DataFrame, local_network: str, 
                        output_dir: str = None, timestamp: str = None, 
                        max_rows: int = 500000) -> List[str]:
        """
        分割大文件 - 数据文件50W行一个文件
        
        Args:
            df: 特征数据DataFrame
            local_network: 本地网标识
            output_dir: 输出目录
            timestamp: 时间戳 (YYYYMM格式)
            max_rows: 每个文件最大行数
            
        Returns:
            保存的文件路径列表
        """
        if output_dir is None:
            output_dir = 'data/processed'
        
        if timestamp is None:
            timestamp = datetime.now().strftime('%Y%m')
        
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        file_paths = []
        total_rows = len(df)
        
        if total_rows <= max_rows:
            # 不需要分割
            filepath = self.save_features_to_file(df, local_network, output_dir, timestamp)
            file_paths.append(filepath)
        else:
            # 需要分割
            num_files = (total_rows + max_rows - 1) // max_rows  # 向上取整
            self.logger.info(f"数据量 {total_rows} 行超过 {max_rows} 行限制，将分割为 {num_files} 个文件")
            
            for i in range(num_files):
                start_idx = i * max_rows
                end_idx = min((i + 1) * max_rows, total_rows)
                
                # 获取当前分片
                df_chunk = df.iloc[start_idx:end_idx]
                
                # 生成序号（三位数格式）
                sequence_num = f"{i+1:03d}"
                filename = f"CC_DAT_AI_TRAIN_OUT_{local_network}_{timestamp}_{sequence_num}.txt"
                filepath = os.path.join(output_dir, filename)
                
                try:
                    # 保存分片
                    df_chunk.to_csv(filepath, sep='|', index=False, encoding='utf-8')
                    file_paths.append(filepath)
                    
                    self.logger.info(f"分片 {i+1}/{num_files} 已保存: {filename} ({len(df_chunk)} 行)")
                    
                except Exception as e:
                    self.logger.error(f"保存分片 {i+1} 失败: {e}")
                    raise
        
        self.logger.info(f"文件分割完成，共生成 {len(file_paths)} 个文件")
        return file_paths
    
    def get_feature_info(self, df: pd.DataFrame) -> Dict:
        """
        获取特征信息
        
        Args:
            df: 特征DataFrame
            
        Returns:
            特征信息字典
        """
        if df.empty:
            return {'error': '数据为空'}
        
        feature_info = {
            'total_features': len(df.columns),
            'total_records': len(df),
            'feature_types': {},
            'missing_rates': {},
            'feature_categories': {
                'time_features': [],
                'statistical_features': [],
                'categorical_features': [],
                'lag_features': []
            }
        }
        
        # 分析特征类型
        for col in df.columns:
            dtype = df[col].dtype
            if pd.api.types.is_numeric_dtype(dtype):
                feature_info['feature_types'][col] = 'numeric'
            else:
                feature_info['feature_types'][col] = 'categorical'
            
            # 计算缺失率
            missing_rate = df[col].isnull().sum() / len(df)
            feature_info['missing_rates'][col] = missing_rate
            
            # 分类特征
            if col in ['year', 'month', 'day', 'hour', 'weekday', 'is_weekend', 'year_month']:
                feature_info['feature_categories']['time_features'].append(col)
            elif 'prev_' in col:  # 更新为新的字段名
                feature_info['feature_categories']['lag_features'].append(col)
            elif col in ['usage_rate', 'lag_rate'] or '_change' in col:
                feature_info['feature_categories']['statistical_features'].append(col)
            elif col in ['gear_category', 'business_category', 'season']:
                feature_info['feature_categories']['categorical_features'].append(col)
        
        return feature_info