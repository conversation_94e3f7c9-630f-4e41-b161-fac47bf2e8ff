2025-09-16 10:20:55:889961|model_training|9524|13900|INFO|train_model.py:99|==================================================
2025-09-16 10:20:55:889961|model_training|9524|13900|INFO|train_model.py:100|开始模型训练流程 (单本地网强制模式)
2025-09-16 10:20:55:889961|model_training|9524|13900|INFO|train_model.py:101|配置文件: config/config.yaml
2025-09-16 10:20:55:889961|model_training|9524|13900|INFO|train_model.py:102|数据目录: data/raw
2025-09-16 10:20:55:889961|model_training|9524|13900|INFO|train_model.py:103|输出目录: models/trained
2025-09-16 10:20:55:889961|model_training|9524|13900|INFO|train_model.py:104|==================================================
2025-09-16 10:20:55:889961|model_training|9524|13900|INFO|train_model.py:107|步骤 1: 数据加载
2025-09-16 10:20:55:889961|model_training|9524|13900|INFO|train_model.py:130|预测目标月: 202509; 训练月(当前样本月): 202508; 加载历史窗口: ['202506', '202507', '202508']
2025-09-16 10:20:55:889961|model_training|9524|13900|INFO|train_model.py:135|数据加载子步骤: 按需选择历史月份文件并加载
