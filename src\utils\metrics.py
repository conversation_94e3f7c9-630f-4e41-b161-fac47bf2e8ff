"""
评估指标工具模块

提供各种机器学习模型评估指标的计算和分析功能
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    mean_absolute_percentage_error, explained_variance_score,
    max_error, median_absolute_error
)
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class RegressionMetrics:
    """回归评估指标类"""
    
    @staticmethod
    def calculate_all_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """
        计算所有回归指标
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            指标字典
        """
        metrics = {}
        
        try:
            # 基础回归指标
            metrics['mse'] = mean_squared_error(y_true, y_pred)
            metrics['rmse'] = np.sqrt(metrics['mse'])
            metrics['mae'] = mean_absolute_error(y_true, y_pred)
            metrics['r2'] = r2_score(y_true, y_pred)
            metrics['explained_variance'] = explained_variance_score(y_true, y_pred)
            metrics['max_error'] = max_error(y_true, y_pred)
            metrics['median_ae'] = median_absolute_error(y_true, y_pred)
            
            # MAPE (处理除零情况)
            non_zero_mask = y_true != 0
            if np.any(non_zero_mask):
                metrics['mape'] = mean_absolute_percentage_error(
                    y_true[non_zero_mask], y_pred[non_zero_mask]
                ) * 100
            else:
                metrics['mape'] = np.inf
            
            # 自定义指标
            metrics.update(RegressionMetrics._calculate_custom_metrics(y_true, y_pred))
            
        except Exception as e:
            print(f"计算回归指标失败: {e}")
        
        return metrics
    
    @staticmethod
    def _calculate_custom_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """计算自定义指标"""
        custom_metrics = {}
        
        # 误差统计
        errors = y_pred - y_true
        custom_metrics['mean_error'] = np.mean(errors)  # 偏差
        custom_metrics['std_error'] = np.std(errors)    # 误差标准差
        custom_metrics['abs_mean_error'] = np.mean(np.abs(errors))
        
        # 相对误差
        if np.mean(np.abs(y_true)) > 0:
            custom_metrics['relative_error'] = np.mean(np.abs(errors)) / np.mean(np.abs(y_true))
        else:
            custom_metrics['relative_error'] = np.inf
        
        # 对称平均绝对百分比误差 (SMAPE)
        denominator = (np.abs(y_true) + np.abs(y_pred)) / 2
        non_zero_mask = denominator != 0
        if np.any(non_zero_mask):
            smape = np.mean(np.abs(errors[non_zero_mask]) / denominator[non_zero_mask]) * 100
            custom_metrics['smape'] = smape
        else:
            custom_metrics['smape'] = 0
        
        # 均方根对数误差 (RMSLE)
        if np.all(y_true >= 0) and np.all(y_pred >= 0):
            rmsle = np.sqrt(np.mean((np.log1p(y_true) - np.log1p(y_pred)) ** 2))
            custom_metrics['rmsle'] = rmsle
        
        # 平均绝对百分比误差的变体
        if np.all(y_true > 0):
            custom_metrics['wape'] = np.sum(np.abs(errors)) / np.sum(y_true) * 100  # WAPE
        
        return custom_metrics
    
    @staticmethod
    def calculate_residual_statistics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """
        计算残差统计信息
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            残差统计字典
        """
        residuals = y_pred - y_true
        
        stats_dict = {
            'residual_mean': np.mean(residuals),
            'residual_std': np.std(residuals),
            'residual_min': np.min(residuals),
            'residual_max': np.max(residuals),
            'residual_median': np.median(residuals),
            'residual_q25': np.percentile(residuals, 25),
            'residual_q75': np.percentile(residuals, 75),
            'residual_iqr': np.percentile(residuals, 75) - np.percentile(residuals, 25),
            'residual_skewness': stats.skew(residuals),
            'residual_kurtosis': stats.kurtosis(residuals)
        }
        
        # 正态性检验
        try:
            _, p_value = stats.shapiro(residuals[:5000])  # 限制样本数量
            stats_dict['shapiro_p_value'] = p_value
            stats_dict['is_normal'] = p_value > 0.05
        except Exception:
            stats_dict['shapiro_p_value'] = np.nan
            stats_dict['is_normal'] = False
        
        return stats_dict


class BusinessMetrics:
    """业务指标类"""
    
    def __init__(self, min_percentage: float = 0.5, max_percentage: float = 3.0, 
                 risk_threshold: float = 0.5):
        """
        初始化业务指标计算器
        
        Args:
            min_percentage: 最小滞后百分比
            max_percentage: 最大滞后百分比
            risk_threshold: 风险阈值
        """
        self.min_percentage = min_percentage
        self.max_percentage = max_percentage
        self.risk_threshold = risk_threshold
    
    def calculate_percentage_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                                   total_volumes: np.ndarray) -> Dict[str, float]:
        """
        计算百分比相关指标
        
        Args:
            y_true: 真实滞后量
            y_pred: 预测滞后量
            total_volumes: 总量
            
        Returns:
            百分比指标字典
        """
        true_percentages = (y_true / total_volumes) * 100
        pred_percentages = (y_pred / total_volumes) * 100
        
        metrics = {}
        
        # 百分比预测精度
        metrics['percentage_mae'] = np.mean(np.abs(pred_percentages - true_percentages))
        metrics['percentage_mse'] = np.mean((pred_percentages - true_percentages) ** 2)
        metrics['percentage_rmse'] = np.sqrt(metrics['percentage_mse'])
        
        # 业务规则符合率
        in_range_true = np.sum(
            (true_percentages >= self.min_percentage) & 
            (true_percentages <= self.max_percentage)
        ) / len(true_percentages)
        
        in_range_pred = np.sum(
            (pred_percentages >= self.min_percentage) & 
            (pred_percentages <= self.max_percentage)
        ) / len(pred_percentages)
        
        metrics['true_in_range_rate'] = in_range_true
        metrics['pred_in_range_rate'] = in_range_pred
        metrics['range_prediction_accuracy'] = 1 - abs(in_range_pred - in_range_true)
        
        # 风险识别指标
        metrics.update(self._calculate_risk_metrics(true_percentages, pred_percentages))
        
        return metrics
    
    def _calculate_risk_metrics(self, true_percentages: np.ndarray, 
                              pred_percentages: np.ndarray) -> Dict[str, float]:
        """计算风险识别指标"""
        risk_metrics = {}
        
        # 高风险识别（超过最大阈值）
        high_risk_true = true_percentages > self.max_percentage
        high_risk_pred = pred_percentages > self.max_percentage
        
        risk_metrics['true_high_risk_rate'] = np.mean(high_risk_true)
        risk_metrics['pred_high_risk_rate'] = np.mean(high_risk_pred)
        
        # 漏发风险识别（低于风险阈值）
        miss_risk_true = true_percentages < self.risk_threshold
        miss_risk_pred = pred_percentages < self.risk_threshold
        
        risk_metrics['true_miss_risk_rate'] = np.mean(miss_risk_true)
        risk_metrics['pred_miss_risk_rate'] = np.mean(miss_risk_pred)
        
        # 风险识别准确性
        if np.any(high_risk_true) or np.any(high_risk_pred):
            # 高风险识别的精确度和召回率
            tp_high = np.sum(high_risk_true & high_risk_pred)
            fp_high = np.sum(~high_risk_true & high_risk_pred)
            fn_high = np.sum(high_risk_true & ~high_risk_pred)
            
            if tp_high + fp_high > 0:
                risk_metrics['high_risk_precision'] = tp_high / (tp_high + fp_high)
            else:
                risk_metrics['high_risk_precision'] = 0
            
            if tp_high + fn_high > 0:
                risk_metrics['high_risk_recall'] = tp_high / (tp_high + fn_high)
            else:
                risk_metrics['high_risk_recall'] = 0
        
        return risk_metrics
    
    def calculate_segment_performance(self, y_true: np.ndarray, y_pred: np.ndarray,
                                    segments: np.ndarray) -> Dict[str, Dict[str, float]]:
        """
        计算不同细分市场的性能
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            segments: 细分标签
            
        Returns:
            分段性能字典
        """
        segment_performance = {}
        
        unique_segments = np.unique(segments)
        
        for segment in unique_segments:
            mask = segments == segment
            if np.sum(mask) > 0:
                segment_y_true = y_true[mask]
                segment_y_pred = y_pred[mask]
                
                segment_metrics = RegressionMetrics.calculate_all_metrics(
                    segment_y_true, segment_y_pred
                )
                
                segment_performance[str(segment)] = segment_metrics
        
        return segment_performance


class ModelComparisonMetrics:
    """模型比较指标类"""
    
    @staticmethod
    def calculate_model_rankings(metrics_dict: Dict[str, Dict[str, float]],
                               ranking_metrics: List[str] = None) -> Dict[str, Dict[str, int]]:
        """
        计算模型排名
        
        Args:
            metrics_dict: 模型指标字典
            ranking_metrics: 用于排名的指标列表
            
        Returns:
            排名字典
        """
        if ranking_metrics is None:
            ranking_metrics = ['rmse', 'mae', 'mape']
        
        rankings = {}
        
        for metric in ranking_metrics:
            # 提取所有模型的该指标值
            metric_values = {}
            for model_name, model_metrics in metrics_dict.items():
                if metric in model_metrics and not np.isnan(model_metrics[metric]):
                    metric_values[model_name] = model_metrics[metric]
            
            if metric_values:
                # 按指标值排序（越小越好）
                sorted_models = sorted(metric_values.items(), key=lambda x: x[1])
                
                # 分配排名
                for rank, (model_name, _) in enumerate(sorted_models, 1):
                    if model_name not in rankings:
                        rankings[model_name] = {}
                    rankings[model_name][metric] = rank
        
        return rankings
    
    @staticmethod
    def calculate_model_scores(metrics_dict: Dict[str, Dict[str, float]],
                             weights: Dict[str, float] = None) -> Dict[str, float]:
        """
        计算模型综合得分
        
        Args:
            metrics_dict: 模型指标字典
            weights: 指标权重
            
        Returns:
            模型得分字典
        """
        if weights is None:
            weights = {'r2': 0.3, 'rmse': 0.3, 'mae': 0.2, 'mape': 0.2}
        
        scores = {}
        
        # 标准化指标值
        normalized_metrics = ModelComparisonMetrics._normalize_metrics(metrics_dict, weights.keys())
        
        # 计算加权得分
        for model_name, model_metrics in normalized_metrics.items():
            score = 0
            total_weight = 0
            
            for metric, weight in weights.items():
                if metric in model_metrics and not np.isnan(model_metrics[metric]):
                    score += model_metrics[metric] * weight
                    total_weight += weight
            
            if total_weight > 0:
                scores[model_name] = score / total_weight
            else:
                scores[model_name] = 0
        
        return scores
    
    @staticmethod
    def _normalize_metrics(metrics_dict: Dict[str, Dict[str, float]], 
                          metrics_to_normalize: List[str]) -> Dict[str, Dict[str, float]]:
        """标准化指标值到0-1范围"""
        normalized = {}
        
        # 计算每个指标的最值
        metric_ranges = {}
        for metric in metrics_to_normalize:
            values = []
            for model_metrics in metrics_dict.values():
                if metric in model_metrics and not np.isnan(model_metrics[metric]):
                    values.append(model_metrics[metric])
            
            if values:
                metric_ranges[metric] = {
                    'min': min(values),
                    'max': max(values),
                    'range': max(values) - min(values)
                }
        
        # 标准化
        for model_name, model_metrics in metrics_dict.items():
            normalized[model_name] = {}
            
            for metric in metrics_to_normalize:
                if metric in model_metrics and metric in metric_ranges:
                    value = model_metrics[metric]
                    if not np.isnan(value):
                        range_info = metric_ranges[metric]
                        
                        if range_info['range'] > 0:
                            if metric == 'r2':  # R²越大越好
                                normalized_value = (value - range_info['min']) / range_info['range']
                            else:  # 其他指标越小越好
                                normalized_value = 1 - (value - range_info['min']) / range_info['range']
                        else:
                            normalized_value = 1.0
                        
                        normalized[model_name][metric] = max(0, min(1, normalized_value))
        
        return normalized


class MetricsVisualizer:
    """指标可视化类"""
    
    @staticmethod
    def create_metrics_summary(metrics_dict: Dict[str, float]) -> pd.DataFrame:
        """
        创建指标摘要表
        
        Args:
            metrics_dict: 指标字典
            
        Returns:
            指标摘要DataFrame
        """
        summary_data = []
        
        for metric_name, value in metrics_dict.items():
            summary_data.append({
                'Metric': metric_name,
                'Value': value,
                'Description': MetricsVisualizer._get_metric_description(metric_name)
            })
        
        return pd.DataFrame(summary_data)
    
    @staticmethod
    def _get_metric_description(metric_name: str) -> str:
        """获取指标描述"""
        descriptions = {
            'mse': '均方误差',
            'rmse': '均方根误差',
            'mae': '平均绝对误差',
            'r2': '决定系数',
            'mape': '平均绝对百分比误差',
            'explained_variance': '解释方差得分',
            'max_error': '最大误差',
            'median_ae': '中位数绝对误差',
            'mean_error': '平均误差（偏差）',
            'std_error': '误差标准差',
            'percentage_mae': '百分比平均绝对误差',
            'percentage_rmse': '百分比均方根误差',
            'true_in_range_rate': '真实值范围内比例',
            'pred_in_range_rate': '预测值范围内比例',
            'true_high_risk_rate': '真实高风险比例',
            'pred_high_risk_rate': '预测高风险比例'
        }
        
        return descriptions.get(metric_name, '未知指标')
    
    @staticmethod
    def compare_models_table(metrics_dict: Dict[str, Dict[str, float]],
                           selected_metrics: List[str] = None) -> pd.DataFrame:
        """
        创建模型比较表
        
        Args:
            metrics_dict: 模型指标字典
            selected_metrics: 选择的指标
            
        Returns:
            比较表DataFrame
        """
        if selected_metrics is None:
            selected_metrics = ['rmse', 'mae', 'r2', 'mape']
        
        comparison_data = []
        
        for model_name, model_metrics in metrics_dict.items():
            row = {'Model': model_name}
            
            for metric in selected_metrics:
                value = model_metrics.get(metric, np.nan)
                row[metric.upper()] = value
            
            comparison_data.append(row)
        
        return pd.DataFrame(comparison_data)


def calculate_comprehensive_metrics(y_true: np.ndarray, y_pred: np.ndarray,
                                  total_volumes: np.ndarray = None,
                                  segments: np.ndarray = None,
                                  business_config: Dict[str, float] = None) -> Dict[str, Any]:
    """
    计算综合评估指标
    
    Args:
        y_true: 真实值
        y_pred: 预测值
        total_volumes: 总量数组
        segments: 细分标签
        business_config: 业务配置
        
    Returns:
        综合指标字典
    """
    comprehensive_metrics = {}
    
    # 基础回归指标
    comprehensive_metrics['regression'] = RegressionMetrics.calculate_all_metrics(y_true, y_pred)
    
    # 残差统计
    comprehensive_metrics['residuals'] = RegressionMetrics.calculate_residual_statistics(y_true, y_pred)
    
    # 业务指标
    if total_volumes is not None:
        if business_config is None:
            business_config = {'min_percentage': 0.5, 'max_percentage': 3.0, 'risk_threshold': 0.5}
        
        business_metrics = BusinessMetrics(**business_config)
        comprehensive_metrics['business'] = business_metrics.calculate_percentage_metrics(
            y_true, y_pred, total_volumes
        )
    
    # 分段性能
    if segments is not None:
        business_metrics = BusinessMetrics()
        comprehensive_metrics['segments'] = business_metrics.calculate_segment_performance(
            y_true, y_pred, segments
        )
    
    return comprehensive_metrics