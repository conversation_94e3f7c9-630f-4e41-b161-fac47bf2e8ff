"""
模型评估器

负责模型性能评估，包括多种评估指标、可视化分析和业务指标
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
import yaml
from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    mean_absolute_percentage_error
)
import warnings
warnings.filterwarnings('ignore')

class ModelEvaluator:
    """模型评估器类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化模型评估器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 路径配置
        self.evaluations_dir = self.config.get('model', {}).get('evaluations_dir', 'models/evaluations')
        self.metadata_dir = self.config.get('model', {}).get('metadata_dir', 'models/metadata')
        self.figures_dir = os.path.join(self.evaluations_dir, 'figures')
        
        # 创建评估结果目录
        os.makedirs(self.evaluations_dir, exist_ok=True)
        os.makedirs(self.figures_dir, exist_ok=True)
        
        # 业务规则配置
        self.min_lag_percentage = self.config.get('business_rules', {}).get('min_lag_percentage', 0.5)
        self.max_lag_percentage = self.config.get('business_rules', {}).get('max_lag_percentage', 3.0)
        self.risk_threshold = self.config.get('business_rules', {}).get('risk_threshold', 0.5)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/config.yaml')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"警告: 无法加载配置文件: {e}")
            return {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置统一日志: 使用根日志器格式 (TelecomLogFormatter)"""
        logger = logging.getLogger(__name__)
        try:
            from src.utils.logger import TelecomLogFormatter
            for h in list(logger.handlers):
                if isinstance(h, logging.StreamHandler) and not isinstance(getattr(h, 'formatter', None), TelecomLogFormatter):
                    logger.removeHandler(h)
        except Exception:
            pass
        logger.propagate = True
        return logger
    
    def calculate_basic_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """
        计算基础评估指标
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            评估指标字典
        """
        try:
            metrics = {}
            
            # 回归指标
            metrics['mse'] = mean_squared_error(y_true, y_pred)
            metrics['rmse'] = np.sqrt(metrics['mse'])
            metrics['mae'] = mean_absolute_error(y_true, y_pred)
            metrics['r2'] = r2_score(y_true, y_pred)
            
            # MAPE (需要处理除零情况)
            non_zero_mask = y_true != 0
            if np.any(non_zero_mask):
                metrics['mape'] = mean_absolute_percentage_error(
                    y_true[non_zero_mask], y_pred[non_zero_mask]
                ) * 100
            else:
                metrics['mape'] = np.inf
            
            # 自定义指标
            metrics['mean_error'] = np.mean(y_pred - y_true)  # 偏差
            metrics['std_error'] = np.std(y_pred - y_true)    # 误差标准差
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算基础指标失败: {e}")
            return {}
    
    def calculate_business_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                                 total_volumes: np.ndarray = None) -> Dict[str, Any]:
        """
        计算业务相关指标
        
        Args:
            y_true: 真实滞后量
            y_pred: 预测滞后量
            total_volumes: 总量数组
            
        Returns:
            业务指标字典
        """
        try:
            business_metrics = {}
            
            if total_volumes is not None:
                # 计算滞后量百分比
                true_percentages = (y_true / total_volumes) * 100
                pred_percentages = (y_pred / total_volumes) * 100
                
                # 百分比相关指标
                business_metrics['percentage_mae'] = np.mean(np.abs(pred_percentages - true_percentages))
                business_metrics['percentage_mse'] = np.mean((pred_percentages - true_percentages) ** 2)
                business_metrics['percentage_rmse'] = np.sqrt(business_metrics['percentage_mse'])
                
                # 业务规则符合率
                in_range_true = np.sum(
                    (true_percentages >= self.min_lag_percentage) & 
                    (true_percentages <= self.max_lag_percentage)
                ) / len(true_percentages)
                
                in_range_pred = np.sum(
                    (pred_percentages >= self.min_lag_percentage) & 
                    (pred_percentages <= self.max_lag_percentage)
                ) / len(pred_percentages)
                
                business_metrics['true_in_range_rate'] = in_range_true
                business_metrics['pred_in_range_rate'] = in_range_pred
                
                # 风险识别指标
                high_risk_true = np.sum(true_percentages > self.max_lag_percentage) / len(true_percentages)
                high_risk_pred = np.sum(pred_percentages > self.max_lag_percentage) / len(pred_percentages)
                
                business_metrics['true_high_risk_rate'] = high_risk_true
                business_metrics['pred_high_risk_rate'] = high_risk_pred
                
                # 漏发风险
                miss_risk_true = np.sum(true_percentages < self.risk_threshold) / len(true_percentages)
                miss_risk_pred = np.sum(pred_percentages < self.risk_threshold) / len(pred_percentages)
                
                business_metrics['true_miss_risk_rate'] = miss_risk_true
                business_metrics['pred_miss_risk_rate'] = miss_risk_pred
            
            return business_metrics
            
        except Exception as e:
            self.logger.error(f"计算业务指标失败: {e}")
            return {}
    
    def calculate_distribution_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """
        计算分布相关指标
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            分布指标字典
        """
        try:
            dist_metrics = {}
            
            # 分位数比较
            quantiles = [0.25, 0.5, 0.75, 0.9, 0.95]
            for q in quantiles:
                true_quantile = np.quantile(y_true, q)
                pred_quantile = np.quantile(y_pred, q)
                dist_metrics[f'q{int(q*100)}_true'] = true_quantile
                dist_metrics[f'q{int(q*100)}_pred'] = pred_quantile
                dist_metrics[f'q{int(q*100)}_diff'] = abs(pred_quantile - true_quantile)
            
            # 分布统计
            dist_metrics['mean_true'] = np.mean(y_true)
            dist_metrics['mean_pred'] = np.mean(y_pred)
            dist_metrics['std_true'] = np.std(y_true)
            dist_metrics['std_pred'] = np.std(y_pred)
            
            return dist_metrics
            
        except Exception as e:
            self.logger.error(f"计算分布指标失败: {e}")
            return {}
    
    def evaluate_predictions(self, y_true: np.ndarray, y_pred: np.ndarray,
                           total_volumes: np.ndarray = None,
                           dataset_name: str = "default") -> Dict[str, Any]:
        """
        综合评估预测结果
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            total_volumes: 总量数组
            dataset_name: 数据集名称
            
        Returns:
            完整评估结果
        """
        self.logger.info(f"开始评估数据集: {dataset_name}")
        
        evaluation_result = {
            'dataset_name': dataset_name,
            'evaluation_timestamp': datetime.now().isoformat(),
            'sample_count': len(y_true),
            'basic_metrics': {},
            'business_metrics': {},
            'distribution_metrics': {}
        }
        
        # 基础指标
        evaluation_result['basic_metrics'] = self.calculate_basic_metrics(y_true, y_pred)
        
        # 业务指标
        evaluation_result['business_metrics'] = self.calculate_business_metrics(
            y_true, y_pred, total_volumes
        )
        
        # 分布指标
        evaluation_result['distribution_metrics'] = self.calculate_distribution_metrics(y_true, y_pred)
        
        self.logger.info(f"数据集 {dataset_name} 评估完成")
        
        return evaluation_result
    
    def plot_prediction_scatter(self, y_true: np.ndarray, y_pred: np.ndarray,
                              dataset_name: str = "default",
                              save_path: str = None) -> str:
        """
        绘制预测值vs真实值散点图
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            dataset_name: 数据集名称
            save_path: 保存路径
            
        Returns:
            图片保存路径
        """
        try:
            plt.figure(figsize=(10, 8))
            
            # 散点图
            plt.scatter(y_true, y_pred, alpha=0.6, s=20)
            
            # 理想线
            min_val = min(y_true.min(), y_pred.min())
            max_val = max(y_true.max(), y_pred.max())
            plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='理想预测线')
            
            # 计算R²
            r2 = r2_score(y_true, y_pred)
            
            plt.xlabel('真实值')
            plt.ylabel('预测值')
            plt.title(f'{dataset_name} - 预测值 vs 真实值 (R² = {r2:.4f})')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # 保存图片
            if save_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                save_path = os.path.join(
                    self.figures_dir, 
                    f'scatter_{dataset_name}_{timestamp}.png'
                )
            
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"散点图已保存: {save_path}")
            
            return save_path
            
        except Exception as e:
            self.logger.error(f"绘制散点图失败: {e}")
            return ""
    
    def plot_residuals(self, y_true: np.ndarray, y_pred: np.ndarray,
                      dataset_name: str = "default",
                      save_path: str = None) -> str:
        """
        绘制残差图
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            dataset_name: 数据集名称
            save_path: 保存路径
            
        Returns:
            图片保存路径
        """
        try:
            residuals = y_pred - y_true
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'{dataset_name} - 残差分析', fontsize=16)
            
            # 残差vs预测值
            axes[0, 0].scatter(y_pred, residuals, alpha=0.6, s=20)
            axes[0, 0].axhline(y=0, color='r', linestyle='--')
            axes[0, 0].set_xlabel('预测值')
            axes[0, 0].set_ylabel('残差')
            axes[0, 0].set_title('残差 vs 预测值')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 残差vs真实值
            axes[0, 1].scatter(y_true, residuals, alpha=0.6, s=20)
            axes[0, 1].axhline(y=0, color='r', linestyle='--')
            axes[0, 1].set_xlabel('真实值')
            axes[0, 1].set_ylabel('残差')
            axes[0, 1].set_title('残差 vs 真实值')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 残差直方图
            axes[1, 0].hist(residuals, bins=50, alpha=0.7, edgecolor='black')
            axes[1, 0].axvline(x=0, color='r', linestyle='--')
            axes[1, 0].set_xlabel('残差')
            axes[1, 0].set_ylabel('频数')
            axes[1, 0].set_title('残差分布')
            
            # Q-Q图
            from scipy import stats
            stats.probplot(residuals, dist="norm", plot=axes[1, 1])
            axes[1, 1].set_title('残差Q-Q图')
            
            plt.tight_layout()
            
            # 保存图片
            if save_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                save_path = os.path.join(
                    self.figures_dir, 
                    f'residuals_{dataset_name}_{timestamp}.png'
                )
            
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"残差图已保存: {save_path}")
            
            return save_path
            
        except Exception as e:
            self.logger.error(f"绘制残差图失败: {e}")
            return ""
    
    def plot_percentage_analysis(self, y_true: np.ndarray, y_pred: np.ndarray,
                               total_volumes: np.ndarray,
                               dataset_name: str = "default",
                               save_path: str = None) -> str:
        """
        绘制百分比分析图
        
        Args:
            y_true: 真实滞后量
            y_pred: 预测滞后量
            total_volumes: 总量
            dataset_name: 数据集名称
            save_path: 保存路径
            
        Returns:
            图片保存路径
        """
        try:
            true_percentages = (y_true / total_volumes) * 100
            pred_percentages = (y_pred / total_volumes) * 100
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'{dataset_name} - 滞后量百分比分析', fontsize=16)
            
            # 百分比散点图
            axes[0, 0].scatter(true_percentages, pred_percentages, alpha=0.6, s=20)
            axes[0, 0].plot([0, 5], [0, 5], 'r--', linewidth=2)
            axes[0, 0].axvline(x=self.min_lag_percentage, color='g', linestyle='--', alpha=0.7, label='最小阈值')
            axes[0, 0].axvline(x=self.max_lag_percentage, color='orange', linestyle='--', alpha=0.7, label='最大阈值')
            axes[0, 0].axhline(y=self.min_lag_percentage, color='g', linestyle='--', alpha=0.7)
            axes[0, 0].axhline(y=self.max_lag_percentage, color='orange', linestyle='--', alpha=0.7)
            axes[0, 0].set_xlabel('真实百分比 (%)')
            axes[0, 0].set_ylabel('预测百分比 (%)')
            axes[0, 0].set_title('滞后量百分比对比')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            
            # 百分比分布对比
            bins = np.linspace(0, min(10, max(true_percentages.max(), pred_percentages.max())), 50)
            axes[0, 1].hist(true_percentages, bins=bins, alpha=0.7, label='真实分布', density=True)
            axes[0, 1].hist(pred_percentages, bins=bins, alpha=0.7, label='预测分布', density=True)
            axes[0, 1].axvline(x=self.min_lag_percentage, color='g', linestyle='--', alpha=0.7, label='最小阈值')
            axes[0, 1].axvline(x=self.max_lag_percentage, color='orange', linestyle='--', alpha=0.7, label='最大阈值')
            axes[0, 1].set_xlabel('滞后量百分比 (%)')
            axes[0, 1].set_ylabel('密度')
            axes[0, 1].set_title('百分比分布对比')
            axes[0, 1].legend()
            
            # 不同范围的样本比例
            ranges = ['<0.5%', '0.5%-3%', '>3%']
            true_counts = [
                np.sum(true_percentages < self.min_lag_percentage),
                np.sum((true_percentages >= self.min_lag_percentage) & 
                       (true_percentages <= self.max_lag_percentage)),
                np.sum(true_percentages > self.max_lag_percentage)
            ]
            pred_counts = [
                np.sum(pred_percentages < self.min_lag_percentage),
                np.sum((pred_percentages >= self.min_lag_percentage) & 
                       (pred_percentages <= self.max_lag_percentage)),
                np.sum(pred_percentages > self.max_lag_percentage)
            ]
            
            x = np.arange(len(ranges))
            width = 0.35
            
            axes[1, 0].bar(x - width/2, true_counts, width, label='真实', alpha=0.8)
            axes[1, 0].bar(x + width/2, pred_counts, width, label='预测', alpha=0.8)
            axes[1, 0].set_xlabel('滞后量百分比范围')
            axes[1, 0].set_ylabel('样本数量')
            axes[1, 0].set_title('不同范围样本分布')
            axes[1, 0].set_xticks(x)
            axes[1, 0].set_xticklabels(ranges)
            axes[1, 0].legend()
            
            # 百分比误差分布
            percentage_errors = pred_percentages - true_percentages
            axes[1, 1].hist(percentage_errors, bins=50, alpha=0.7, edgecolor='black')
            axes[1, 1].axvline(x=0, color='r', linestyle='--')
            axes[1, 1].set_xlabel('百分比误差 (%)')
            axes[1, 1].set_ylabel('频数')
            axes[1, 1].set_title('百分比预测误差分布')
            
            plt.tight_layout()
            
            # 保存图片
            if save_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                save_path = os.path.join(
                    self.figures_dir, 
                    f'percentage_{dataset_name}_{timestamp}.png'
                )
            
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"百分比分析图已保存: {save_path}")
            
            return save_path
            
        except Exception as e:
            self.logger.error(f"绘制百分比分析图失败: {e}")
            return ""
    
    def generate_evaluation_report(self, evaluation_results: List[Dict[str, Any]],
                                 report_name: str = None) -> str:
        """
        生成评估报告
        
        Args:
            evaluation_results: 评估结果列表
            report_name: 报告名称
            
        Returns:
            报告文件路径
        """
        try:
            if report_name is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                report_name = f'evaluation_report_{timestamp}'
            
            report_path = os.path.join(self.evaluations_dir, f'{report_name}.json')
            
            # 汇总报告
            summary_report = {
                'report_name': report_name,
                'generation_timestamp': datetime.now().isoformat(),
                'total_datasets': len(evaluation_results),
                'individual_evaluations': evaluation_results,
                'summary_statistics': {}
            }
            
            # 计算汇总统计
            if evaluation_results:
                all_basic_metrics = {}
                all_business_metrics = {}
                
                for eval_result in evaluation_results:
                    # 收集基础指标
                    for metric, value in eval_result.get('basic_metrics', {}).items():
                        if metric not in all_basic_metrics:
                            all_basic_metrics[metric] = []
                        all_basic_metrics[metric].append(value)
                    
                    # 收集业务指标
                    for metric, value in eval_result.get('business_metrics', {}).items():
                        if metric not in all_business_metrics:
                            all_business_metrics[metric] = []
                        all_business_metrics[metric].append(value)
                
                # 计算平均值
                summary_basic = {}
                for metric, values in all_basic_metrics.items():
                    if values and all(isinstance(v, (int, float)) for v in values):
                        summary_basic[f'{metric}_mean'] = np.mean(values)
                        summary_basic[f'{metric}_std'] = np.std(values)
                
                summary_business = {}
                for metric, values in all_business_metrics.items():
                    if values and all(isinstance(v, (int, float)) for v in values):
                        summary_business[f'{metric}_mean'] = np.mean(values)
                        summary_business[f'{metric}_std'] = np.std(values)
                
                summary_report['summary_statistics'] = {
                    'basic_metrics_summary': summary_basic,
                    'business_metrics_summary': summary_business
                }
            
            # 保存报告
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(summary_report, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"评估报告已保存: {report_path}")
            
            return report_path
            
        except Exception as e:
            self.logger.error(f"生成评估报告失败: {e}")
            return ""
    
    def compare_models(self, evaluation_results: List[Dict[str, Any]],
                      comparison_name: str = None) -> str:
        """
        比较多个模型的性能
        
        Args:
            evaluation_results: 评估结果列表
            comparison_name: 比较名称
            
        Returns:
            比较报告路径
        """
        try:
            if comparison_name is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                comparison_name = f'model_comparison_{timestamp}'
            
            # 创建比较表格
            comparison_data = []
            
            for eval_result in evaluation_results:
                row = {
                    'dataset_name': eval_result.get('dataset_name', 'Unknown'),
                    'sample_count': eval_result.get('sample_count', 0)
                }
                
                # 添加基础指标
                basic_metrics = eval_result.get('basic_metrics', {})
                for metric in ['rmse', 'mae', 'r2', 'mape']:
                    row[metric] = basic_metrics.get(metric, np.nan)
                
                # 添加业务指标
                business_metrics = eval_result.get('business_metrics', {})
                for metric in ['percentage_rmse', 'pred_in_range_rate', 'pred_high_risk_rate']:
                    row[metric] = business_metrics.get(metric, np.nan)
                
                comparison_data.append(row)
            
            # 转换为DataFrame
            comparison_df = pd.DataFrame(comparison_data)
            
            # 保存比较表格
            comparison_path = os.path.join(self.evaluations_dir, f'{comparison_name}.csv')
            comparison_df.to_csv(comparison_path, index=False, encoding='utf-8')
            
            # 绘制比较图
            self._plot_model_comparison(comparison_df, comparison_name)
            
            self.logger.info(f"模型比较结果已保存: {comparison_path}")
            
            return comparison_path
            
        except Exception as e:
            self.logger.error(f"模型比较失败: {e}")
            return ""
    
    def _plot_model_comparison(self, comparison_df: pd.DataFrame, comparison_name: str):
        """绘制模型比较图"""
        try:
            metrics_to_plot = ['rmse', 'mae', 'r2', 'mape']
            available_metrics = [m for m in metrics_to_plot if m in comparison_df.columns]
            
            if not available_metrics:
                return
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'{comparison_name} - 模型性能比较', fontsize=16)
            
            axes = axes.flatten()
            
            for i, metric in enumerate(available_metrics[:4]):
                ax = axes[i]
                
                # 柱状图
                bars = ax.bar(comparison_df['dataset_name'], comparison_df[metric], 
                             alpha=0.7, color=plt.cm.Set3(i))
                
                ax.set_title(f'{metric.upper()} 比较')
                ax.set_ylabel(metric.upper())
                ax.tick_params(axis='x', rotation=45)
                
                # 添加数值标签
                for bar, value in zip(bars, comparison_df[metric]):
                    if not np.isnan(value):
                        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                               f'{value:.4f}', ha='center', va='bottom', fontsize=8)
            
            plt.tight_layout()
            
            # 保存图片
            figure_path = os.path.join(self.figures_dir, f'{comparison_name}_comparison.png')
            plt.savefig(figure_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"比较图已保存: {figure_path}")
            
        except Exception as e:
            self.logger.error(f"绘制比较图失败: {e}")
    
    def load_evaluation_history(self, pattern: str = "*.json") -> List[Dict[str, Any]]:
        """
        加载历史评估结果
        
        Args:
            pattern: 文件匹配模式
            
        Returns:
            历史评估结果列表
        """
        history = []
        
        try:
            evaluation_files = Path(self.evaluations_dir).glob(pattern)
            
            for file_path in evaluation_files:
                with open(file_path, 'r', encoding='utf-8') as f:
                    evaluation_data = json.load(f)
                    history.append(evaluation_data)
            
            self.logger.info(f"加载了 {len(history)} 个历史评估结果")
            
        except Exception as e:
            self.logger.error(f"加载评估历史失败: {e}")
        
        return history