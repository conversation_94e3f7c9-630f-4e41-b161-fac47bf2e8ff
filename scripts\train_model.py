"""
模型训练脚本

用于训练滞后量预测模型
"""

import os
import sys
import argparse
import yaml
import logging
import pandas as pd
from datetime import datetime
from pathlib import Path

import joblib
import numpy as np
# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data_processor import DataLoader, DataCleaner, DataValidator
from src.feature_engineer import FeatureExtractor, FeatureTransformer, FeatureSelector
from src.model import ModelTrainer
from src.utils import setup_project_logging, get_module_logger, DateUtils, FileUtils, get_logging_level, init_logging_system

def parse_arguments():
    """解析命令行参数

    变更：--latnid 现为必填参数，强制一次只训练一个本地网，避免多本地网嵌套并行导致的资源竞争。
    """
    parser = argparse.ArgumentParser(description='训练滞后量预测模型 (单本地网模式)')

    parser.add_argument('--config', '-c', type=str,
                        default='config/config.yaml',
                        help='配置文件路径')

    parser.add_argument('--latnid', '--local-network', '-n', type=str,
                        required=True,
                        help='必须指定本地网（一次仅训练该本地网模型）')

    parser.add_argument('--bill-cycle', '--billing-month', '--target-month', '-m', type=str,
                        required=False,
                        help='要训练的模型账期 (YYYYMM)。若不提供，默认为当前系统月份。模型将使用该账期的前两个月数据训练。')

    return parser.parse_args()

def load_and_update_config(config_path: str, args) -> dict:
    """加载配置；仅允许少量运行期参数覆盖（如并行数），其余以配置文件为准"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        sys.exit(1)

    # 不再支持命令行覆盖并行参数，全部使用配置文件

    return config

def setup_logging(config: dict):
    """设置日志（完全依赖配置文件）"""
    log_dir = config.get('logging', {}).get('log_dir', 'logs')
    config_level = config.get('logging', {}).get('level', 4)  # 默认INFO
    console_level = config_level
    file_level = 6  # 文件仍使用DEBUG级别

    init_logging_system(log_dir, console_level, file_level)
    return setup_project_logging('model_training', log_dir)

def validate_config(config: dict, logger):
    """验证配置"""
    required_sections = ['data', 'model', 'feature_engineering']

    for section in required_sections:
        if section not in config:
            logger.error(f"配置文件缺少必需的部分: {section}")
            return False

    # 验证数据目录
    data_dir = config['data']['raw_data_dir']
    if not os.path.exists(data_dir):
        logger.error(f"数据目录不存在: {data_dir}")
        return False

    return True

def evaluate_and_log_feature_importance(results: dict, logger) -> None:
    """根据训练结果加载模型并输出特征重要性排行榜日志"""
    def _unwrap_model(obj):
        if isinstance(obj, dict) and 'base_model' in obj:
            return obj.get('base_model')
        return obj

    for local_network, res in results.items():
        try:
            if not res or not res.get('success', False):
                continue
            model_path = res.get('model_path')
            feature_names = list(res.get('feature_names', []) or [])
            if not model_path or not os.path.exists(model_path):
                logger.warning(f"本地网 {local_network}: 模型文件缺失，跳过特征重要性评估: {model_path}")
                continue
            try:
                loaded = joblib.load(model_path)
            except Exception as le:
                logger.error(f"本地网 {local_network}: 加载模型失败，跳过特征重要性评估: {le}")
                continue
            model = _unwrap_model(loaded)

            importances = None
            try:
                if hasattr(model, 'feature_importances_') and getattr(model, 'feature_importances_') is not None:
                    importances = np.asarray(model.feature_importances_, dtype=float)
                elif hasattr(model, 'coef_') and getattr(model, 'coef_') is not None:
                    importances = np.abs(np.ravel(model.coef_)).astype(float)
                elif hasattr(model, 'get_booster'):
                    # xgboost 兜底：按 f0,f1,... 对齐到特征顺序
                    booster = model.get_booster()
                    score_dict = booster.get_score(importance_type='gain')
                    importances = np.array([score_dict.get(f"f{i}", 0.0) for i in range(len(feature_names))], dtype=float)
            except Exception as ei:
                logger.warning(f"本地网 {local_network}: 计算特征重要性出错: {ei}")
                importances = None

            if importances is None or importances.size == 0:
                logger.warning(f"本地网 {local_network}: 模型不支持内置特征重要性或结果为空，跳过")
                continue

            # 对齐长度
            if feature_names and len(feature_names) != len(importances):
                minlen = min(len(feature_names), len(importances))
                logger.warning(
                    f"本地网 {local_network}: 特征数与重要性长度不一致({len(feature_names)} vs {len(importances)}), 已按前{minlen}对齐")
                feature_names = feature_names[:minlen]
                importances = importances[:minlen]
            if not feature_names:
                feature_names = [f"f{i}" for i in range(len(importances))]

            order = np.argsort(importances)[::-1]
            total = float(importances.sum()) if importances.size else 0.0

            logger.info("-" * 50)
            logger.info(f"本地网 {local_network} 特征重要性排行榜（共{len(importances)}列）:")
            for rank, idx in enumerate(order, 1):
                name = str(feature_names[idx]) if idx < len(feature_names) else f"f{idx}"
                score = float(importances[idx])
                if total > 0 and np.isfinite(total):
                    pct = score / total * 100.0
                    logger.info(f"  {rank:>2d}. {name:<40} 重要性: {score:.6f} ({pct:.2f}%)")
                else:
                    logger.info(f"  {rank:>2d}. {name:<40} 重要性: {score:.6f}")
            logger.info("-" * 50)
        except Exception as ue:
            logger.error(f"本地网 {local_network}: 特征重要性评估环节异常: {ue}")
    return True

def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()

    # 加载配置
    config = load_and_update_config(args.config, args)

    # 设置日志（不再支持命令行覆盖日志级别）
    logger = setup_logging(config)

    # 验证配置
    if not validate_config(config, logger):
        sys.exit(1)

    logger.info("=" * 50)
    logger.info("开始模型训练流程 (单本地网强制模式)")
    logger.info(f"配置文件: {args.config}")
    logger.info(f"数据目录: {config['data']['raw_data_dir']}")
    logger.info(f"输出目录: {config['model']['trained_models_dir']}")
    logger.info("=" * 50)

    # 步骤 1: 数据加载 (内部包含: 确定目标账期 -> 计算所需历史月份 -> 选择文件 -> 加载数据)
    logger.info("步骤 1: 数据加载")
    # 语义调整：命令行传入的账期代表“预测月 (future / 待预测月)”，本次训练使用其前一月作为训练月。
    # 训练样本行: 使用 训练月(M_train) 真实值 + M_train-1, M_train-2 历史特征。
    # 因此需要加载: M_train, M_train-1, M_train-2 共三个月数据；不再加载预测月本身（其还未发生）。
    if args.bill_cycle:
        prediction_month = args.bill_cycle
    else:
        prediction_month = datetime.now().strftime('%Y%m')
    if not (len(prediction_month) == 6 and prediction_month.isdigit()):
        logger.error(f"账期格式错误，应为YYYYMM: {prediction_month}")
        sys.exit(1)

    training_month = DateUtils.shift_year_month_str(prediction_month, -1)  # 当前要训练模型对应的已发生月份
    hist_prev1 = DateUtils.shift_year_month_str(training_month, -1)
    hist_prev2 = DateUtils.shift_year_month_str(training_month, -2)
    required_months = {training_month, hist_prev1, hist_prev2}
    logger.info(
        f"预测目标月: {prediction_month}; 训练月(当前样本月): {training_month}; 加载历史窗口: {sorted(required_months)}"
    )

    # 精准文件选择与加载
    logger.info("数据加载子步骤: 按需选择历史月份文件并加载")
    data_loader = DataLoader(args.config)
    selected_files, missing = data_loader.select_files_for_months(args.latnid, required_months)
    if missing:
        logger.error(f"本地网 {args.latnid} 缺少所需月份文件: {missing}，无法继续训练")
        sys.exit(1)
    if not selected_files:
        logger.error(f"未找到本地网 {args.latnid} 对应月份 {sorted(required_months)} 的任何文件")
        sys.exit(1)
    data_dict = data_loader.load_specific_files_grouped(args.latnid, selected_files)
    if not data_dict:
        logger.error("文件加载结果为空，终止")
        sys.exit(1)
    # 记录各文件行数已由 DataLoader 内日志完成
    # 再次校验每个月份是否都有数据（防止文件存在但空）
    df_total = next(iter(data_dict.values()))
    months_present = set(df_total['file_time'].astype(str).unique()) if not df_total.empty and 'file_time' in df_total.columns else set()
    if not required_months.issubset(months_present):
        logger.error(
            f"存在月份文件空/无有效数据。需要: {sorted(required_months)}; 实际: {sorted(months_present)}。"
            f" 请确认已提供训练月及其前两个月的原始数据 (不需要未来预测月 {prediction_month})"
        )
        sys.exit(1)
    logger.info(f"按需加载完成: 行数 {len(df_total)}; 覆盖月份 {sorted(months_present)} (不含预测月 {prediction_month})")

    # 2. 数据清洗
    logger.info("步骤 2: 数据清洗")
    data_cleaner = DataCleaner(args.config)
    cleaned_data = {}

    # 过滤数据收集结构: {local_network: DataFrame}
    filtered_data_by_net = {}

    for key, df in data_dict.items():
        cleaned_df, cleaning_report, filtered_df = data_cleaner.clean_data(df, collect_filtered=True)
        if not cleaned_df.empty:
            cleaned_data[key] = cleaned_df
            logger.info(f"数据集 {key}: 清洗前 {len(df)} 行，清洗后 {len(cleaned_df)} 行")
        else:
            logger.warning(f"数据集 {key} 清洗后为空，跳过")
        if filtered_df is not None and not filtered_df.empty:
            filtered_data_by_net[key] = filtered_df
            logger.info(f"数据集 {key}: 记录过滤行 {len(filtered_df)} 行 (含原因)")

    if not cleaned_data:
        logger.error("所有数据集清洗后都为空")
        sys.exit(1)

    # 3. 数据验证
    logger.info("步骤 3: 数据验证")
    data_validator = DataValidator(args.config)

    for key, df in cleaned_data.items():
        validation_result = data_validator.validate_all(df)
        if not validation_result['overall_valid']:
            logger.warning(f"数据集 {key} 验证失败: {validation_result['validation_results']}")
        # 收集验证阶段无效行
        invalid_rows = data_validator.identify_invalid_rows(df)
        if invalid_rows is not None and not invalid_rows.empty:
            if key not in filtered_data_by_net:
                filtered_data_by_net[key] = invalid_rows
            else:
                # 合并并避免重复（按 order_id + product_instance_id + remind_time 去重）
                combined = pd.concat([filtered_data_by_net[key], invalid_rows], ignore_index=True)
                key_cols = [c for c in ['order_id','product_instance_id','remind_time'] if c in combined.columns]
                if key_cols:
                    combined = combined.drop_duplicates(subset=key_cols + ['filter_reason'], keep='first')
                filtered_data_by_net[key] = combined
            logger.info(f"数据集 {key}: 验证阶段新增无效标记 {len(invalid_rows)} 行")

    # 输出过滤文件（若配置开启）
    filtered_cfg = config.get('data', {}).get('filtered_output', {})
    if filtered_cfg.get('enabled') and filtered_data_by_net:
        from datetime import datetime as _dt
        output_dir = filtered_cfg.get('output_dir', 'data/processed/filtered')
        FileUtils.ensure_dir(output_dir)
        for local_net, fdf in filtered_data_by_net.items():
            if fdf is None or fdf.empty:
                continue
            # 原始10字段顺序
            base_cols = [
                'order_id','remind_time','product_instance_id','local_network',
                'business_type','volume_type','gear_label','total_volume','trigger_usage','lag_amount'
            ]
            exist_cols = [c for c in base_cols if c in fdf.columns]
            # 构造输出行: 原始字段 | '#' | filter_reason
            out_df = fdf[exist_cols].copy()
            out_df['filter_reason'] = fdf.get('filter_reason', '')
            # 生成时间戳: 优先 file_time，否则当前年月
            timestamp = None
            if 'file_time' in fdf.columns and fdf['file_time'].notna().any():
                # 取最大值（YYYYMM）
                ts_series = fdf['file_time'].astype(str).str.extract(r'(\d{6})', expand=False).dropna()
                if not ts_series.empty:
                    timestamp = ts_series.max()
            if not timestamp:
                timestamp = _dt.now().strftime('%Y%m')
            sequence = '01'  # 简化：单文件输出，后续可根据行数拆分
            filename = f"CC_DAT_AI_TRAIN_FILTER_{local_net}_{timestamp}_{sequence}.txt"
            output_path = os.path.join(output_dir, filename)
            try:
                with open(output_path, 'w', encoding='utf-8') as fw:
                    for _, row in out_df.iterrows():
                        base_part = '|'.join(str(row[col]) if pd.notna(row[col]) else '' for col in exist_cols)
                        reason = row['filter_reason'] if pd.notna(row['filter_reason']) else ''
                        fw.write(f"{base_part}#{reason}\n")
                logger.info(f"过滤数据文件已生成: {output_path} (行数: {len(out_df)})")
            except Exception as fe:
                logger.error(f"写出过滤文件失败 {output_path}: {fe}")

    # 4. 特征工程
    logger.info("步骤 4: 特征工程")

    # 特征提取
    feature_extractor = FeatureExtractor(args.config)
    feature_data = {}

    for key, df in cleaned_data.items():
        # 传入训练月（不是预测月）生成当前月 + 前1/2月特征
        features_df = feature_extractor.extract_all_features(df, training_month=training_month)
        if not features_df.empty:
            feature_data[key] = features_df
            logger.info(f"数据集 {key}: 提取了 {len(features_df.columns)} 个特征")

    # 特征转换 - 每个本地网独立处理
    transformed_data = {}

    # 这里保留一份未缩放的原始特征 (raw_feature_data) 供业务落地或校验使用
    raw_feature_data = feature_data.copy()

    for key, df in feature_data.items():
        # 为每个本地网创建独立的转换器
        feature_transformer = FeatureTransformer(args.config)
        transformed_df = feature_transformer.fit_transform(df)
        transformed_data[key] = transformed_df
        logger.info(f"数据集 {key}: 特征转换完成 (列数: {len(transformed_df.columns)}) — 已禁用转换器持久化")

    # 特征选择 - 暂跳过（可在 ModelTrainer 内实现）
    final_data = transformed_data

    for key, df in final_data.items():
        logger.info(f"数据集 {key}: 准备了 {len(df.columns)} 个特征用于训练")

    # 保存特征工程结果
    features_dir = config.get('data', {}).get('processed_data_dir', 'data/processed')
    FileUtils.ensure_dir(features_dir)

    # 业务特征文件：使用原始特征 -> 再抽取业务标准列（不含扩展统计 & 交互）
    for key, df in raw_feature_data.items():
        if df is None or df.empty:
            logger.warning(f"本地网 {key} 特征数据为空，跳过保存")
            continue
        # 特征文件账期使用训练月 (窗口内最新已发生月份)
        timestamp = training_month
        try:
            if hasattr(feature_extractor, 'export_business_features'):
                logger.info("调用 export_business_features 生成业务标准特征文件")
                biz_df = feature_extractor.export_business_features(df, ensure_raw=True)
            else:
                logger.warning("FeatureExtractor 缺少 export_business_features 方法 (可能是旧代码缓存)，回退直接使用原始特征列保存")
                biz_df = df
            feature_extractor.split_large_file(biz_df, key, features_dir, timestamp)
            logger.info(f"本地网 {key} 的业务标准特征数据已保存 -> 目录: {features_dir}")
        except Exception as fe:
            logger.error(f"本地网 {key} 特征文件保存失败: {fe}")

    # 5. 模型训练
    logger.info("步骤 5: 模型训练")

    model_trainer = ModelTrainer(args.config)

    # 训练配置（显式关闭并行）
    training_config = {
        'algorithm': config.get('model', {}).get('algorithm'),
        'enable_parallel': False
    }

    # 单本地网数据字典
    local_network_data = {args.latnid: final_data[args.latnid]}
    # 传递 training_month 给 batch 训练（用于模型文件命名/元数据）
    try:
        results = model_trainer.batch_train_by_local_network(
            local_network_data, training_timestamp=training_month, **training_config
        )
    except TypeError:
        # 兼容旧版本ModelTrainer未实现 training_timestamp 参数
        logger.warning("ModelTrainer.batch_train_by_local_network 未接受 training_timestamp, 回退为当前时间模型命名")
        results = model_trainer.batch_train_by_local_network(
            local_network_data, **training_config
        )

    # 6. 结果汇总
    logger.info("步骤 6: 训练结果汇总")

    successful_models = 0
    failed_models = 0

    for local_network, result in results.items():
        if result.get('success', False):
            successful_models += 1
            logger.info(f"本地网 {local_network}: 训练成功")
            logger.info(f"  - 模型文件: {result.get('model_path', 'N/A')}")
            # 增补模型元数据: 写入 feature_names（已移除 transformer 持久化）
            try:
                model_path = result.get('model_path')
                if model_path:
                    model_filename = os.path.basename(model_path)
                    metadata_filename = model_filename.replace('.pkl', '_metadata.json')
                    metadata_dir = config.get('model', {}).get('metadata_dir', 'models/metadata')
                    metadata_path = os.path.join(metadata_dir, metadata_filename)
                    if os.path.exists(metadata_path):
                        import json
                        with open(metadata_path, 'r', encoding='utf-8') as mf:
                            meta_json = json.load(mf)
                        # 仅当不存在时写入，避免重复覆盖 (但允许补齐)
                        # 仅写入 feature_names
                        if 'feature_names' not in meta_json:
                            meta_json['feature_names'] = result.get('feature_names', [])
                        with open(metadata_path, 'w', encoding='utf-8') as mf:
                            json.dump(meta_json, mf, ensure_ascii=False, indent=2)
                        logger.info(f"  - 已更新元数据: 写入 feature_names({len(result.get('feature_names', []))})")
                    else:
                        logger.warning(f"  - 元数据文件缺失，无法更新特征列表: {metadata_path}")
            except Exception as me:
                logger.error(f"  - 更新模型元数据失败: {me}")

            # 获取测试指标
            test_metrics = result.get('test_metrics', {})
            if test_metrics and 'rmse' in test_metrics:
                logger.info(f"  - 测试RMSE: {test_metrics['rmse']:.4f}")
            if test_metrics and 'r2' in test_metrics:
                logger.info(f"  - R²得分: {test_metrics['r2']:.4f}")

            # 获取交叉验证结果
            cv_results = result.get('cv_results', {})
            if cv_results and 'cv_rmse_mean' in cv_results:
                logger.info(f"  - CV RMSE均值: {cv_results['cv_rmse_mean']:.4f}")

        else:
            failed_models += 1
            logger.error(f"本地网 {local_network}: 训练失败 - {result.get('error', 'Unknown error')}")

    logger.info("=" * 50)
    logger.info("训练完成汇总:")
    logger.info(f"成功训练模型: {successful_models}")
    logger.info(f"训练失败模型: {failed_models}")
    logger.info(f"总模型数: {len(results)}")

    if successful_models > 0:
        logger.info(f"模型保存目录: {config['model']['trained_models_dir']}")
        logger.info(f"元数据保存目录: {config['model']['metadata_dir']}")

    # 7. 特征重要性评估
    logger.info("步骤 7: 特征重要性评估")
    try:
        evaluate_and_log_feature_importance(results, logger)
    except Exception as fe:
        logger.error(f"特征重要性评估阶段异常: {fe}")

    logger.info("=" * 50)

    # 返回合适的退出码
    sys.exit(0 if failed_models == 0 else 1)


if __name__ == "__main__":
    main()