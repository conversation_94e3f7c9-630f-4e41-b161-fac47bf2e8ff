# 滞后量预测模型配置文件

# 数据配置 - 控制数据加载、处理和存储路径
data:
  # 原始数据目录 - 存放训练数据文件
  raw_data_dir: "data/raw"
  
  # 处理后数据目录 - 存放特征工程后的数据
  processed_data_dir: "data/processed"
  
  # 预测结果目录 - 存放模型预测输出
  predictions_dir: "data/predictions"
  
  # 训练数据文件名模式
  train_file_pattern: "CC_DAT_AI_TRAIN_*_*.txt"
  
  # 过滤数据输出配置
  # enabled: 是否输出被清洗/验证阶段过滤掉的原始记录
  # output_dir: 过滤文件输出目录（会自动创建）
  filtered_output:
    enabled: true
    output_dir: "data/processed/filtered"
  
  # 数据分割配置
  train_test_split: 0.8  # 80%训练，20%测试

# 模型配置 - 控制机器学习模型的训练、保存和参数
model:
  # 模型文件存储目录
  trained_models_dir: "models/trained"
  
  # 模型元数据目录
  metadata_dir: "models/metadata"
  
  # 主要算法类型 - 支持以下3种算法，可根据实际需求选择：
  # 1. "random_forest" - 随机森林算法（默认推荐）
  #    特点：训练稳定、抗过拟合、可解释性好、对参数不敏感
  #    适用：数据量较小、要求稳定性、需要特征重要性分析
  # 2. "xgboost" - XGBoost梯度提升算法
  #    特点：准确性高、支持缺失值、内置正则化、功能强大
  #    适用：追求最高准确性、数据质量较好、有充足调参时间
  # 3. "lightgbm" - LightGBM轻量级梯度提升算法
  #    特点：训练速度快、内存占用低、支持大数据、易于调参
  #    适用：大数据量、生产环境、要求训练速度、内存有限
  algorithm: "lightgbm"
  
  # 随机森林算法参数
  random_forest:
    n_estimators: 100      # 决策树数量
    max_depth: 10         # 最大深度
    min_samples_split: 5  # 分割所需最小样本数
    min_samples_leaf: 2   # 叶节点最小样本数
    random_state: 42      # 随机种子，确保结果可重复
    n_jobs: -1           # 并行作业数，-1表示使用全部CPU核心
  
  # XGBoost算法参数
  xgboost:
    n_estimators: 100     # 提升轮数
    max_depth: 6         # 树的最大深度
    learning_rate: 0.1   # 学习率
    subsample: 0.8       # 样本采样比例
    colsample_bytree: 0.8 # 特征采样比例
    random_state: 42     # 随机种子
  
  # LightGBM算法参数
  lightgbm:
    n_estimators: 100     # 提升轮数
    max_depth: 6         # 树的最大深度  
    learning_rate: 0.1   # 学习率
    num_leaves: 31       # 叶子节点数量
    subsample: 0.8       # 样本采样比例
    colsample_bytree: 0.8 # 特征采样比例
    random_state: 42     # 随机种子
    verbose: -1          # 控制输出详细程度，-1表示静默模式
  
  # 交叉验证折数 - 用于模型性能评估和超参数调优
  # 作用：将训练数据分成N份，轮流用N-1份训练、1份验证，获得更可靠的性能评估
  # 3折：快速验证，适合小数据量
  # 5折：标准配置，平衡准确性和速度（推荐）
  # 10折：高精度评估，适合充足数据量
  cross_validation_folds: 5
  
  # 超参数调优开关 - 是否自动寻找最优模型参数
  # true：使用网格搜索自动调优参数，提升模型性能，但训练时间显著增加
  # false：使用上述预设参数直接训练，训练速度快，适合快速验证
  # 建议：重要模型开启，实验性模型关闭
  hyperparameter_tuning: true
  
  # 超参数优化方法选择
  # "grid_search" - 网格搜索（默认）：穷举所有参数组合，准确但较慢
  # "bayesian" - 贝叶斯优化：智能搜索，支持实时进度显示，通常更快找到好参数
  optimization_method: "bayesian"  # 使用贝叶斯优化获得真实进度反馈
  
  # 贝叶斯优化参数（仅在optimization_method为"bayesian"时有效）
  bayesian_n_calls: 50  # 贝叶斯优化评估次数，相当于网格搜索的参数组合数
  
  # 训练超时配置 - 防止训练时间过长
  max_training_time_minutes: 30  # 单个本地网最大训练时间（分钟）
  
  # 目标类型: lag_amount (默认) 或 lag_percentage
  target_type: "lag_amount"
  
  # 是否启用预测结果校准 (仅在 target_type=lag_percentage 时有意义)
  enable_calibration: false
  calibration_method: "isotonic"  # 预留: isotonic / platt

# 特征工程配置 - 控制特征提取、转换和选择过程
feature_engineering:
  # 档位标签配置
  # 标准档位值，用于数据验证和特征工程
  gear_labels: [0.2, 0.4, 0.6, 0.8, 1.0]
  
  # 时序特征配置
  # 定义用于生成历史特征的时间窗口大小
  time_window_months: 3  # 使用过去3个月数据
  
  # 特征变换配置
  feature_transformation:
    # 分类特征编码方式
    # "label" - 标签编码（内存友好，适合高基数特征）
    # "onehot" - 独热编码（适合低基数特征，但消耗内存）
    categorical_encoding: "label"  # 默认使用标签编码避免内存问题
    
    # One-hot编码最大类别数限制
    # 超过此限制的分类特征将被跳过或使用标签编码
    max_onehot_categories: 20  # 降低限制避免内存溢出
    
    # 缺失值处理策略
    missing_value_strategy: "median"  # 数值特征：median, mean; 分类特征：mode

# 业务规则配置 - 核心业务逻辑约束和过滤条件
business_rules:
  # 滞后量预测边界约束
  # 最小滞后量百分比
  min_lag_percentage: 0.5  # 最小滞后量0.5%
  
  # 最大滞后量百分比
  max_lag_percentage: 3.0  # 最大滞后量3%
  
  # 风险系数阈值
  risk_threshold: 0.5      # 风险系数阈值
  
  # 数据过滤规则
  # 过滤档位标签配置
  # 档位0和1的数据不适用于滞后量预测
  filter_gear_labels: [0, 1]  # 过滤档位标签为0和1的数据
  
  # 扩展软过滤配置: 在 soft 模式下不直接丢弃落在核心范围外但在扩展范围内的样本, 赋予较低权重
  filtering_mode: "hard"  # 可选: hard / soft
  extended_min_lag_percentage: 0.3
  extended_max_lag_percentage: 4.0
  core_weight: 2.0
  extended_weight: 1.0

# 处理配置 - 控制并发处理和性能优化
processing:
  # 最大工作线程数
  max_workers: 4
  
  # 数据块大小
  chunk_size: 10000

# 日志配置 - 控制系统日志记录和输出
logging:
  # 日志级别 
  # 0=NONE(无日志), 1=FATAL(严重错误), 2=ERROR(一般错误), 3=WARN(警告)
  # 4=INFO(信息), 5=DVIEW(简略调试), 6=DEBUG(一般调试), 7=TRACE(详尽调试)
  level: 4  # 默认使用INFO级别(4)
  
  # 日志目录
  log_dir: "logs"

