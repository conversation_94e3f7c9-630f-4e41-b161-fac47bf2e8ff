# 滞后量预测模型 - AI 开发速览指令 (面向代码智能体)

面向本仓库的最小且关键知识，帮助智能体在 1 分钟内掌握架构/约束并安全产出代码。当前内容基于真实源码 (`data_loader.py`, `trainer.py`, `config.yaml`) 而非旧文档假设。

## 1. 业务核心与目标变量
- 标签原始列: `lag_amount` (可为负，不在训练阶段截断)。
- 训练流程派生 `lag_percentage = lag_amount/total_volume*100` 并默认用其建模+Isotonic 校准，保存模型可能是包装 dict `{base_model, calibrator, target_type}`。
- 预测阶段再将预测值按总量裁剪到 0.5%~3%（`business_rules.min_lag_percentage/max_lag_percentage`）。

## 2. 文件/字段规范
| 类型 | 命名 | 说明 |
| ---- | ---- | ---- |
| 原始 | `CC_DAT_AI_TRAIN_{LATNID}_{YYYYMM}_{SEQ}.txt` | 9或10列；缺 `lag_amount` 时需按公式重算并允许负值 |
| 特征 | `CC_DAT_AI_TRAIN_OUT_{LATNID}_{YYYYMM}_{SEQ}.txt` | 固定16列 (当前+前1/前2月三期聚合) |
| 预测 | `CC_DAT_AI_PRE_OUT_{LATNID}_{YYYYMM}.txt` | 13列 + 预测值；追加百分比在后处理层可选 |
| 模型 | `CC_DAT_AI_TRAIN_OUT_{LATNID}_{YYYYMM}.pkl` | 同名 `_metadata.json` 记录 target_type/feature_names |
约束：UTF-8、`|` 分隔；档位过滤掉 0/1；合法档位集合 `[0.2,0.4,0.6,0.8]` (配置含1.0但仍过滤 0/1)。

## 3. 分层与并行
数据流: raw → (`data_processor/*`) 清洗/验证 → (`feature_engineer/*`) 构造16列 → (`model/trainer.py`) 训练/调优/校准 → (`model/predictor.py`) 预测+裁剪 → 输出。当前精简版：每次仅处理单个本地网；`batch_train_by_local_network` 现在只是单网包装（若传入多键仅取第一个）。贝叶斯调优仍保持单行进度输出。

## 4. 训练 vs 预测 差异
| 维度 | 训练 | 预测 |
| ---- | ---- | ---- |
| 月份窗口 | 目标月-1 为训练月；需训练月+前1月+前2月 | 目标月仅用前1月+前2月历史构造 |
| 目标 | lag_percentage | 使用历史特征推断未来月 lag_amount (再裁剪) |
| 截断 | 不截断标签 | 裁剪预测值 0.5%-3% * total_volume |
| 超参 | 可启用 (`model.hyperparameter_tuning`) | 不调优 |
| 并行 | 可批量/并行本地网 | 单本地网一次 |

## 5. 允许的 CLI 参数 (避免幻觉新增)
训练: `--latnid` `--bill-cycle` `--config`
预测: `--latnid` `--prediction-month` `--config`
其他旧参数（如 `--enable-tuning`, `--data-dir`, `--output-dir` 等）均已从脚本删除，调优/目录统一走配置。

## 6. 配置关键键
`model.algorithm` (默认 lightgbm) | `model.hyperparameter_tuning` | `model.optimization_method` (bayesian/grid_search) | `model.bayesian_n_calls` | `model.cross_validation_folds` | `business_rules.min_lag_percentage/max_lag_percentage`。并发控制已在精简版移除，不再使用 `processing.max_workers`。

## 7. 进度与并发约束
贝叶斯优化使用内部 `_fit_with_progress_monitoring`：单行回车刷新（`\r`），完成后换行；多线程时每本地网独立行。保持此行为，勿改成多行 print 或频繁 logger 输出。

## 8. 模型保存与元数据
模型文件命名固定；保存后自动写 `_metadata.json` 并追加 `target_type`, `target_column`, `feature_names`, 可含 `clip_range`。修改训练逻辑时务必同步元数据更新，新增特征后写回列表。

## 9. 业务裁剪位置
仅 `predictor` 后处理：检测 `total_volume` 存在再裁剪；缺失则 WARNING 跳过。不要在训练或特征阶段提前应用边界。

## 10. 常见错误防护
- 误删除负值 lag_amount 或将其强制为0 → 禁止。
- 在训练阶段裁剪到 0.5%~3% → 会导致分布收缩，禁止。
- 生成非规范文件名 / 乱序字段 → 预测脚本会解析失败。
- 假设 joblib.load 一定返回 estimator → 需要判断是否 dict 包含 calibrator。
- 并行调优使用共享 tqdm → 造成输出交错，应维持当前单行机制。

## 11. 快速符合性校验 (提交前自查)
1) 新增参数是否已写入 config.yaml 且被读取
2) 预测仍保持裁剪逻辑未移除
3) 新特征不破坏原 16 列顺序（追加需放末尾并更新元数据）
4) 没有新增废弃 CLI 参数引用
5) （精简版）未重新引入已移除的并发配置节

## 12. 最小示例
```python
from src.data_processor.data_loader import DataLoader
from src.model.trainer import ModelTrainer
loader = DataLoader('config/config.yaml')
files, _ = loader.select_files_for_months('912', {'202506','202507','202508'})
group = loader.load_specific_files_grouped('912', files)
trainer = ModelTrainer('config/config.yaml')
result = trainer.batch_train_by_local_network({latnid: group[latnid]}, training_timestamp='202508')
```

## 13. 扩展策略
优先：复用批处理/工厂模式 → 配置扩展 → 最后才改脚本入口。保持脚本参数最小、业务开关集中配置。

—— 完 ——