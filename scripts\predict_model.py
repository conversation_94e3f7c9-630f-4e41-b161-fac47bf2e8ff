"""
模型预测脚本

用于使用训练好的模型进行预测
"""

import os
import sys
import argparse
import yaml
import logging
import numpy as np
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data_processor import DataLoader, DataCleaner, DataValidator
from src.feature_engineer import FeatureExtractor, FeatureTransformer, FeatureSelector
from src.model import ModelPredictor
from src.utils import setup_project_logging, get_module_logger, DateUtils, FileUtils, init_logging_system

def parse_arguments():
    """解析命令行参数
    必填：
        --config 配置文件路径
        --latnid 本地网编码（仅预测该本地网）
    可选：
        --prediction-month 预测目标月（缺省=当前系统月）
    当前仅保留以上两个核心必需+一个可选参数；不再支持 log-level 命令行参数。
    """
    parser = argparse.ArgumentParser(description='使用训练好的模型进行未来月滞后量预测 (单本地网)')
    parser.add_argument('--config', '-c', type=str, default='config/config.yaml', help='配置文件路径')
    parser.add_argument('--latnid', '-n', type=str, required=True, help='必填，本地网编码，例如 912/888/919')
    parser.add_argument('--prediction-month', '-p', type=str, help='预测目标月 YYYYMM，缺省为当前系统月')
    return parser.parse_args()

def load_config(config_path: str) -> dict:
    """加载配置（不再支持命令行覆盖，全部依赖文件）"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        sys.exit(1)

def setup_logging(config: dict, log_level):
    """设置统一日志（读取配置的 log_dir 与 level；支持数字0-7或字符串级别）"""
    log_dir = config.get('logging', {}).get('log_dir', 'logs')
    # 兼容数字/字符串级别
    try:
        from src.utils.logger import get_logging_level
        console_level = get_logging_level(log_level)
    except Exception:
        console_level = 4
    file_level = 6  # 文件保持DEBUG
    init_logging_system(log_dir, console_level, file_level)
    return setup_project_logging('model_prediction', log_dir)

def validate_config(config: dict, logger):
    """验证配置"""
    required_sections = ['data', 'model', 'feature_engineering']
    
    for section in required_sections:
        if section not in config:
            logger.error(f"配置文件缺少必需的部分: {section}")
            return False
    
    # 验证模型目录
    model_dir = config['model']['trained_models_dir']
    if not os.path.exists(model_dir):
        logger.error(f"模型目录不存在: {model_dir}")
        return False
    
    return True

def process_single_file(file_path: str, config: dict, logger):
    """处理单个文件"""
    logger.info(f"处理单个文件: {file_path}")
    
    # 数据加载 - 传递配置文件路径
    data_loader = DataLoader(config_path=config.get('_config_path', 'config/config.yaml'))
    try:
        df = data_loader.load_single_file(file_path)
        if df.empty:
            logger.error(f"文件为空或格式不正确: {file_path}")
            return None
    except Exception as e:
        logger.error(f"加载文件失败 {file_path}: {e}")
        return None
    
    # 提取本地网信息
    local_network = df['local_network'].iloc[0] if 'local_network' in df.columns else 'unknown'
    
    return {local_network: df}

def process_directory_data(data_dir: str, local_network_filter: str, config: dict, logger):
    """处理目录数据"""
    logger.info(f"处理目录数据: {data_dir}")
    
    # 数据加载 - 传递配置文件路径而不是配置字典
    data_loader = DataLoader(config_path=config.get('_config_path', 'config/config.yaml'))
    
    if local_network_filter:
        # 只加载指定本地网的数据
        data_dict = data_loader.load_data_by_local_network([local_network_filter], data_dir)
        if not data_dict:
            logger.error(f"未找到本地网 {local_network_filter} 的数据")
            return None
    else:
        # 加载所有数据 - 使用正确的方法
        data_dict = data_loader.load_data_by_local_network(local_networks=None, data_dir=data_dir)
        if not data_dict:
            logger.error("未找到任何数据文件")
            return None
    
    return data_dict

def perform_feature_engineering(data_dict: dict, config: dict, logger, prediction_month: str):
    """执行特征工程 (未来预测模式)

    使用历史已发生数据构造 prediction_month 的未来样本：仅前1/2月特征。
    当前月 total/trigger/lag 置 0。
    """
    logger.info("执行特征工程 (未来预测月样本构造)")
    
    # 保存原始用户ID映射
    original_user_mappings = {}
    
    # 数据清洗 - 传递配置文件路径
    data_cleaner = DataCleaner(config_path=config.get('_config_path', 'config/config.yaml'))
    cleaned_data = {}
    
    # 严格历史数据存在性校验：prediction_month 前1/2月原始文件必须存在
    raw_dir = config.get('data', {}).get('raw_data_dir', 'data/raw')
    prev1 = DateUtils.shift_year_month_str(prediction_month, -1)
    prev2 = DateUtils.shift_year_month_str(prediction_month, -2)
    required_months = [prev1, prev2]

    for key, df in data_dict.items():
        # key 为本地网编码 (来自 DataLoader.load_data_by_local_network)
        missing_months = []
        for ym in required_months:
            # 训练原始文件命名规范: CC_DAT_AI_TRAIN_{本地网}_{YYYYMM}_序号.txt (序号三位或两位，已有样例 01)
            pattern_candidates = [
                f"CC_DAT_AI_TRAIN_{key}_{ym}_01.txt",
                f"CC_DAT_AI_TRAIN_{key}_{ym}_001.txt"
            ]
            found = any(os.path.exists(os.path.join(raw_dir, cand)) for cand in pattern_candidates)
            if not found:
                missing_months.append(ym)
        if missing_months:
            # 直接抛出异常，阻断预测
            raise FileNotFoundError(
                f"本地网 {key}: 预测 {prediction_month} 需要前两个月数据文件缺失: {missing_months} (目录: {raw_dir})"
            )
        # 保存原始用户ID
        if 'product_instance_id' in df.columns:
            original_user_mappings[key] = df['product_instance_id'].tolist()
        
        # 数据清洗
        cleaning_result = data_cleaner.clean_data(df)
        # 标准返回: (cleaned_df, report, filtered_df_optional)
        if isinstance(cleaning_result, tuple):
            if len(cleaning_result) == 3:
                cleaned_df, cleaning_report, _filtered_df = cleaning_result
            elif len(cleaning_result) == 2:  # 兼容旧实现 (cleaned_df, report)
                cleaned_df, cleaning_report = cleaning_result
            else:  # 其它情况只取第一项作为清洗结果
                cleaned_df = cleaning_result[0]
        else:
            cleaned_df = cleaning_result
            
        if not cleaned_df.empty:
            cleaned_data[key] = cleaned_df
            logger.info(f"数据集 {key}: 清洗前 {len(df)} 行，清洗后 {len(cleaned_df)} 行")
        else:
            logger.warning(f"数据集 {key} 清洗后为空，跳过")
    
    if not cleaned_data:
        logger.error("所有数据集清洗后都为空")
        return None
    
    # 数据验证 - 传递配置文件路径
    data_validator = DataValidator(config_path=config.get('_config_path', 'config/config.yaml'))
    for key, df in cleaned_data.items():
        validation_result = data_validator.validate_all(df)
        if not validation_result.get('is_valid', True):
            logger.warning(f"数据集 {key} 验证失败: {validation_result.get('issues', [])}")
    
    # 特征提取 - 传递配置文件路径
    feature_extractor = FeatureExtractor(config_path=config.get('_config_path', 'config/config.yaml'))
    feature_data = {}
    for key, df in cleaned_data.items():
        features_df = feature_extractor.extract_all_features(df, training_month=prediction_month, mode='future')
        if not features_df.empty:
            # 过滤档位 0 / 1
            if 'gear_label' in features_df.columns:
                before = len(features_df)
                features_df = features_df[~features_df['gear_label'].isin([0,1,'0','1'])]
                removed = before - len(features_df)
                if removed > 0:
                    logger.info(f"数据集 {key}: 过滤无效档位(0/1) {removed} 行")
            feature_data[key] = features_df
            logger.info(f"数据集 {key}: 未来月样本构造 {len(features_df)} 行")
    
    # 简化：直接在线拟合转换（未来月数据仅用于预测，不持久化）
    logger.info("步骤 6: 在线特征转换 (本地即席 fit_transform，已去除持久化)")
    transformed_data = {}
    raw_business_snapshot = {}
    for key, df in feature_data.items():
        feature_transformer = FeatureTransformer(config_path=config.get('_config_path', 'config/config.yaml'))
        try:
            # 在进入变换前保存业务原始列快照
            raw_cols = [
                'user','year','month','local_network','business_type','volume_type','gear_label',
                'total_volume','trigger_usage','lag_amount',
                'prev_1m_total_volume','prev_1m_trigger_usage','prev_1m_lag_amount',
                'prev_2m_total_volume','prev_2m_trigger_usage','prev_2m_lag_amount'
            ]
            raw_business_snapshot[key] = df[[c for c in raw_cols if c in df.columns]].copy()
            transformed_df = feature_transformer.fit_transform(df, create_interactions=True)
        except Exception as te:
            logger.error(f"本地网 {key}: 在线特征转换失败: {te}")
            continue
        # 不再兼容 lag_ 前缀，要求上游统一使用 prev_*
        transformed_data[key] = transformed_df
        logger.info(f"数据集 {key}: 在线转换完成 特征数 {len(transformed_df.columns)}")

    if not transformed_data:
        logger.error("转换后没有可预测的数据集 (在线转换阶段全失败)")
        return None, original_user_mappings, raw_business_snapshot

    return transformed_data, original_user_mappings, raw_business_snapshot

def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()
    
    # 加载配置（不再做命令行覆盖）
    config = load_config(args.config)
    
    # 设置日志：移除命令行 log-level，改为读取配置或默认 INFO
    cfg_log_level = (config.get('logging', {}) or {}).get('level', 4)
    logger = setup_logging(config, cfg_log_level)

    # 记录配置文件路径供后续下游组件使用
    config['_config_path'] = args.config

    # 验证配置
    if not validate_config(config, logger):
        sys.exit(1)
    
    # 应用业务约束固定为 True（如需关闭，通过配置文件实现）
    apply_constraints = True
    
    logger.info("=" * 50)
    logger.info("开始模型预测流程")
    logger.info(f"配置文件: {args.config}")
    logger.info(f"模型目录: {config['model']['trained_models_dir']}")
    logger.info(f"应用业务约束: {apply_constraints} (固定启用，可由配置控制)")
    logger.info("=" * 50)

    # 记录目标类型与校准配置（以配置为准，提供可观测性）
    mcfg = config.get('model', {}) or {}
    target_type = str(mcfg.get('target_type', 'lag_amount'))
    enable_calibration = bool(mcfg.get('enable_calibration', False))
    logger.info(f"目标类型: {target_type}; 启用校准: {enable_calibration}")
    
    try:
        # 1. 数据处理
        logger.info("步骤 1: 数据加载和处理")

        data_dir = config['data']['raw_data_dir']
        if not os.path.exists(data_dir):
            logger.error(f"数据目录不存在: {data_dir}")
            sys.exit(1)
        data_dict = process_directory_data(data_dir, args.latnid, config, logger)
        
        if not data_dict:
            logger.error("未能加载任何有效数据")
            sys.exit(1)
        
        logger.info(f"成功加载 {len(data_dict)} 个数据集")
        
        # 2. 特征工程
        logger.info("步骤 2: 特征工程 (含在线转换)")
        # 确定预测目标月 (未来月)
        if args.prediction_month:
            prediction_month = args.prediction_month
        else:
            prediction_month = datetime.now().strftime('%Y%m')
        if not (len(prediction_month) == 6 and prediction_month.isdigit()):
            logger.error(f"prediction_month 格式错误: {prediction_month}")
            sys.exit(1)
        logger.info(f"未来预测目标月: {prediction_month}")

        try:
            processed_data, user_mappings, raw_business_snapshot = perform_feature_engineering(data_dict, config, logger, prediction_month)
        except FileNotFoundError as fe:
            logger.error(str(fe))
            logger.error("历史数据不足，终止预测。请补齐缺失月份原始文件后重试。")
            sys.exit(1)
        except Exception as e:
            logger.error(f"特征工程阶段异常: {e}")
            sys.exit(1)
        
        if not processed_data:
            logger.error("特征工程处理失败")
            sys.exit(1)
        
        # 保存中间结果
        # 可选中间结果保存由配置控制（如需要可在此读取 config['feature_engineering'].get('save_intermediate')）
        if config.get('feature_engineering', {}).get('save_intermediate_prediction', False):
            intermediate_dir = config.get('data', {}).get('processed_data_dir', 'data/processed')
            FileUtils.ensure_dir(intermediate_dir)
            timestamp = prediction_month
            for key, df in processed_data.items():
                out_path = os.path.join(intermediate_dir, f"prediction_features_{key}_{timestamp}.csv")
                df.to_csv(out_path, index=False, encoding='utf-8')
                logger.info(f"中间结果已保存: {out_path}")
        
        # 3. 模型预测
        logger.info("步骤 3: 模型预测")
        predictor = ModelPredictor(args.config)
        
        # 检查可用模型
        available_models = predictor.list_available_models()
        if not available_models:
            logger.error("未找到任何可用的模型")
            sys.exit(1)
        
        logger.info(f"找到 {len(available_models)} 个可用模型")
        
        # 执行预测：单本地网模式（latnid 为必填）
        try:
            if args.latnid not in processed_data:
                logger.error(f"指定本地网 {args.latnid} 不在处理后的数据集中")
                sys.exit(1)
            target_data = {args.latnid: processed_data[args.latnid]}
            # 预测前过滤无效档位 (再次兜底)
            pdf = target_data[args.latnid]
            if 'gear_label' in pdf.columns:
                before = len(pdf)
                pdf = pdf[~pdf['gear_label'].isin([0,1,'0','1'])]
                if len(pdf) != before:
                    logger.info(f"本地网 {args.latnid}: 二次过滤无效档位 {before-len(pdf)} 行")
                target_data[args.latnid] = pdf
            # 添加 current_total_volume fallback
            df = target_data[args.latnid]
            if 'current_total_volume' not in df.columns:
                ref_col = None
                for candidate in ['prev_1m_total_volume','total_volume']:
                    if candidate in df.columns:
                        ref_col = candidate; break
                if ref_col:
                    df['current_total_volume'] = df[ref_col]
                else:
                    logger.warning(f"本地网 {args.latnid}: 未找到可作为业务规则分母的列，跳过约束")
            predictions = predictor.predict_by_local_network(target_data, apply_constraints)
            # 恢复业务原始列（覆盖缩放后列）
            if args.latnid in predictions and args.latnid in raw_business_snapshot:
                pred_df = predictions[args.latnid]
                raw_df = raw_business_snapshot[args.latnid]
                for col in raw_df.columns:
                    if col in pred_df.columns:
                        pred_df[col] = raw_df[col].values[:len(pred_df)]
                # 重新计算 predicted_lag_percentage 如可能
                if 'predicted_lag_amount' in pred_df.columns and 'total_volume' in pred_df.columns:
                    with np.errstate(divide='ignore', invalid='ignore'):
                        pred_df['predicted_lag_percentage'] = (
                            pred_df['predicted_lag_amount'] / pred_df['total_volume'] * 100
                        )
                # 应用边界裁剪 (如果未在模型级完成)
                if 'predicted_lag_amount' in pred_df.columns and 'total_volume' in pred_df.columns:
                    vols = pred_df['total_volume'].replace(0, np.nan).values
                    adjusted = predictor.apply_business_rules(pred_df['predicted_lag_amount'].values, vols)
                    pred_df['predicted_lag_amount'] = adjusted
                    if 'predicted_lag_percentage' in pred_df.columns:
                        pred_df['predicted_lag_percentage'] = (
                            pred_df['predicted_lag_amount'] / pred_df['total_volume'] * 100
                        )
                predictions[args.latnid] = pred_df
        except Exception as e:
            logger.error(f"预测失败: {e}")
            sys.exit(1)
        
        # 4. 保存预测结果
        logger.info("步骤 4: 保存预测结果")
        
        # 批量输出 (单文件模式已删除，统一由 save_predictions 处理)
        timestamp = DateUtils.get_current_timestamp()
        required_output_cols = [
            'prev_1m_total_volume','prev_1m_trigger_usage','prev_1m_lag_amount',
            'prev_2m_total_volume','prev_2m_trigger_usage','prev_2m_lag_amount'
        ]
        for lk, pdf in predictions.items():
            missing = [c for c in required_output_cols if c not in pdf.columns]
            if missing:
                logger.warning(f"本地网 {lk}: 预测结果缺失标准字段 {missing} (可能历史不足或命名未统一)")
        saved_files = predictor.save_predictions(predictions, prediction_month, user_mappings)
        logger.info(f"预测结果文件: {saved_files.get(args.latnid, '未找到输出')}  (单本地网模式)")
        
        # 5. 生成预测摘要
        if config.get('prediction', {}).get('generate_summary', True):
            logger.info("步骤 5: 生成预测摘要")
            summary = predictor.generate_prediction_summary(predictions)
            summary_dir = config.get('data', {}).get('predictions_dir', 'data/predictions')
            summary_file = os.path.join(summary_dir, f"prediction_summary_{DateUtils.get_current_timestamp()}.json")
            if FileUtils.save_json(summary, summary_file):
                logger.info(f"预测摘要已保存: {summary_file}")
            logger.info("=" * 30)
            logger.info("预测摘要:")
            logger.info(f"处理数据集数量: {summary['total_datasets']}")
            logger.info(f"总样本数: {summary.get('overall_statistics', {}).get('total_samples', 0)}")
            overall_stats = summary.get('overall_statistics', {})
            if overall_stats:
                logger.info(f"预测平均值: {overall_stats.get('overall_prediction_mean', 0):.4f}")
                logger.info(f"预测标准差: {overall_stats.get('overall_prediction_std', 0):.4f}")
                logger.info(f"预测最小值: {overall_stats.get('overall_prediction_min', 0):.4f}")
                logger.info(f"预测最大值: {overall_stats.get('overall_prediction_max', 0):.4f}")
            logger.info("=" * 30)
        
        # 6. 结果统计
        logger.info("步骤 6: 预测结果统计")
        
        successful_predictions = 0
        failed_predictions = 0
        total_samples = 0
        
        for data_key, prediction_df in predictions.items():
            if not prediction_df.empty:
                successful_predictions += 1
                samples = len(prediction_df)
                total_samples += samples
                logger.info(f"数据集 {data_key}: 成功预测 {samples} 个样本")
            else:
                failed_predictions += 1
                logger.warning(f"数据集 {data_key}: 预测失败或结果为空")
        
        logger.info("=" * 50)
        logger.info("预测完成汇总:")
        logger.info(f"成功预测本地网数: {successful_predictions}")
        logger.info(f"失败预测本地网数: {failed_predictions}")
        logger.info(f"总预测样本数: {total_samples}")
        
        if successful_predictions > 0:
            output_dir = config.get('data', {}).get('predictions_dir', 'data/predictions')
            logger.info(f"预测结果保存目录: {output_dir}")
        
        logger.info("=" * 50)
        
        # 返回合适的退出码
        sys.exit(0 if failed_predictions == 0 else 1)
        
    except KeyboardInterrupt:
        logger.info("预测被用户中断")
        sys.exit(130)
    
    except Exception as e:
        logger.error(f"预测过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()